<?php

use App\Enum\ImageSources;
use App\Http\Controllers\AIImageGeneratorController;
use App\Http\Controllers\BulkDownloadController;
use App\Http\Controllers\ChapterAudioController;
use App\Http\Controllers\PublicShareController;
use App\Models\Campaign;
use App\Service\GenerateEbookAsPDF;
use App\Service\ImageGenerateFactory;
use App\Service\MediumService;
use App\Service\YoutubeClient;
use Illuminate\Support\Facades\Route;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\Storage;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::redirect('/', '/login')->name('login');

// Public Roadmap Routes
Route::get('/roadmap', [\App\Http\Controllers\PublicRoadmapController::class, 'index'])->name('public.roadmap');
Route::post('/roadmap/like/{roadmapItem}', [\App\Http\Controllers\PublicRoadmapController::class, 'toggleLike'])->name('public.roadmap.like');
Route::post('/roadmap/submit-request', [\App\Http\Controllers\PublicRoadmapController::class, 'submitRequest'])->name('public.roadmap.submit-request');

Route::get('/ticket/{ticket}', function (\App\Models\Ticket $ticket) {
    return redirect()->to("/tickets/{$ticket->id}".(auth()->user()->canUpdateTicket() ? '/edit' : ''));
})->name('ticket.show')->middleware('auth');

Route::get('/edit-book/{campaign}', \App\Filament\Pages\EditBook::class)
    ->name('edit-book')
    ->middleware('auth');

// Standalone ebook editing page (outside Filament admin panel)
Route::get('/standalone/edit-book/{campaign}', [\App\Http\Controllers\StandaloneEditBookController::class, 'show'])
    ->name('standalone.edit-book')
    ->middleware('auth');

// Debug route to test authentication
Route::middleware('auth')->get('/api/debug/auth', function () {
    return response()->json([
        'authenticated' => auth()->check(),
        'user' => auth()->user() ? auth()->user()->only(['id', 'name', 'email']) : null,
    ]);
});

// Edit Book API Routes
Route::middleware('auth')->prefix('api/edit-book')->group(function () {
    Route::get('/campaign/{campaign}', [\App\Http\Controllers\Api\EditBookController::class, 'getCampaign']);
    Route::post('/campaign/{campaign}/chapter', [\App\Http\Controllers\Api\EditBookController::class, 'addChapter']);
    Route::put('/campaign/{campaign}/chapter/{chapter}', [\App\Http\Controllers\Api\EditBookController::class, 'updateChapter']);
    Route::delete('/campaign/{campaign}/chapter/{chapter}', [\App\Http\Controllers\Api\EditBookController::class, 'deleteChapter']);
    Route::put('/campaign/{campaign}/section/{section}', [\App\Http\Controllers\Api\EditBookController::class, 'updateSection']);
    Route::post('/campaign/{campaign}/chapter/{chapter}/section', [\App\Http\Controllers\Api\EditBookController::class, 'addSection']);
    Route::delete('/campaign/{campaign}/section/{section}', [\App\Http\Controllers\Api\EditBookController::class, 'deleteSection']);
    Route::post('/campaign/{campaign}/reorder-sections', [\App\Http\Controllers\Api\EditBookController::class, 'reorderSections']);
    Route::post('/campaign/{campaign}/reorder-chapters', [\App\Http\Controllers\Api\EditBookController::class, 'reorderChapters']);
    Route::post('/campaign/{campaign}/regenerate-ebook', [\App\Http\Controllers\Api\EditBookController::class, 'regenerateEbook']);
});

Route::get('/preview/{campaign}', function (\App\Models\Campaign $campaign) {
    $ebook=$campaign;
    $format = $ebook->ebookFormat;
    return view('ebook.ebook-pdf', compact('ebook','format'));
});
Route::get('/image/{campaign}', function (\App\Models\Campaign $campaign) {
    $imageUrl = ImageGenerateFactory::create("google_search_images")->generateImage($campaign, $campaign->sections->first());
    return response()->json([
        "url"=>$imageUrl
    ]);
});

Route::middleware('auth')->get('/ai-image-models',[AIImageGeneratorController::class,'getAvailableModels'])->name('ai.image.models');
Route::middleware('auth')->post('/generate-image',[AIImageGeneratorController::class,'generateImage'])->name('generate.image');
Route::middleware('auth')->post('/generate-content',[AIImageGeneratorController::class,'generateContent'])->name('generate.content');
Route::middleware('auth')->post('/api/upload-image',[\App\Http\Controllers\Api\ImageUploadController::class,'uploadImage'])->name('api.upload.image');

Route::get('/content/csv/download', [BulkDownloadController::class, 'downloadCSV'])->name('content.csv.download');
Route::get('/share/{hash}', PublicShareController::class)->name('share.read');

// Review routes
Route::post('/reviews', [\App\Http\Controllers\ReviewController::class, 'store'])->name('reviews.store');
Route::get('/campaign_reviews/{campaign}/download', [BulkDownloadController::class, 'downloadReviews'])->name('reviews.download');

Route::get('/download-chapter-audio/{campaign}', [ChapterAudioController::class, 'downloadZip'])
    ->middleware('auth')
    ->name('download.chapter.audio');

Route::get('/download-chapter-audio/{campaign}/{model}', [ChapterAudioController::class, 'downloadZip'])
    ->middleware('auth')
    ->name('download.chapter.audio.model');

Route::get('/download-chapter-audio-single/{chapter}', [ChapterAudioController::class, 'downloadSingleChapter'])
    ->middleware('auth')
    ->name('download.chapter.audio.single');

// Update notification routes
Route::get('/unsubscribe/{token}', [\App\Http\Controllers\UpdateNotificationController::class, 'unsubscribe'])->name('update-notifications.unsubscribe');

// Authenticated routes for update preferences
Route::middleware('auth')->group(function () {
    Route::get('/user/update-preferences', [\App\Http\Controllers\UserUpdatePreferenceController::class, 'show'])->name('user.update-preferences');
    Route::post('/user/update-preferences', [\App\Http\Controllers\UserUpdatePreferenceController::class, 'update'])->name('user.update-preferences.update');
    Route::post('/changelog/{update}/mark-read', [\App\Http\Controllers\ChangelogController::class, 'markAsRead'])->name('changelog.mark-read');
    Route::get('/admin/update/{update}/preview-email', [\App\Http\Controllers\Admin\UpdateEmailPreviewController::class, 'preview'])->name('admin.update.preview-email');

    // Temp: Admin-only route for immediate campaign cleanup (for testing)
//    Route::get('/admin/cleanup-campaigns', function () {
//        // Only allow admin users
//        if (!auth()->user()->isAdmin()) {
//            abort(403, 'Unauthorized');
//        }
//
//        try {
//            // Run the cleanup command with days=0 for immediate cleanup
//            \Illuminate\Support\Facades\Artisan::call('campaigns:cleanup-soft-deleted', ['--days' => 0]);
//
//            $output = \Illuminate\Support\Facades\Artisan::output();
//
//            return response()->json([
//                'success' => true,
//                'message' => 'Cleanup command executed successfully',
//                'output' => $output
//            ]);
//
//        } catch (\Exception $e) {
//            return response()->json([
//                'success' => false,
//                'message' => 'Error executing cleanup command',
//                'error' => $e->getMessage()
//            ], 500);
//        }
//    })->name('admin.cleanup-campaigns');
    // Temp end: Admin-only route for immediate campaign cleanup (for testing)

});
