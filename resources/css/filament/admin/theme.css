@import '../../../../vendor/filament/filament/resources/css/theme.css';

@config './tailwind.config.js';

[x-cloak] {
    display: none;
}

.fi-body {
    @apply dark:bg-gray-900 !important;
}

.fi-sidebar-header, .fi-sidebar-nav, .fi-topbar > nav, .fi-ta-ctn {
    @apply dark:bg-gray-800 !important;
}

.fi-fieldset{
    @apply dark:border-gray-600 !important;
}

.fi-section, .fi-wi-stats-overview-stat {
    @apply dark:ring-gray-600 !important;
    @apply dark:bg-gray-800 !important;
}

@media (min-width: 640px) {
    .sm\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

@media (min-width: 640px) {
    .sm\:max-w-lg {
        max-width: 32rem;
    }
}

/* Line clamp utilities for book titles */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Custom grid improvements */
.campaign-grid-card {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.campaign-grid-card:hover {
    transform: translateY(-4px);
}

/* Ensure proper z-index stacking */
.campaign-grid-dropdown {
    z-index: 9999 !important;
}

/* Better line height for book titles */
.line-height-1\.2 {
    line-height: 1.2;
}
