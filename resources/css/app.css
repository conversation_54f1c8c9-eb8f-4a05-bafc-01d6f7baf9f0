@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the edit book application */
.edit-book-container {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.edit-book-container * {
  box-sizing: border-box;
}

/* Line clamp utilities for book titles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom grid improvements */
.campaign-grid-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.campaign-grid-card:hover {
  transform: translateY(-4px);
}

/* Ensure proper z-index stacking */
.campaign-grid-dropdown {
  z-index: 9999 !important;
}

/* Better line height for book titles */
.line-height-1\.2 {
  line-height: 1.2;
}

/* Draggable styles */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  transform: rotate(5deg);
}

/* Chapter item hover effects */
.chapter-item {
  transition: all 0.2s ease;
}

.chapter-item:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
