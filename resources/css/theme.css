@import '../../../../vendor/filament/filament/resources/css/theme.css';

@config './tailwind.config.js';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* [x-cloak] {
    display: none;
}

.fi-body {
    @apply dark:bg-gray-900 !important;
}

.fi-sidebar-header, .fi-sidebar-nav, .fi-topbar > nav, .fi-ta-ctn {
    @apply dark:bg-gray-800 !important;
}

.fi-fieldset{
    @apply dark:border-gray-600 !important;
}

.fi-section, .fi-wi-stats-overview-stat {
    @apply dark:ring-gray-600 !important;
    @apply dark:bg-gray-800 !important;
} */