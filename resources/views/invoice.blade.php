<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Invoice</title>

    <style>
        /*@font-face {*/
        /*    font-family: SourceSansPro;*/
        /*    src: url(SourceSansPro-Regular.ttf);*/
        /*}*/

        .clearfix:after {
            content: "";
            display: table;
            clear: both;
        }

        a {
            color: #147AFF;
            text-decoration: none;
        }

        body {
            position: relative;
            width: 21cm;
            height: 29.7cm;
            margin: 0 auto;
            color: #2A3268;
            background: #FFFFFF;
            font-size: 14px;
            font-family: Arial, SansSerif, serif;
        }

        header {
            width: 89%;
            padding: 10px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #C1C9DE;
        }

        #logo {
            float: left;
            margin-top: 8px;
        }

        #logo img {
            height: 50px;
            width: auto;
        }

        #company {
            float: right;
            text-align: right;
            color: #74778E;
        }

        #company .name {
            color: #2A3268;
        }

        #details {
            width: 89%;
            margin-bottom: 30px;
        }

        #client {
            padding-left: 8px;
            border-left: 5px solid #147AFF;
            float: left;
        }

        #client .to {
            color: #74778E;
        }

        h2.name {
            font-size: 1.5em;
            font-weight: normal;
            margin: 0;
        }

        #invoice {
            float: right;
            text-align: right;
        }

        #invoice h1 {
            color: #0F70EF;
            font-size: 1.5429em;
            line-height: 1;
            font-weight: normal;
            margin: 0  0 5px 0;
        }

        #invoice .date {
            font-size: 1em;
            color: #74778E;
        }

        table {
            width: 89%;
            border-collapse: collapse;
            border-spacing: 0;
            margin-bottom: 10px;
        }

        table th,
        table td {
            padding: 10px;
            background: #EDF2F8;
            text-align: center;
            border-bottom: 1px solid #FFFFFF;
        }

        table th {
            white-space: nowrap;
            font-weight: normal;
        }

        table td {
            text-align: center;
        }

        table td h3{
            color: #147AFF;
            font-size: 1.1em;
            font-weight: normal;
            margin: 0 0 0.2em 0;
        }

        table .no {
            color: #FFFFFF;
            font-size: 1.2em;
            background: #147AFF;
        }

        table .desc {
            text-align: left;
        }

        table tbody tr:last-child td {
            border: none;
        }

        table tfoot td {
            padding: 10px 20px;
            background: #FFFFFF;
            border-bottom: none;
            font-size: 1.1em;
            white-space: nowrap;
            border-top: 1px solid #C1C9DE;
        }

        table tfoot tr:first-child td {
            border-top: none;
        }

        table tfoot tr:last-child td {
            color: #147AFF;
            font-size: 1.4em;
            border-top: 1px solid #147AFF;

        }

        table tfoot tr td:first-child {
            border: none;
        }

        #conclusion{
            width: 89%;
            text-align: center;
        }

        #thanks{
            font-size: 2.1429em;
            margin-bottom: 10px;
        }

        #notices{
            padding-left: 8px;
            padding-top: 5px;
            padding-bottom: 5px;
            border-left: 5px solid #147AFF;
            margin-bottom: 20px;
            width: 89%;
        }

        #notices .notice {
            font-size: 1.1em;
        }

        footer {
            color: #74778E;
            width: 100%;
            height: 30px;
            position: absolute;
            bottom: 0;
            border-top: 1px solid #C1C9DE;
            padding: 8px 0;
            text-align: center;
        }


    </style>
</head>
<body>
<header class="clearfix">
    <div id="logo">
{{--        <img alt="xCloud" id="companyLogo" src="{{ config('app.url') . '/logo.png' }}">--}}
        <img alt="MedPoster AI" id="companyLogo" src="https://medposterai.com/wp-content/uploads/2024/05/medposter-ai.png">
    </div>
    <div id="company">
        <h2 class="name">{{ config('app.name') }}</h2>
        <div>PO Box 106, Parkersburg, WV 26104</div>
        <div><a href="mailto:<EMAIL>"><EMAIL></a></div>
    </div>
</header>
<main>
    <div id="details" class="clearfix">
        <div id="client">
            <div class="to">INVOICE TO:</div>
            <h2 class="name">{{  Arr::get($webhook->payload, 'customer_details.name') ?? Arr::get($webhook->payload, 'billing_details.name') }}</h2>
            <div class="email">
                <a href="mailto:{{ Arr::get($webhook->payload, 'customer_details.email') ?? Arr::get($webhook->payload, 'billing_details.email') }}">
                    {{ Arr::get($webhook->payload, 'customer_details.email') ?? Arr::get($webhook->payload, 'billing_details.email') }}
                </a>
            </div>
            <div>
                <div>{{ $user->billing_address ?? null }}</div>
            </div>
        </div>
        <div id="invoice">
            <h1>INVOICE # {{ Arr::get($webhook->payload, 'payment_intent') ?? Arr::get($webhook->payload, 'id') }}</h1>
            <div class="date">Date of Invoice: {{ $webhook->created_at->format('d/m/Y') }}</div>
        </div>
    </div>
    <table>
        <thead>
        <tr>
            <th class="no">TXN ID</th>
            <th class="status">STATUS</th>
            <th class="amount">AMOUNT</th>
            <th class="amount">TOTAL</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td class="no">{{  Arr::get($webhook->payload, 'payment_intent') }}</td>
            <td class="debit">{{ strtoupper(Arr::get($webhook->payload, 'payment_status') ?? 'REFUNDED') }}</td>
            <td class="credit">{{ number_format($amount, 2, '.', ',') . ' ' . strtoupper(Arr::get($webhook->payload, 'currency')) }}</td>
            <td class="total">{{ number_format($amount, 2, '.', ',') . ' ' . strtoupper(Arr::get($webhook->payload, 'currency')) }}</td>
        </tr>


        </tbody>
        <tfoot>
        <tr>
            <td colspan="1"></td>
            <td colspan="2">SUBTOTAL</td>
            <td>{{ number_format($amount, 2, '.', ',') . ' ' . strtoupper(Arr::get($webhook->payload, 'currency')) }}</td>
        </tr>

        <tr>
            <td colspan="1"></td>
            <td colspan="2">TOTAL</td>
            <td>{{ number_format($amount, 2, '.', ',') . ' ' . strtoupper(Arr::get($webhook->payload, 'currency')) }}</td>
        </tr>
        </tfoot>
    </table>
    {{--    <div id="notices">--}}
    {{--        <div>NOTICE:</div>--}}
    {{--        <div class="notice">A finance charge of 1.5% will be made on unpaid balances after 30 days.</div>--}}
    {{--    </div>--}}
    {{--    <div id="conclusion">--}}
    {{--        <div id="thanks">Thank you!</div>--}}
    {{--    </div>--}}
</main>
<footer>
    Invoice was created on a computer and is valid without the signature and seal.
</footer>
</body>
</html>
