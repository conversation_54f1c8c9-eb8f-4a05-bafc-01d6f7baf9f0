<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Notification Preferences - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto py-8 px-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        🔔 Update Notification Preferences
                    </h1>
                    <p class="text-gray-600 mt-1">
                        Manage how you receive notifications about {{ config('app.name') }} updates
                    </p>
                </div>
                
                <div class="flex gap-3">
                    <a href="{{ url()->previous() }}"
                       class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        ← Back
                    </a>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">
                            {{ session('success') }}
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Preferences Form -->
        <form method="POST" action="{{ route('user.update-preferences.update') }}">
            @csrf
            
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">📧 Email Notifications</h2>
                
                <div class="space-y-4">
                    <!-- Major Updates -->
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="email_major_updates" 
                                   name="email_major_updates" 
                                   type="checkbox" 
                                   value="1"
                                   {{ $preferences->email_major_updates ? 'checked' : '' }}
                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="email_major_updates" class="font-medium text-gray-700">
                                🎉 Major Updates
                            </label>
                            <p class="text-gray-500">
                                Significant new features, major releases, and important announcements
                            </p>
                        </div>
                    </div>

                    <!-- Minor Updates -->
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="email_minor_updates" 
                                   name="email_minor_updates" 
                                   type="checkbox" 
                                   value="1"
                                   {{ $preferences->email_minor_updates ? 'checked' : '' }}
                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="email_minor_updates" class="font-medium text-gray-700">
                                ✨ Feature Updates
                            </label>
                            <p class="text-gray-500">
                                New features, enhancements, and improvements to existing functionality
                            </p>
                        </div>
                    </div>

                    <!-- Patch Updates -->
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="email_patch_updates" 
                                   name="email_patch_updates" 
                                   type="checkbox" 
                                   value="1"
                                   {{ $preferences->email_patch_updates ? 'checked' : '' }}
                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="email_patch_updates" class="font-medium text-gray-700">
                                🔧 Bug Fixes
                            </label>
                            <p class="text-gray-500">
                                Bug fixes, patches, and minor improvements
                            </p>
                        </div>
                    </div>

                    <!-- Security Updates -->
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="email_security_updates" 
                                   name="email_security_updates" 
                                   type="checkbox" 
                                   value="1"
                                   {{ $preferences->email_security_updates ? 'checked' : '' }}
                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="email_security_updates" class="font-medium text-gray-700">
                                🔒 Security Updates
                            </label>
                            <p class="text-gray-500">
                                Security patches, vulnerability fixes, and security-related announcements
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            Your Account Information
                        </h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p><strong>Name:</strong> {{ $user->name }}</p>
                            <p><strong>Email:</strong> {{ $user->email }}</p>
                            <p class="mt-2">Notifications will be sent to this email address.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    <p>You can change these preferences at any time.</p>
                </div>
                
                <div class="flex gap-3">
                    <a href="{{ url()->previous() }}" 
                       class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        💾 Save Preferences
                    </button>
                </div>
            </div>
        </form>
    </div>
</body>
</html>
