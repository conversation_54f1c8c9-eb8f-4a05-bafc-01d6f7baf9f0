<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-auto">
        <div class="bg-white shadow-lg rounded-lg p-8 text-center">
            @if($success)
                <!-- Success State -->
                <div class="mb-6">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">
                        ✅ Successfully Unsubscribed
                    </h1>
                    
                    <p class="text-gray-600 mb-6">
                        You have been successfully unsubscribed from all update notification emails from {{ config('app.name') }}.
                    </p>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <p class="text-sm text-blue-800">
                            <strong>Note:</strong> You can still view updates and manage your preferences by logging into your account.
                        </p>
                    </div>
                </div>
            @else
                <!-- Error State -->
                <div class="mb-6">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">
                        ❌ Invalid Unsubscribe Link
                    </h1>
                    
                    <p class="text-gray-600 mb-6">
                        This unsubscribe link is invalid or has already been used. If you're still receiving unwanted emails, please contact our support team.
                    </p>
                </div>
            @endif

            <!-- Action Buttons -->
            <div class="space-y-3">
                <a href="{{ config('app.url') }}" 
                   class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                    🏠 Go to {{ config('app.name') }}
                </a>
                
                @if($success)
                    <a href="{{ route('user.update-preferences') }}" 
                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                        ⚙️ Manage Preferences
                    </a>
                @else
                    <a href="mailto:{{ config('mail.from.address') }}" 
                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                        📧 Contact Support
                    </a>
                @endif
            </div>

            <!-- Footer -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-xs text-gray-500">
                    {{ config('app.name') }} • Update Notifications
                </p>
            </div>
        </div>
    </div>
</body>
</html>
