
const kwestifyNewForm = document.createElement("form");
kwestifyNewForm.setAttribute("method", @js($method ?? 'GET'));
kwestifyNewForm.setAttribute("action", @js($action));
kwestifyNewForm.setAttribute("target", "_blank");

const formData = @js($form ?? [])

Object.keys(formData).forEach(key => {
    const kwestifyNewInput = document.createElement("input");
    kwestifyNewInput.setAttribute("type", "hidden");
    kwestifyNewInput.setAttribute("name", key);
    kwestifyNewInput.setAttribute("value", formData[key]);
    kwestifyNewForm.appendChild(kwestifyNewInput)
});

// Append the kwestifyNewForm to the document body
document.body.appendChild(kwestifyNewForm);

// Submit the kwestifyNewForm
kwestifyNewForm.submit();
