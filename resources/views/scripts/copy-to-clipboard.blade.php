// Get the text field

text = @js($text)

// Check if the Clipboard API is available
if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
        console.log('Text copied successfully!');
    }).catch(err => {
        console.error('Failed to copy text: ', err);
    });
} else {
    // Create a temporary textarea element
    const textarea = document.createElement('textarea');

    textarea.value = text;

    // Append the textarea to the document
    document.body.appendChild(textarea);

    // Select the content
    textarea.select();

    // Copy the text inside the textarea
    document.execCommand('copy');

    // Remove the textarea from the document
    document.body.removeChild(textarea);
}

