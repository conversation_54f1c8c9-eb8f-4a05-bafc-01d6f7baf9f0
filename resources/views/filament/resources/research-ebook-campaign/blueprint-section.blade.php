@php
    $viewData = $getViewData();
    $title = $viewData['title'] ?? '';
    $type = $viewData['type'] ?? 'list';
    
    if ($type === 'list') {
        $items = is_callable($viewData['items']) ? $viewData['items']($getState) : $viewData['items'];
    } else {
        $content = is_callable($viewData['content']) ? $viewData['content']($getState) : $viewData['content'];
    }
@endphp

<div class="rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-4">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ $title }}</h3>
    
    @if($type === 'list' && !empty($items))
        <ul class="space-y-2">
            @foreach($items as $index => $item)
                <li class="flex items-start">
                    <span class="text-gray-500 dark:text-gray-400 mr-2">{{ $index + 1 }}.</span>
                    <span class="text-gray-700 dark:text-gray-300">{{ $item }}</span>
                </li>
            @endforeach
        </ul>
    @elseif($type === 'text' && !empty($content))
        <div class="prose prose-sm dark:prose-invert max-w-none">
            <p class="text-gray-700 dark:text-gray-300">{{ $content }}</p>
        </div>
    @endif
</div>