<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Enhance Your eBookWriter Experience
                </h2>
                <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    Unlock powerful features and capabilities with our premium addons. Each addon is designed to supercharge your eBook creation workflow.
                </p>
            </div>
        </div>

        <!-- Addons Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            @foreach($this->getAddons() as $addon)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden @if($addon['selected'] ?? false) ring-2 ring-blue-800 @endif">
                    <!-- Addon Header -->
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-start justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0" style="padding-right: 12px;">
                                    @php
                                        $iconClass = match($addon['icon']) {
                                            'heroicon-o-speaker-wave' => 'heroicon-o-speaker-wave',
                                            'heroicon-o-magnifying-glass' => 'heroicon-o-magnifying-glass',
                                            'heroicon-o-book-open' => 'heroicon-o-book-open',
                                            'heroicon-o-video-camera' => 'heroicon-o-video-camera',
                                            default => 'heroicon-o-puzzle-piece'
                                        };
                                    @endphp
                                    <x-dynamic-component :component="$iconClass" class="w-8 h-8 text-primary-600" />
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        {{ $addon['name'] }}
                                    </h3>
                                    @if($addon['purchased'])
                                        <span class="inline-flex items-center px-0.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                            <x-heroicon-s-check-circle class="w-3 h-3 mr-1" /> &nbsp;
                                            Purchased
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            Available
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Addon Content -->
                    <div class="p-6">
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            {{ $addon['description'] }}
                        </p>

                        <!-- Features List -->
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Key Features:</h4>
                            <ul class="space-y-1">
                                @foreach($addon['features'] as $feature)
                                    <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                        <x-heroicon-s-check class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                                        {{ $feature }}
                                    </li>
                                @endforeach
                            </ul>
                        </div>

                        <!-- Action Button -->
                        <div class="flex justify-end">
                            @if($addon['purchased'])
                                <span class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 dark:bg-green-800 dark:text-green-100 cursor-not-allowed">
                                    <x-heroicon-s-check-circle class="w-4 h-6 mr-5" />
                                    Already Purchased
                                </span>
                            @else
                                <a href="{{ $addon['url'] }}"
                                   target="_blank"
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                    <x-heroicon-o-shopping-cart class="w-4 h-4 mr-2" />
                                    Purchase Now
                                    <x-heroicon-s-arrow-top-right-on-square class="w-3 h-3 ml-1" />
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Footer Information -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
            <div class="flex items-start space-x-3">
                <x-heroicon-o-information-circle class="w-6 h-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" style="margin-right: 5px;"/>
                <div>
                    <h3 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                        Need Help?
                    </h3>
                    <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
                        To upgrade your plan or purchase addons, please visit our sales page with the same email address you used to register.
                    </p>
                    <div class="flex flex-wrap gap-3 mt-2">
                        <ul>
                            <li>
                                <a href="https://ebookwriter.ai/"
                                target="_blank"
                                class="inline-flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                    Visit Sales Page
                                    <x-heroicon-s-arrow-top-right-on-square class="w-3 h-3 ml-1" />
                                </a>
                            </li>
                            <li>
                                <a href="https://warriorplus.com/account/purchases"
                                target="_blank"
                                class="inline-flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                    Manage Subscriptions
                                    <x-heroicon-s-arrow-top-right-on-square class="w-3 h-3 ml-1" />
                                </a>
                            </li>
                        </ul>
                        
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
