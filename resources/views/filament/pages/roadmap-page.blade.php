<x-filament-panels::page>
    <div class="roadmap-page-wrapper relative flex min-h-screen flex-col items-center bg-slate-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
        <div class="w-full max-w-4xl mx-auto">
            <!-- Header -->
            <header class="text-center mb-10">
                <div class="flex items-center justify-center gap-4 mb-4">
                    <h1 class="text-4xl md:text-5xl font-extrabold tracking-tight text-slate-900 dark:text-white">Product Roadmap</h1>
                    
                    <!-- Status Summary -->
                    <div class="flex items-center gap-2">
                        @php $statusCounts = $this->getStatusCounts(); @endphp
                        @if($statusCounts['in_progress'] > 0)
                            <div class="inline-flex items-center px-3 py-2 bg-orange-500  text-white text-sm font-medium rounded-full shadow-lg">
                                <div class="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                                {{ $statusCounts['in_progress'] }} In Progress
                            </div>
                        @endif
                        
                        @if($statusCounts['planned'] > 0)
                            <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm font-medium rounded-full shadow-lg">
                                {{ $statusCounts['planned'] }} Planned
                            </div>
                        @endif
                    </div>
                </div>

                <p class="mt-4 text-sm text-slate-500 dark:text-gray-400 max-w-2xl mx-auto">
                    Here's what we're working on and what's coming next. Stay tuned for exciting updates!
                </p>

                <!-- Quick Stats -->
                <div class="mt-6 flex justify-center gap-6 text-sm text-slate-600 dark:text-gray-400">
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <span>{{ $this->getUpcomingCount() }} Upcoming</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>{{ $this->getCompletedCount() }} Completed</span>
                    </div>
                </div>
            </header>
            
            <!-- Search and Filters -->
            <div class="mb-8 space-y-4">
                <!-- Submit Request Button -->
                @if(auth()->check() && auth()->user()->hasValidSubscription())
                    <div class="flex justify-center mb-6">
                        <button
                            wire:click="$dispatch('open-modal', { id: 'submit-request-modal' })"
                            class="inline-flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Submit Request
                        </button>
                    </div>
                @endif

                <!-- Search Bar -->
                <div class="w-full max-w-2xl mx-auto">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                            <div wire:loading.remove wire:target="loadRoadmapItems">
                                <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                    <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </div>
                            <div wire:loading wire:target="loadRoadmapItems" class="animate-spin">
                                <svg class="w-5 h-5 text-blue-500" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                        <x-filament::input
                            wire:model.live.debounce.500ms="filters.search"
                            placeholder="Search roadmap items..."
                            type="search"
                            class="w-full pl-12 pr-10 py-3 border border-slate-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition bg-white dark:bg-gray-800 text-slate-900 dark:text-white"
                            style="padding-left: 25px; border: 0.5px solid grey"
                        />
                        @if(!empty($this->filters['search']))
                            <button
                                wire:click="$set('filters.search', '')"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600 dark:hover:text-gray-300">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                    <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </button>
                        @endif
                    </div>
                </div>

                <!-- Filter Buttons -->
                <div class="flex flex-wrap justify-center gap-2 px-4">
                    <!-- Status Filters -->
                    <button wire:click="$set('filters.status', ''); applyFilters()"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap {{ empty($this->filters['status']) ? 'bg-blue-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600' }}">
                        All
                    </button>
                    <button wire:click="$set('filters.status', 'in_progress'); applyFilters()"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap {{ $this->filters['status'] === 'in_progress' ? 'bg-orange-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600' }}">
                        ⚡ In Progress
                    </button>
                    <button wire:click="$set('filters.status', 'planned'); applyFilters()"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap {{ $this->filters['status'] === 'planned' ? 'bg-blue-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600' }}">
                        📋 Planned
                    </button>
                    <button wire:click="$set('filters.status', 'completed'); applyFilters()"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap {{ $this->filters['status'] === 'completed' ? 'bg-green-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600' }}">
                        ✅ Completed
                    </button>

                    <!-- Category Filters -->
                    <div class="w-px h-6 bg-slate-300 dark:bg-gray-600 mx-2"></div>
                    <button wire:click="$set('filters.category', 'features'); applyFilters()"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap {{ $this->filters['category'] === 'features' ? 'bg-purple-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600' }}">
                        🚀 Features
                    </button>
                    <button wire:click="$set('filters.category', 'improvements'); applyFilters()"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap {{ $this->filters['category'] === 'improvements' ? 'bg-yellow-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600' }}">
                        ⚡ Improvements
                    </button>
                    <button wire:click="$set('filters.category', 'fixes'); applyFilters()"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap {{ $this->filters['category'] === 'fixes' ? 'bg-red-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600' }}">
                        🐛 Fixes
                    </button>

                    @if(!empty($this->filters['status']) || !empty($this->filters['category']) || !empty($this->filters['search']))
                        <button wire:click="clearFilters"
                                class="px-3 py-1 text-sm rounded-full bg-gray-500 text-white hover:bg-gray-600 transition shadow-lg whitespace-nowrap">
                            Clear All
                        </button>
                    @endif
                </div>
            </div>
            
            <!-- Timeline Content -->
            <div class="relative">
                <!-- Timeline Line (only show when there are items) -->
                @if($this->roadmapItems && $this->roadmapItems->count() > 0)
                    <div class="absolute left-4 sm:left-1/2 -ml-0.5 w-1 bg-blue-200 dark:bg-blue-800 h-full rounded-full">
                        <div class="absolute top-0 left-0 w-1 bg-blue-500 dark:bg-blue-400 rounded-full transition-all duration-500 ease-out" style="height: 100%;"></div>
                    </div>
                @endif

                <!-- Loading State -->
                <div wire:loading wire:target="loadRoadmapItems,clearFilters" class="space-y-16">
                    @for($i = 0; $i < 3; $i++)
                        <div class="timeline-item sm:space-x-8">
                            <div class="absolute left-4 -ml-5 sm:relative sm:left-auto sm:ml-0 z-10">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-500 ring-8 ring-slate-50 dark:ring-gray-900 animate-pulse">
                                    <div class="w-4 h-4 bg-white rounded-full"></div>
                                </div>
                            </div>
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 w-full ml-12 sm:ml-0 shadow-sm animate-pulse">
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                                    <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mt-2 sm:mt-0"></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                                </div>
                            </div>
                        </div>
                    @endfor
                </div>

                <!-- Roadmap Items List -->
                <div wire:loading.remove wire:target="loadRoadmapItems,clearFilters" class="space-y-16" id="roadmap-container">
                    @forelse($this->roadmapItems ?? [] as $item)
                        <div class="timeline-item sm:space-x-8">
                            <!-- Timeline Dot -->
                            <div class="absolute left-4 -ml-5 sm:relative sm:left-auto sm:ml-0 z-10">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full ring-8 ring-slate-50 dark:ring-gray-900 transition-all duration-300 hover:scale-110 timeline-dot-{{ $item->status }}">
                                    <span class="text-white text-sm font-bold">
                                        @if($item->status === 'in_progress')
                                            ⚡
                                        @else
                                            {{ $item->status_emoji }}
                                        @endif
                                    </span>
                                </div>
                            </div>

                            <!-- Roadmap Item Card -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 w-full ml-12 sm:ml-0 shadow-sm roadmap-card transition-all duration-300 hover:shadow-lg hover:-translate-y-1 relative
                                {{ $item->is_overdue && $item->status !== 'completed' ? 'border-l-4 border-red-500' : '' }}
                                {{ $item->priority === 'high' ? 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800' : '' }}
                                {{ $item->priority === 'critical' ? 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800' : '' }}">

                                <!-- High Priority Corner Badge -->
                                @if($item->priority === 'high' || $item->priority === 'critical')
                                    <div class="absolute top-0 right-0 -mt-2 -mr-2">
                                        <div class="inline-flex items-center px-2 py-1 text-xs font-bold rounded-full shadow-lg
                                            {{ $item->priority === 'critical' ? 'bg-red-500 text-white animate-pulse' : '' }}
                                            {{ $item->priority === 'high' ? 'bg-orange-500 text-white' : '' }}">
                                            {{ $item->priority === 'critical' ? '🔥 CRITICAL' : '⚡ HIGH' }}
                                        </div>
                                    </div>
                                @endif

                                <!-- Header -->
                                <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-2 flex-wrap">
                                            <h2 class="text-xl font-bold text-slate-900 dark:text-white">{{ $item->title }}</h2>

                                            <!-- Status Badge -->
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full status-badge-{{ $item->status }}">
                                                {{ $item->status === 'in_progress' ? 'In Progress' : ucfirst(str_replace('_', ' ', $item->status)) }}
                                            </span>                                     

                                            <!-- Timeframe Badge (Eye-catching) -->
                                            @if($item->estimated_date && $item->status !== 'completed')
                                                <div class="inline-flex items-center px-2 py-2 text-xs font-black rounded-full date-badge-enhanced {{ $item->is_overdue ? 'timeframe-badge-overdue' : '' }}">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    @if($item->is_overdue)
                                                        OVERDUE
                                                    @else
                                                        {{ $item->formatted_estimated_date }}
                                                    @endif
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Category and Additional Info -->
                                        <div class="flex items-center gap-4 text-sm">
                                            <span class="flex items-center gap-1 font-medium text-slate-800 dark:text-gray-100">
                                                {{ $item->category_emoji }}
                                                {{ ucfirst($item->category) }}
                                            </span>

                                            @if($item->estimated_date && !$item->is_overdue && $item->status !== 'completed')
                                                <span class="flex items-center gap-1 text-blue-700 dark:text-blue-300 font-medium">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                                    </svg>
                                                    {{ $item->time_until }}
                                                </span>
                                            @endif
                                            
                                              <!-- Like Button -->
                                            <div class="flex items-center gap-1">
                                                @if(auth()->check())
                                                    <button
                                                        wire:click="toggleLike({{ $item->id }})"
                                                        wire:loading.attr="disabled"
                                                        class="like-button group flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-200 hover:bg-red-50 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed {{ $item->likes->where('user_id', auth()->id())->count() > 0 ? 'liked' : '' }}">
                                                        <svg
                                                            class="heart-icon w-4 h-4 transition-colors duration-200 {{ $item->likes->where('user_id', auth()->id())->count() > 0 ? 'text-red-500 fill-current liked' : 'text-gray-400 group-hover:text-red-500' }}"
                                                            fill="{{ $item->likes->where('user_id', auth()->id())->count() > 0 ? 'currentColor' : 'none' }}"
                                                            stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                        </svg>
                                                        <span class="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-red-500">
                                                            {{ $item->likes_count ?? 0 }}
                                                        </span>
                                                        <span wire:loading wire:target="toggleLike({{ $item->id }})" class="ml-1">
                                                            <svg class="animate-spin w-3 h-3 text-gray-400" fill="none" viewBox="0 0 24 24">
                                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                            </svg>
                                                        </span>
                                                    </button>
                                                @else
                                                    <!-- Non-authenticated users see count only -->
                                                    <div class="flex items-center gap-1 px-2 py-1">
                                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                        </svg>
                                                        <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                                            {{ $item->likes_count ?? 0 }}
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Progress Bar for In Progress Items -->
                                @if($item->status === 'in_progress' && isset($item->metadata['progress']))
                                    <div class="mb-6 p-4 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                                        <div class="flex justify-between items-center text-sm font-medium text-slate-800 dark:text-gray-200 mb-3">
                                            <span class="flex items-center gap-2">
                                                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                </svg>
                                                Progress
                                            </span>
                                            <span class="text-orange-600 dark:text-orange-400 font-bold">{{ $item->progress_percentage }}%</span>
                                        </div>
                                        <div class="relative">
                                            <div class="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-3 shadow-inner">
                                                <div class="bg-orange-500 h-3 rounded-full transition-all duration-500 ease-out shadow-sm progress-bar-shimmer"
                                                     style="width: {{ $item->progress_percentage }}%">
                                                </div>
                                            </div>
                                            <!-- Progress milestones -->
                                            <div class="flex justify-between mt-2 text-xs text-slate-700 dark:text-gray-300">
                                                <span>0%</span>
                                                <span>25%</span>
                                                <span>50%</span>
                                                <span>75%</span>
                                                <span>100%</span>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Summary -->
                                <p class="text-slate-800 dark:text-gray-200 mb-4 leading-relaxed">{{ $item->summary }}</p>

                                <!-- Content (if available) -->
                                @if($item->content)
                                    <div class="prose prose-slate dark:prose-invert max-w-none text-sm prose-headings:text-slate-800 dark:prose-headings:text-gray-100 prose-p:text-slate-700 dark:prose-p:text-gray-200 prose-li:text-slate-700 dark:prose-li:text-gray-200">
                                        {!! \Illuminate\Support\Str::markdown($item->content) !!}
                                    </div>
                                @endif


                            </div>
                        </div>
                    @empty
                        <div class="text-center py-12">
                            <div class="mx-auto w-24 h-24 bg-slate-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                <svg class="w-12 h-12 text-slate-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-slate-900 dark:text-white mb-2">No roadmap items found</h3>
                            <p class="text-slate-500 dark:text-gray-400">
                                @if(!empty($this->filters['search']) || !empty($this->filters['status']) || !empty($this->filters['category']))
                                    Try adjusting your filters to see more items.
                                @else
                                    Check back soon for exciting updates!
                                @endif
                            </p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <style>
        .roadmap-card {
            border: 1px solid rgba(148, 163, 184, 0.1);
            transition: all 0.3s ease;
        }

        .roadmap-card:hover {
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .timeline-item {
            position: relative;
            display: flex;
            align-items: flex-start;
        }

        @media (max-width: 640px) {
            .timeline-item {
                padding-left: 0;
            }
        }

        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
        }

        .prose p {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .prose ul, .prose ol {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        /* High priority card animations */
        .roadmap-card.high-priority {
            animation: subtle-glow 3s ease-in-out infinite alternate;
        }

        @keyframes subtle-glow {
            from {
                box-shadow: 0 0 5px rgba(251, 191, 36, 0.3);
            }
            to {
                box-shadow: 0 0 15px rgba(251, 191, 36, 0.5);
            }
        }

        /* Critical priority pulse effect */
        .critical-badge {
            animation: critical-pulse 2s ease-in-out infinite;
        }

        @keyframes critical-pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.9;
            }
        }

        /* Overdue bounce animation */
        .overdue-badge {
            animation: overdue-bounce 1s ease-in-out infinite;
        }

        @keyframes overdue-bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-3px);
            }
            60% {
                transform: translateY(-2px);
            }
        }

        /* Enhanced progress bar animations */
        .progress-bar-enhanced {
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .progress-shimmer {
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            50% {
                transform: translateX(100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* Enhanced date badge styling to match progress bar */
        .date-badge-enhanced {
            background: linear-gradient(135deg, #f97316, #eab308) !important;
            color: white !important;
        }

        .dark .date-badge-enhanced {
            background: linear-gradient(135deg, #f97316, #eab308) !important;
        }

        /* Better text contrast for light mode */
        .roadmap-card .text-slate-500 {
            color: rgb(71 85 105) !important; /* slate-600 equivalent */
        }

        .dark .roadmap-card .text-slate-500 {
            color: rgb(203 213 225) !important;
        }

        .roadmap-card .text-slate-600 {
            color: rgb(51 65 85) !important; /* slate-700 equivalent */
        }

        .dark .roadmap-card .text-slate-600 {
            color: rgb(226 232 240) !important;
        }

        .roadmap-card .text-slate-700 {
            color: rgb(51 65 85) !important; /* slate-700 */
        }

        .dark .roadmap-card .text-slate-700 {
            color: rgb(241 245 249) !important; /* slate-100 */
        }

        .roadmap-card .text-slate-800 {
            color: rgb(30 41 59) !important; /* slate-800 */
        }

        .dark .roadmap-card .text-slate-800 {
            color: rgb(248 250 252) !important; /* slate-50 */
        }

        /* Ensure prose content is visible */
        .roadmap-card .prose {
            color: rgb(51 65 85) !important; /* slate-700 */
        }

        .dark .roadmap-card .prose {
            color: rgb(226 232 240) !important; /* slate-200 */
        }

        .roadmap-card .prose h1,
        .roadmap-card .prose h2,
        .roadmap-card .prose h3,
        .roadmap-card .prose h4,
        .roadmap-card .prose h5,
        .roadmap-card .prose h6 {
            color: rgb(30 41 59) !important; /* slate-800 */
        }

        .dark .roadmap-card .prose h1,
        .dark .roadmap-card .prose h2,
        .dark .roadmap-card .prose h3,
        .dark .roadmap-card .prose h4,
        .dark .roadmap-card .prose h5,
        .dark .roadmap-card .prose h6 {
            color: rgb(248 250 252) !important; /* slate-50 */
        }

        .roadmap-card .prose p,
        .roadmap-card .prose li {
            color: rgb(51 65 85) !important; /* slate-700 */
        }

        .dark .roadmap-card .prose p,
        .dark .roadmap-card .prose li {
            color: rgb(203 213 225) !important; /* slate-300 */
        }

        /* Timeline dot colors - Force specific colors */
        .timeline-dot-completed {
            background-color: #10b981 !important; /* green-500 */
        }

        .timeline-dot-in_progress {
            background-color: #f97316 !important; /* orange-500 */
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .timeline-dot-planned {
            background-color: #3b82f6 !important; /* blue-500 */
        }

        .timeline-dot-cancelled {
            background-color: #9ca3af !important; /* gray-400 */
        }

        /* Force Tailwind background colors to work */
        .bg-yellow-500 {
            background-color: #eab308 !important;
        }

        .bg-green-500 {
            background-color: #22c55e !important;
        }

        .bg-purple-500 {
            background-color: #a855f7 !important;
        }

        .bg-orange-500 {
            background-color: #f97316 !important;
        }

        .bg-blue-500 {
            background-color: #3b82f6 !important;
        }

        .bg-red-500 {
            background-color: #ef4444 !important;
        }

        .bg-gray-400 {
            background-color: #9ca3af !important;
        }

        .bg-gray-500 {
            background-color: #6b7280 !important;
        }

        .bg-indigo-500 {
            background-color: #6366f1 !important;
        }

        .bg-pink-500 {
            background-color: #ec4899 !important;
        }

        .bg-teal-500 {
            background-color: #14b8a6 !important;
        }

        .bg-cyan-500 {
            background-color: #06b6d4 !important;
        }

        /* Dark mode variants */
        .dark .bg-yellow-500 {
            background-color: #eab308 !important;
        }

        .dark .bg-green-500 {
            background-color: #22c55e !important;
        }

        .dark .bg-purple-500 {
            background-color: #a855f7 !important;
        }

        .dark .bg-orange-500 {
            background-color: #f97316 !important;
        }

        .dark .bg-blue-500 {
            background-color: #3b82f6 !important;
        }

        .dark .bg-red-500 {
            background-color: #ef4444 !important;
        }

        .dark .bg-gray-400 {
            background-color: #9ca3af !important;
        }

        .dark .bg-gray-500 {
            background-color: #6b7280 !important;
        }

        .dark .bg-indigo-500 {
            background-color: #6366f1 !important;
        }

        .dark .bg-pink-500 {
            background-color: #ec4899 !important;
        }

        .dark .bg-teal-500 {
            background-color: #14b8a6 !important;
        }

        .dark .bg-cyan-500 {
            background-color: #06b6d4 !important;
        }

        /* Like button animations */
        .like-button {
            transition: all 0.2s ease-in-out;
        }

        .like-button:hover {
            transform: scale(1.05);
        }

        .like-button:active {
            transform: scale(0.95);
        }

        .like-button.liked {
            animation: heartBeat 0.6s ease-in-out;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            25% { transform: scale(1.2); }
            50% { transform: scale(1.1); }
            75% { transform: scale(1.15); }
            100% { transform: scale(1); }
        }

        /* Heart icon fill animation */
        .heart-icon {
            transition: all 0.3s ease-in-out;
        }

        .heart-icon.liked {
            color: #ef4444 !important;
            fill: #ef4444 !important;
        }

        .heart-icon:not(.liked):hover {
            color: #ef4444 !important;
            transform: scale(1.1);
        }
    </style>

    <!-- Submit Request Modal -->
    <div x-data="{ open: false }"
         x-on:open-modal.window="if ($event.detail.id === 'submit-request-modal') open = true"
         x-on:close-modal.window="open = false"
         x-show="open"
         x-cloak
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">

        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
             x-show="open"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="open = false"></div>

        <!-- Modal -->
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md transform transition-all"
                 x-show="open"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">

                <!-- Modal Header -->
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Submit Request</h3>
                        <button @click="open = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Body -->
                <form wire:submit.prevent="submitRequest" class="p-6 space-y-4">
                    <!-- Title Field -->
                    <div>
                        <label for="request_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Title <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="request_title"
                            wire:model="requestForm.title"
                            placeholder="Enter a descriptive title for your request"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            maxlength="255"
                            required>
                        @error('requestForm.title')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Content Field -->
                    <div>
                        <label for="request_content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description <span class="text-red-500">*</span>
                        </label>
                        <textarea
                            id="request_content"
                            wire:model="requestForm.content"
                            placeholder="Provide detailed information about your request, including use cases and benefits"
                            rows="6"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                            required></textarea>
                        @error('requestForm.content')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Type Field -->
                    <div>
                        <label for="request_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Type <span class="text-red-500">*</span>
                        </label>
                        <select
                            id="request_type"
                            wire:model="requestForm.type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            required>
                            <option value="">Select type...</option>
                            <option value="feature">🚀 Feature</option>
                            <option value="improvement">⚡ Improvement</option>
                            <option value="bug">🐛 Bug Fix</option>
                        </select>
                        @error('requestForm.type')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Modal Footer -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <button
                            type="button"
                            @click="open = false"
                            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                            Cancel
                        </button>
                        <button
                            type="submit"
                            wire:loading.attr="disabled"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <span wire:loading.remove>Submit Request</span>
                            <span wire:loading class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Submitting...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-filament-panels::page>
