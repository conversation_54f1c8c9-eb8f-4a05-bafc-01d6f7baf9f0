<x-filament-panels::page>
    <form wire:submit="save">
        {{ $this->form }}

        <div class="mt-6 flex justify-end">
            <x-filament::button type="submit">
                Save Settings
            </x-filament::button>
        </div>
    </form>

    <!-- Direct Preview Button (Fallback) -->


    <!-- Include Font Awesome for star icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <script>

        function updateStars(rating, stars) {
            stars.forEach(star => {
                const starRating = parseInt(star.getAttribute('data-rating'));
                if (starRating <= rating) {
                    star.classList.add('text-yellow-400');
                    star.classList.remove('text-gray-300');
                } else {
                    star.classList.remove('text-yellow-400');
                    star.classList.add('text-gray-300');
                }
            });
        }

        function highlightStars(rating, stars) {
            stars.forEach(star => {
                const starRating = parseInt(star.getAttribute('data-rating'));
                if (starRating <= rating) {
                    star.classList.add('text-yellow-400');
                    star.classList.remove('text-gray-300');
                } else {
                    star.classList.remove('text-yellow-400');
                    star.classList.add('text-gray-300');
                }
            });
        }

        function updateRatingText(rating, ratingText) {
            let text = '';
            switch(rating) {
                case 1:
                    text = 'Poor (1/5)';
                    break;
                case 2:
                    text = 'Fair (2/5)';
                    break;
                case 3:
                    text = 'Good (3/5)';
                    break;
                case 4:
                    text = 'Very Good (4/5)';
                    break;
                case 5:
                    text = 'Excellent (5/5)';
                    break;
                default:
                    text = 'Please select a rating';
            }
            ratingText.textContent = text;
        }

        // Function to update the preview modal with form data
        function updatePreviewModal(formData) {
            // Ensure formData is an object
            formData = formData || {};

            // Update title and subtitle
            document.getElementById('previewTitle').textContent = formData.title || 'We\'d Love Your Feedback!';
            document.getElementById('previewSubtitle').textContent = formData.subtitle || 'Please take a moment to rate your experience with this eBook.';

            // Update button text
            document.getElementById('previewSubmitButton').textContent = formData.submit_button_text || 'Submit Review';
            document.getElementById('previewCloseThankYou').textContent = formData.close_button_text || 'Close';

            // Update thank you message
            document.getElementById('previewThankYouTitle').textContent = formData.thank_you_message || 'Thank You!';

            // Toggle field visibility based on settings (default to showing fields if not specified)
            document.getElementById('previewNameField').style.display =
                (formData.show_name_field === undefined || formData.show_name_field) ? 'block' : 'none';
            document.getElementById('previewEmailField').style.display =
                (formData.show_email_field === undefined || formData.show_email_field) ? 'block' : 'none';
            document.getElementById('previewRatingField').style.display =
                (formData.show_rating_field === undefined || formData.show_rating_field) ? 'block' : 'none';
            document.getElementById('previewCommentField').style.display =
                (formData.show_comment_field === undefined || formData.show_comment_field) ? 'block' : 'none';

            // Update required attributes (default to required for name, email, and rating if not specified)
            if (formData.name_field_required === undefined || formData.name_field_required) {
                document.getElementById('previewName').setAttribute('required', '');
            } else {
                document.getElementById('previewName').removeAttribute('required');
            }

            if (formData.email_field_required === undefined || formData.email_field_required) {
                document.getElementById('previewEmail').setAttribute('required', '');
            } else {
                document.getElementById('previewEmail').removeAttribute('required');
            }

            if (formData.rating_field_required === undefined || formData.rating_field_required) {
                document.getElementById('previewRating').setAttribute('required', '');
            } else {
                document.getElementById('previewRating').removeAttribute('required');
            }

            // Comment is optional by default
            if (formData.comment_field_required) {
                document.getElementById('previewComment').setAttribute('required', '');
            } else {
                document.getElementById('previewComment').removeAttribute('required');
            }

            // Reset to review form view (hide thank you message)
            document.getElementById('previewReviewForm').style.display = 'block';
            document.getElementById('previewThankYouMessage').style.display = 'none';

            // Initialize star rating to 5 stars
            const stars = document.querySelectorAll('#previewRatingField .star');
            const ratingText = document.querySelector('#previewRatingField .rating-text');
            updateStars(5, stars);
            updateRatingText(5, ratingText);
        }

        // Function to show the preview modal
        function showPreviewModal() {
            const modal = document.getElementById('reviewPreviewModal');
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent scrolling

            // Set the modal open flag
            isModalOpen = true;

            // Ensure the modal stays open by preventing form submission
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const originalOnSubmit = form.onsubmit;
                form.onsubmit = function(e) {
                    if (!modal.classList.contains('hidden')) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                    return originalOnSubmit ? originalOnSubmit(e) : true;
                };
            });

            // Add event listeners to prevent modal closing when clicking inside
            const modalOverlay = document.getElementById('modalOverlay');
            const modalContent = document.getElementById('modalContent');

            // Close modal when clicking outside, but not when clicking inside
            modalOverlay.addEventListener('click', function(e) {
                if (e.target === modalOverlay) {
                    hidePreviewModal();
                }
            });

            // Prevent clicks inside the modal from closing it
            modalContent.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        // Function to hide the preview modal
        function hidePreviewModal() {
            const modal = document.getElementById('reviewPreviewModal');
            modal.classList.add('hidden');
            document.body.style.overflow = ''; // Restore scrolling

            // Reset the modal open flag
            isModalOpen = false;

            // Add a small delay to ensure any pending form submissions don't happen
            setTimeout(() => {
                isModalOpen = false;
            }, 100);
        }

        // Flag to track if the modal is open
        let isModalOpen = false;

        document.addEventListener('DOMContentLoaded', function() {
            // Prevent form submission when modal is open
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    if (isModalOpen) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                });
            });
            // Listen for the open-review-preview-modal event using Livewire's event system
            document.addEventListener('livewire:initialized', () => {
                Livewire.on('open-review-preview-modal', (event) => {
                try {
                    // Get the form data from the event (Livewire passes data directly)
                    const formData = event.formData || {};

                    // Log the data for debugging
                    console.log('Preview form data:', formData);

                    // Update the modal with the form data
                    updatePreviewModal(formData);

                    // Show the modal
                    showPreviewModal();
                } catch (error) {
                    // Log any errors
                    console.error('Error in preview modal:', error);

                    // Use default values if there's an error
                    const defaultFormData = {
                        title: 'We\'d Love Your Feedback!',
                        subtitle: 'Please take a moment to rate your experience with this eBook.',
                        submit_button_text: 'Submit Review',
                        close_button_text: 'Close',
                        thank_you_message: 'Thank You!',
                        show_name_field: true,
                        show_email_field: true,
                        show_rating_field: true,
                        show_comment_field: true,
                        name_field_required: true,
                        email_field_required: true,
                        rating_field_required: true,
                        comment_field_required: false
                    };

                    // Update the modal with default data
                    updatePreviewModal(defaultFormData);

                    // Show the modal
                    showPreviewModal();
                }
                });
            });

            // Close modal when clicking the close button
            document.getElementById('closePreviewModal').addEventListener('click', hidePreviewModal);

            // Show thank you message when clicking submit button in preview
            document.getElementById('previewSubmitButton').addEventListener('click', function() {
                // Add a loading state to the button
                const button = this;
                const originalText = button.textContent;
                button.disabled = true;
                button.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Submitting...';

                // Simulate a form submission delay
                setTimeout(function() {
                    document.getElementById('previewReviewForm').style.display = 'none';
                    document.getElementById('previewThankYouMessage').style.display = 'block';
                    // Reset button state for next preview
                    button.disabled = false;
                    button.textContent = originalText;
                }, 800);
            });


            // Add star rating functionality
            const stars = document.querySelectorAll('#previewRatingField .star');
            const ratingInput = document.getElementById('previewRating');
            const ratingText = document.querySelector('#previewRatingField .rating-text');

            stars.forEach(star => {
                // Handle click on star
                star.addEventListener('click', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    updateStars(rating, stars);
                    ratingInput.value = rating;
                    updateRatingText(rating, ratingText);
                });

                // Handle hover effects
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    highlightStars(rating, stars);
                });

                star.addEventListener('mouseleave', function() {
                    const currentRating = parseInt(ratingInput.value);
                    updateStars(currentRating, stars);
                });
            });
        });
    </script>
</x-filament-panels::page>
