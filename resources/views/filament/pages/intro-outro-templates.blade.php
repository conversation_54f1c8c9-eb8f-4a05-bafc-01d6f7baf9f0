<x-filament::page>
    <div class="space-y-6">
        <!-- Header Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center space-x-3 mb-4">
                <x-heroicon-o-microphone class="w-8 h-8 text-primary-600" />
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Audio Intro/Outro Templates
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Manage dynamic templates for chapter introductions and conclusions across multiple languages
                    </p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <div class="font-medium text-blue-900 dark:text-blue-100">Languages Supported</div>
                    <div class="text-blue-700 dark:text-blue-300">{{ count($templates) }} languages</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <div class="font-medium text-green-900 dark:text-green-100">Template Types</div>
                    <div class="text-green-700 dark:text-green-300">Intro & Outro</div>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg">
                    <div class="font-medium text-purple-900 dark:text-purple-100">Dynamic Placeholders</div>
                    <div class="text-purple-700 dark:text-purple-300">5 variables available</div>
                </div>
            </div>
        </div>

        <!-- Template Guidelines -->
        <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <div class="flex items-start space-x-3">
                <x-heroicon-o-light-bulb class="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                <div class="text-sm">
                    <h3 class="font-medium text-amber-900 dark:text-amber-100 mb-2">Template Guidelines</h3>
                    <ul class="space-y-1 text-amber-800 dark:text-amber-200">
                        <li>• Use placeholders like <code class="bg-amber-100 dark:bg-amber-800 px-1 rounded">{chapter_number}</code> for dynamic content</li>
                        <li>• Keep templates concise and professional (recommended: 50-150 characters)</li>
                        <li>• Maintain consistent tone and style within each language</li>
                        <li>• Test templates with different chapter titles and book names</li>
                        <li>• Consider cultural appropriateness for each language</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form wire:submit.prevent="submit" wire:target="submit" class="space-y-6">
            {{ $this->form }}

            <div class="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p>Changes will be saved to <code>resources/prompts/intro-outro-templates.json</code></p>
                    <p>A backup will be created automatically before updating.</p>
                </div>
                
                <div class="flex space-x-3">
                    <x-filament::button 
                        color="gray" 
                        outlined
                        wire:click="mount"
                        icon="heroicon-o-arrow-path"
                    >
                        Reset Changes
                    </x-filament::button>
                    
                    <x-filament::button 
                        icon="heroicon-o-check-circle" 
                        type="submit" 
                        loading="true"
                    >
                        Update Templates
                    </x-filament::button>
                </div>
            </div>
        </form>

        <!-- API Information -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h3 class="font-medium text-gray-900 dark:text-white mb-2">API Access</h3>
            <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <p>Templates are accessible via API endpoints:</p>
                <code class="block bg-gray-100 dark:bg-gray-700 p-2 rounded text-xs">
                    GET {{ url('/api/intro-outro-templates') }} - All templates<br>
                    GET {{ url('/api/intro-outro-templates/{language}') }} - Language-specific templates
                </code>
            </div>
        </div>
    </div>
</x-filament::page>
