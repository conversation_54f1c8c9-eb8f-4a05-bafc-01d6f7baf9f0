<x-filament-panels::page>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .plan-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            transition: all 0.3s ease-in-out;
            border: 1px solid #e5e7eb;
            min-height: 500px;
        }
        .dark .plan-card {
            background: #1f2937;
            border-color: #374151;
        }
        .plan-card.active {
            border: 2px solid #4f46e5;
            box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
            transform: scale(1.02);
        }
        .plan-card.popular {
            border: 2px solid #4f46e5;
        }
        .plan-button {
            margin-top: 2rem;
            width: 100%;
            font-weight: 600;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            border: none;
            cursor: pointer;
        }
        .plan-button-primary {
            background-color: #4f46e5;
            color: white;
        }
        .plan-button-primary:hover {
            background-color: #4338ca;
        }
        .plan-button-secondary {
            background-color: white;
            border: 1px solid #d1d5db;
            color: #374151;
        }
        .plan-button-secondary:hover {
            background-color: #f9fafb;
        }
        .dark .plan-button-secondary {
            background-color: #374151;
            border-color: #4b5563;
            color: #d1d5db;
        }
        .dark .plan-button-secondary:hover {
            background-color: #4b5563;
        }
        .plan-button-active {
            background-color: #10b981;
            color: white;
            cursor: default;
        }
        .plan-button-active:hover {
            background-color: #059669;
        }
        .popular-badge {
            position: absolute;
            top: -0.5rem;
            left: 50%;
            transform: translateX(-50%);
            background-color: #4f46e5;
            color: white;
            font-size: 0.75rem;
            font-weight: 700;
            padding: 0.375rem 1rem;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
    </style>

    <div class="space-y-8">
        <!-- Header Section -->
        <div class="text-center mb-8">
            <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900 dark:text-white mb-4">
                Find the perfect plan
            </h1>
            <p class="text-lg md:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Whether you're just starting out or scaling your business, we have a plan that's right for you.
            </p>
        </div>

        <!-- Pricing Plans Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
            @foreach($this->getPricingPlans() as $plan)
                @php
                    $isCurrentPlan = $this->isCurrentPlan($plan['wplus_code']);
                    $isPopular = $plan['popular'];
                @endphp

                <div class="plan-card @if($isCurrentPlan) active @elseif($isPopular) popular @endif relative">
                    @if($isPopular && !$isCurrentPlan)
                        <span class="popular-badge">Most Popular</span>
                    @endif

                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                            {{ str_replace('Monthly ', '', $plan['name']) }}
                        </h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-6">
                            {{ $plan['description'] }}
                        </p>
                        <p class="text-6xl font-extrabold text-gray-900 dark:text-white mb-8">
                            <span class="text-4xl">{{ $plan['price'] }}</span>
                            <span class="text-xl font-medium text-gray-500 dark:text-gray-400">{{ $plan['period'] }}</span>
                        </p>

                        <ul class="mt-8 space-y-4 text-gray-600 dark:text-gray-400 mb-6">
                            @foreach($plan['features'] as $feature)
                                <li class="flex items-center">
                                    <x-heroicon-s-check-circle class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" />
                                    {{ $feature }}
                                </li>
                            @endforeach
                        </ul>

                        @if($plan['addons_included'] && $plan['savings'] > 0)
                            <div class="mt-6 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                <div class="flex items-center space-x-2 mb-1">
                                    <x-heroicon-s-star class="w-4 h-4 text-green-600 dark:text-green-400" />
                                    <span class="text-sm font-bold text-green-900 dark:text-green-100">
                                        All Addons Included
                                    </span>
                                </div>
                            </div>
                        @endif
                    </div>

                    @if($isCurrentPlan)
                        <button class="plan-button plan-button-active">
                            <x-heroicon-s-check class="w-5 h-5 mr-2" />
                            Current Plan
                        </button>
                    @else
                        <a href="{{ $plan['url'] }}" target="_blank" class="plan-button @if($isPopular) plan-button-primary @else plan-button-secondary @endif">
                            @if($isPopular)
                                Upgrade to {{ str_replace('Monthly ', '', $plan['name']) }}
                            @else
                                Choose Plan
                            @endif
                        </a>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- FAQ Section -->
        <div class="mt-8">
            <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 dark:text-white text-center mb-8">
                Frequently Asked Questions
            </h2>
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div class="space-y-6">
                    <details class="group border-b border-gray-200 dark:border-gray-700 pb-6">
                        <summary class="flex justify-between items-center cursor-pointer list-none">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                                What are monthly credits and how do they work?
                            </h3>
                            <span class="text-gray-500 dark:text-gray-400">
                                <x-heroicon-o-plus class="w-5 h-5 group-open:hidden" />
                                <x-heroicon-o-minus class="w-5 h-5 hidden group-open:block" />
                            </span>
                        </summary>
                        <p class="mt-4 text-gray-600 dark:text-gray-400">
                            Monthly credits are used for generating content. Each action, like creating an eBook or exporting a document, consumes a certain number of credits. Unused credits do not roll over to the next month.
                        </p>
                    </details>

                    <details class="group border-b border-gray-200 dark:border-gray-700 pb-6">
                        <summary class="flex justify-between items-center cursor-pointer list-none">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                                Can I change my plan later?
                            </h3>
                            <span class="text-gray-500 dark:text-gray-400">
                                <x-heroicon-o-plus class="w-5 h-5 group-open:hidden" />
                                <x-heroicon-o-minus class="w-5 h-5 hidden group-open:block" />
                            </span>
                        </summary>
                        <p class="mt-4 text-gray-600 dark:text-gray-400">
                            Absolutely! You can upgrade or downgrade your plan at any time from your account settings. Changes will be prorated and take effect immediately.
                        </p>
                    </details>

                    <details class="group border-b border-gray-200 dark:border-gray-700 pb-6">
                        <summary class="flex justify-between items-center cursor-pointer list-none">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                                What is the difference between Basic, Advanced, and Premium eBook generation?
                            </h3>
                            <span class="text-gray-500 dark:text-gray-400">
                                <x-heroicon-o-plus class="w-5 h-5 group-open:hidden" />
                                <x-heroicon-o-minus class="w-5 h-5 hidden group-open:block" />
                            </span>
                        </summary>
                        <p class="mt-4 text-gray-600 dark:text-gray-400">
                            The difference lies in the level of customization and features. Basic offers standard templates, Advanced provides more template choices and branding options, while Premium unlocks full customization, custom fonts, and advanced layouts.
                        </p>
                    </details>

                    <details class="group border-b-0 pb-0">
                        <summary class="flex justify-between items-center cursor-pointer list-none">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                                Do you offer a free trial?
                            </h3>
                            <span class="text-gray-500 dark:text-gray-400">
                                <x-heroicon-o-plus class="w-5 h-5 group-open:hidden" />
                                <x-heroicon-o-minus class="w-5 h-5 hidden group-open:block" />
                            </span>
                        </summary>
                        <p class="mt-4 text-gray-600 dark:text-gray-400">
                            We do not offer a free trial, but our Starter plan is a great way to test our core features at a low cost. We also offer a 14-day money-back guarantee on all plans.
                        </p>
                    </details>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
