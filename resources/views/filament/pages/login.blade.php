<x-filament-panels::page.simple>
    @if (filament()->hasRegistration())
        <x-slot name="subheading">
            {{ __('filament-panels::pages/auth/login.actions.register.before') }}

            {{ $this->registerAction }}
        </x-slot>
    @endif
    <x-filament-panels::form wire:submit="authenticate">
        {{ $this->form }}

        <x-filament-panels::form.actions :actions="$this->getCachedFormActions()" :full-width="$this->hasFullWidthFormActions()"/>
    </x-filament-panels::form>
    @php
        $url = config('aiebook.home_url');
    @endphp

    <span class="text-center font-bold tracking-tight text-gray-950 dark:text-white">
       <a style="--c-300:var(--primary-300);--c-400:var(--primary-400);--c-500:var(--primary-500);--c-600:var(--primary-600);" class="fi-link fi-link-size-md relative inline-flex items-center justify-center font-semibold outline-none transition duration-75 hover:underline focus:underline disabled:pointer-events-none disabled:opacity-70 gap-1.5 text-sm text-custom-600 dark:text-custom-400 fi-ac-link-action" href={{ $url }} target="_blank"> Visit {{ config('app.name') }} Here To Learn More</a>
    </span>
</x-filament-panels::page.simple>
