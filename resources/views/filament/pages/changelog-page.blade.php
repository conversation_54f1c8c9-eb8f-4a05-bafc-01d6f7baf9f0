<x-filament-panels::page>
    <div class="changelog-page-wrapper relative flex min-h-screen flex-col items-center bg-slate-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
        <!-- All your existing content here -->
        <!-- Make sure everything is inside this single root div -->
        
        <div class="w-full max-w-4xl mx-auto">
            <!-- Header -->
            <header class="text-center mb-10">
                <div class="flex items-center justify-center gap-4 mb-4">
                    <h1 class="text-4xl md:text-5xl font-extrabold tracking-tight text-slate-900 dark:text-white">What's New</h1>

                    <!-- What's New Badge -->
                    @if(is_array($this->unreadUpdates) && count($this->unreadUpdates) > 0)
                        <div class="relative">
                            <button
                                wire:click="markAllAsRead"
                                class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ count($this->unreadUpdates) }} New
                            </button>
                        </div>
                    @endif
                </div>

                <p class="mt-4 text-lg text-slate-500 dark:text-gray-400 max-w-2xl mx-auto">
                    We're constantly improving our app. Here's a summary of what has changed.
                </p>

                <!-- Preferences Button -->
                @if(auth()->check())
                    <div class="mt-6">
                        <button
                            wire:click="openPreferencesModal"
                            class="inline-flex items-center px-4 py-2 text-sm text-slate-600 dark:text-gray-400 hover:text-slate-900 dark:hover:text-white bg-white dark:bg-gray-800 hover:bg-slate-50 dark:hover:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 transition-colors duration-200 shadow-sm">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Notification Preferences
                        </button>
                    </div>
                @endif
            </header>
            
            <!-- Search Bar -->
            <div class="mb-8 w-full max-w-2xl mx-auto">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                        <div wire:loading.remove wire:target="loadUpdates">
                            <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </div>
                        <div wire:loading wire:target="loadUpdates" class="animate-spin">
                            <svg class="w-5 h-5 text-blue-500" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </div>
                    <x-filament::input
                        wire:model.live.debounce.500ms="filters.search"
                        placeholder="Search updates..."
                        type="search"
                        class="w-full pl-12 pr-10 py-3 border border-slate-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition bg-white dark:bg-gray-800 text-slate-900 dark:text-white"
                        style="padding-left: 25px; border: 0.5px solid grey"
                        id="searchInput"
                    />
                    @if(!empty($this->filters['search']))
                        <button
                            wire:click="$set('filters.search', '')"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600 dark:hover:text-gray-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </button>
                    @endif
                </div>
            </div>
            
            <!-- Timeline Content -->
           <div class="relative">
                <!-- Timeline Line with Progress -->
                <div class="absolute left-4 sm:left-1/2 -ml-0.5 w-1 bg-blue-200 dark:bg-blue-800 h-full rounded-full">
                    <div class="absolute top-0 left-0 w-1 bg-blue-500 dark:bg-blue-400 rounded-full transition-all duration-500 ease-out"
                         style="height: {{ $this->updates && $this->updates->count() > 0 ? '100%' : '0%' }};"></div>
                </div>
                <!-- Loading State -->
                <div wire:loading wire:target="loadUpdates,clearFilters,markAsRead" class="space-y-16">
                    @for($i = 0; $i < 3; $i++)
                        <div class="timeline-item sm:space-x-8">
                            <div class="absolute left-4 -ml-5 sm:relative sm:left-auto sm:ml-0 z-10">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-500 ring-8 ring-slate-50 dark:ring-gray-900 animate-pulse">
                                    <div class="w-4 h-4 bg-white rounded-full"></div>
                                </div>
                            </div>
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 w-full ml-12 sm:ml-0 shadow-sm animate-pulse">
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                                    <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-64"></div>
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mt-1 sm:mt-0"></div>
                                </div>
                                <div class="h-6 bg-blue-200 dark:bg-blue-800 rounded-full w-20 mb-4"></div>
                                <div class="space-y-2">
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                                </div>
                            </div>
                        </div>
                    @endfor
                </div>
                <!-- Updates List -->
                <div wire:loading.remove wire:target="loadUpdates,clearFilters" class="space-y-16" id="changelog-container">
                    @forelse($this->updates ?? [] as $update)
                        <div class="timeline-item sm:space-x-8 {{ empty($this->filters['search']) || str_contains(strtolower($update->title . ' ' . $update->summary . ' ' . $update->description . ' ' . $update->content), strtolower($this->filters['search'])) ? '' : 'hidden' }}">
                            <!-- Timeline Dot -->
                            <div class="absolute left-4 -ml-5 sm:relative sm:left-auto sm:ml-0 z-10">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-500 dark:bg-blue-400 ring-8 ring-slate-50 dark:ring-gray-900 timeline-dot transition-transform duration-300 hover:scale-110">
                                    @php
                                        $emoji = match($update->type ?? 'minor') {
                                            'major' => '🎉',
                                            'minor' => '⚡️',
                                            'patch' => '🐛',
                                            'security' => '🔒',
                                            default => '✨'
                                        };
                                    @endphp
                                    <span class="text-xl">{{ $emoji }}</span>
                                </div>
                            </div>
                            
                            <!-- Update Card -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 w-full ml-12 sm:ml-0 shadow-sm changelog-card transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                                <!-- Header -->
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                                    <div class="flex items-center gap-3">
                                        <h2 class="text-xl font-bold text-slate-900 dark:text-white">{{ $update->title }}</h2>
                                        @if(is_array($this->unreadUpdates) && in_array($update->id, $this->unreadUpdates))
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-bold text-white bg-gradient-to-r from-red-500 to-red-600 rounded-full animate-pulse">
                                                NEW
                                            </span>
                                        @endif
                                    </div>
                                    <span class="text-sm font-medium text-slate-500 dark:text-gray-400 mt-1 sm:mt-0">
                                        {{ $update->created_at->format('F j, Y') }}
                                    </span>
                                </div>
                                
                                <!-- Tag -->
                                @php
                                    $tagConfig = match($update->type ?? 'minor') {
                                        'major' => [
                                            'bg' => 'bg-green-100 dark:bg-green-900/30',
                                            'text' => 'text-green-800 dark:text-green-300',
                                            'icon' => 'M12 4.5v15m7.5-7.5h-15',
                                            'label' => 'Added'
                                        ],
                                        'minor' => [
                                            'bg' => 'bg-blue-100 dark:bg-blue-900/30',
                                            'text' => 'text-blue-800 dark:text-blue-300',
                                            'icon' => 'M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z',
                                            'label' => 'Improved'
                                        ],
                                        'patch' => [
                                            'bg' => 'bg-red-100 dark:bg-red-900/30',
                                            'text' => 'text-red-800 dark:text-red-300',
                                            'icon' => 'M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125',
                                            'label' => 'Fixed'
                                        ],
                                        'security' => [
                                            'bg' => 'bg-orange-100 dark:bg-orange-900/30',
                                            'text' => 'text-orange-800 dark:text-orange-300',
                                            'icon' => 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
                                            'label' => 'Security'
                                        ],
                                        default => [
                                            'bg' => 'bg-gray-100 dark:bg-gray-700',
                                            'text' => 'text-gray-800 dark:text-gray-300',
                                            'icon' => 'M12 4.5v15m7.5-7.5h-15',
                                            'label' => 'Updated'
                                        ]
                                    };
                                @endphp
                                
                                {{-- <div class="tag text-xs font-semibold px-3 py-1 rounded-full inline-flex items-center gap-1.5 {{ $tagConfig['bg'] }} {{ $tagConfig['text'] }}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                        <path d="{{ $tagConfig['icon'] }}" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                    <span>{{ $tagConfig['label'] }}</span>
                                </div> --}}
                                <!-- Modern Badges -->
                                <div class="flex flex-wrap items-center gap-2 mb-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {{ match($update->type ?? 'minor') {
                                        'major' => 'bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300 dark:from-green-900/30 dark:to-green-800/30 dark:text-green-300 dark:border-green-700',
                                        'minor' => 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300 dark:from-blue-900/30 dark:to-blue-800/30 dark:text-blue-300 dark:border-blue-700',
                                        'patch' => 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border border-yellow-300 dark:from-yellow-900/30 dark:to-yellow-800/30 dark:text-yellow-300 dark:border-yellow-700',
                                        default => 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300 dark:from-gray-700 dark:to-gray-600 dark:text-gray-300 dark:border-gray-600'
                                    } }}">
                                        {{ strtoupper($update->type ?? 'MINOR') }}
                                    </span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {{ match($update->category ?? 'features') {
                                        'features' => 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border border-emerald-300 dark:from-emerald-900/30 dark:to-emerald-800/30 dark:text-emerald-300 dark:border-emerald-700',
                                        'fixes' => 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 dark:from-red-900/30 dark:to-red-800/30 dark:text-red-300 dark:border-red-700',
                                        'improvements' => 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300 dark:from-blue-900/30 dark:to-blue-800/30 dark:text-blue-300 dark:border-blue-700',
                                        'security' => 'bg-gradient-to-r from-orange-100 to-orange-200 text-orange-800 border border-orange-300 dark:from-orange-900/30 dark:to-orange-800/30 dark:text-orange-300 dark:border-orange-700',
                                        default => 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300 dark:from-gray-700 dark:to-gray-600 dark:text-gray-300 dark:border-gray-600'
                                    } }}">
                                        {{ ucfirst($update->category ?? 'Features') }}
                                    </span>
                                </div>
                                
                                <!-- Content -->
                                <div class="mt-4 text-slate-600 dark:text-gray-300 leading-relaxed">
                                    {{ $update->summary ?? $update->description ?? 'No description available.' }}

                                    <!-- Full Content (Always Visible) -->
                                    @if($update->content && $update->content !== ($update->summary ?? $update->description))
                                        <div class="mt-4 prose prose-sm max-w-none dark:prose-invert bg-gradient-to-br from-slate-50 to-slate-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-4 border border-slate-200 dark:border-gray-600">
                                            {!! $update->processed_content !!}
                                        </div>
                                    @endif
                                </div>
                                 <!-- Mark as Read Button -->
                                @if(is_array($this->unreadUpdates) && in_array($update->id, $this->unreadUpdates))
                                    <div class="mt-6 flex justify-end">
                                        <button
                                            wire:click="markAsRead({{ $update->id }})"
                                            wire:loading.attr="disabled"
                                            wire:target="markAsRead({{ $update->id }})"
                                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105">

                                            <!-- Loading spinner -->
                                            <div wire:loading wire:target="markAsRead({{ $update->id }})" class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>

                                            <!-- Check icon -->
                                            <svg wire:loading.remove wire:target="markAsRead({{ $update->id }})" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>

                                            <span wire:loading.remove wire:target="markAsRead({{ $update->id }})">Mark as Read</span>
                                            <span wire:loading wire:target="markAsRead({{ $update->id }})">Marking...</span>
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                       
                    @empty
                        <!-- No Results State -->
                        <div class="text-center py-16 hidden" id="no-results">
                            <div class="mx-auto w-24 h-24 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center mb-6">
                                <svg class="w-12 h-12 text-slate-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-slate-800 dark:text-white mb-2">No results found</h3>
                            <p class="text-slate-500 dark:text-gray-400 mb-6">Try searching for something else.</p>
                            @if(!empty($this->filters['search']))
                                <button
                                    wire:click="$set('filters.search', '')"
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 rounded-lg transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Clear Search
                                </button>
                            @endif
                        </div>
                    @endforelse
                </div>
           </div>
           <!-- Global Loading Progress Bar -->
            <div wire:loading wire:target="loadUpdates,clearFilters,markAsRead" class="fixed top-0 left-0 right-0 z-50">
                <div class="h-1 bg-blue-200 dark:bg-blue-800">
                    <div class="h-1 bg-blue-500 animate-pulse" style="width: 100%; animation: loading-bar 2s ease-in-out infinite;"></div>
                </div>
            </div>

            <!-- Compact Footer -->
            {{-- @if($this->updates && $this->updates->count() >= 2)
                <div class="text-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a
                        href="{{ route('changelog.index') }}"
                        target="_blank"
                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        View Public Changelog
                    </a>
                </div>
            @endif --}}
        </div>
        
        <!-- Modal -->
        <x-filament::modal id="preferences-modal" width="lg">
            <x-slot name="heading">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2zM4 7h12V5H4v2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notification Preferences</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Customize your update notifications</p>
                    </div>
                </div>
            </x-slot>
            <div class="space-y-6">
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">Stay Updated</h4>
                            <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">Choose which types of updates you'd like to receive email notifications for. You can change these settings anytime.</p>
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <!-- Major Updates -->
                    <div class="group bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-5 hover:shadow-sm transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 dark:text-white">Major Updates</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">New features and significant changes</p>
                                </div>
                            </div>
                            <x-filament::input.checkbox
                                wire:model="email_major_updates"
                                class="ml-4 scale-125"
                            />
                        </div>
                    </div>

                    <!-- Minor Updates -->
                    <div class="group bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-5 hover:shadow-sm transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 dark:text-white">Minor Updates</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Improvements and enhancements</p>
                                </div>
                            </div>
                            <x-filament::input.checkbox
                                wire:model="email_minor_updates"
                                class="ml-4 scale-125"
                            />
                        </div>
                    </div>

                    <!-- Patch Updates -->
                    <div class="group bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-5 hover:shadow-sm transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v-1z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 dark:text-white">Patch Updates</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Bug fixes and small improvements</p>
                                </div>
                            </div>
                            <x-filament::input.checkbox
                                wire:model="email_patch_updates"
                                class="ml-4 scale-125"
                            />
                        </div>
                    </div>

                    <!-- Security Updates -->
                    <div class="group bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800 rounded-xl p-5 hover:shadow-sm transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 dark:text-white">Security Updates</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Security fixes and patches</p>
                                </div>
                            </div>
                            <x-filament::input.checkbox
                                wire:model="email_security_updates"
                                class="ml-4 scale-125"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <x-slot name="footerActions">
                <div class="flex items-center justify-between w-full">
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        Changes are saved automatically
                    </div>
                    <div class="flex items-center gap-3">
                        <x-filament::button
                            x-on:click="$dispatch('close-modal', { id: 'preferences-modal' })"
                            color="gray"
                            size="sm">
                            Cancel
                        </x-filament::button>

                        <x-filament::button
                            wire:click="savePreferences"
                            wire:loading.attr="disabled"
                            color="primary"
                            size="sm"
                            class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                            <div wire:loading wire:target="savePreferences" class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                            <span wire:loading.remove wire:target="savePreferences">Save Preferences</span>
                            <span wire:loading wire:target="savePreferences">Saving...</span>
                        </x-filament::button>
                    </div>
                </div>
            </x-slot>
        </x-filament::modal>
        
        <!-- Custom CSS for Timeline Animations -->
        <style>
        :root {
            --brand-primary: #4f46e5;
            --brand-secondary: #e0e7ff;
            --text-primary: #111827;
            --text-secondary: #4b5563;
            --surface-background: #f8fafc;
            --surface-container: #ffffff;
            --tag-added-bg: #dcfce7;
            --tag-added-text: #16a34a;
            --tag-improved-bg: #dbeafe;
            --tag-improved-text: #2563eb;
            --tag-fixed-bg: #fee2e2;
            --tag-fixed-text: #dc2626;
            --tag-removed-bg: #e5e7eb;
            --tag-removed-text: #4b5563;
        }

        .tag {
            @apply text-xs font-semibold px-3 py-1 rounded-full inline-flex items-center gap-1.5;
        }

        .timeline-item {
            @apply relative flex items-start;
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .timeline-item.hidden {
            opacity: 0;
            transform: translateY(20px);
            height: 0;
            margin: 0;
            overflow: hidden;
        }

        .timeline-dot {
            transition: transform 0.3s ease;
        }

        .timeline-item:hover .timeline-dot {
            transform: scale(1.2);
        }

        .changelog-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .changelog-card:hover {
            transform: translateY(-5px);
            @apply shadow-lg;
        }

        .progress-bar-inner {
            animation: fillProgress 2s ease-out forwards;
        }

        @keyframes fillProgress {
            from { height: 0%; }
            to { height: 100%; }
        }

        @keyframes loading-bar {
            0% { width: 0%; }
            50% { width: 100%; }
            100% { width: 0%; }
        }

        /* Enhanced pulse animation for new updates */
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* Loading skeleton shimmer effect */
        .animate-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Dark mode shimmer */
        .dark .animate-shimmer {
            background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
            background-size: 200% 100%;
        }

        /* Changelog content images */
        .prose img {
            @apply rounded-lg shadow-md max-w-full h-auto;
            margin: 1rem auto;
            display: block;
        }

        .prose img:hover {
            @apply shadow-lg transform scale-105;
            transition: all 0.3s ease;
        }

        /* Ensure images don't break layout */
        .changelog-card .prose {
            overflow-wrap: break-word;
        }

        .changelog-card .prose img {
            max-width: 100%;
            height: auto;
        }
        </style>

        <!-- Timeline Search and Animation Script -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const changelogContainer = document.getElementById('changelog-container');
            const noResultsMessage = document.getElementById('no-results');

            if (searchInput && changelogContainer) {
                const timelineItems = Array.from(changelogContainer.querySelectorAll('.timeline-item'));
                const totalItems = timelineItems.length;

                function filterChangelog() {
                    const searchTerm = searchInput.value.toLowerCase();
                    let visibleItems = 0;

                    timelineItems.forEach(item => {
                        const textContent = item.textContent.toLowerCase();
                        const isVisible = textContent.includes(searchTerm);
                        item.classList.toggle('hidden', !isVisible);

                        if (isVisible) {
                            visibleItems++;
                        }
                    });

                    // Show/hide no results message
                    if (noResultsMessage) {
                        if (visibleItems === 0 && searchTerm.length > 0) {
                            noResultsMessage.classList.remove('hidden');
                        } else {
                            noResultsMessage.classList.add('hidden');
                        }
                    }
                }

                // Listen for Livewire updates
                searchInput.addEventListener('input', filterChangelog);

                // Initial filter
                filterChangelog();
            }
        });
        </script>
    </div>

    <x-filament-actions::modals />
</x-filament-panels::page>