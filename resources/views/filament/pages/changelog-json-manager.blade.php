<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Overview -->
        <x-filament::section>
            <x-slot name="heading">
                📊 Changelog Overview
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                    <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                        {{ count($this->updates) }}
                    </div>
                    <div class="text-sm text-primary-600 dark:text-primary-400">
                        Total Updates
                    </div>
                </div>

                <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ collect($this->updates)->where('type', 'major')->count() }}
                    </div>
                    <div class="text-sm text-green-600 dark:text-green-400">
                        Major Updates
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ collect($this->updates)->where('type', 'minor')->count() }}
                    </div>
                    <div class="text-sm text-blue-600 dark:text-blue-400">
                        Minor Updates
                    </div>
                </div>
            </div>
        </x-filament::section>

        <!-- Updates List -->
        <x-filament::section>
            <x-slot name="heading">
                📝 Recent Updates
            </x-slot>

            <div class="space-y-3">
                @forelse(collect($this->updates)->take(10) as $update)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-3">
                                <h3 class="font-semibold text-gray-900 dark:text-white">
                                    {{ $update['title'] }}
                                </h3>

                                <x-filament::badge
                                    :color="match($update['type']) {
                                        'major' => 'success',
                                        'minor' => 'info',
                                        'patch' => 'warning',
                                        default => 'gray'
                                    }">
                                    {{ ucfirst($update['type']) }}
                                </x-filament::badge>

                                <x-filament::badge
                                    :color="match($update['category']) {
                                        'features' => 'primary',
                                        'fixes' => 'danger',
                                        'improvements' => 'info',
                                        'security' => 'warning',
                                        default => 'gray'
                                    }">
                                    {{ ucfirst($update['category']) }}
                                </x-filament::badge>
                            </div>

                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                v{{ $update['version'] }} • {{ \Carbon\Carbon::parse($update['released_at'])->format('M j, Y') }}
                            </div>
                        </div>

                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            {{ $update['summary'] }}
                        </p>
                    </div>
                @empty
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        No updates found in JSON file
                    </div>
                @endforelse
            </div>
        </x-filament::section>

        <!-- JSON Content -->
        <x-filament::section>
            <x-slot name="heading">
                🔧 Raw JSON Content
            </x-slot>

            <div class="space-y-4">
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <pre class="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap overflow-x-auto max-h-96">{{ $this->jsonContent }}</pre>
                </div>

                <div class="flex gap-2">
                    <x-filament::button
                        wire:click="loadJsonData"
                        color="gray"
                        size="sm">
                        🔄 Refresh
                    </x-filament::button>

                    <x-filament::button
                        onclick="copyToClipboard()"
                        color="gray"
                        size="sm">
                        📋 Copy JSON
                    </x-filament::button>
                </div>
            </div>
        </x-filament::section>

        <!-- Instructions -->
        <x-filament::section>
            <x-slot name="heading">
                📖 Instructions
            </x-slot>

            <div class="prose prose-sm max-w-none dark:prose-invert">
                <h4>Managing the JSON Changelog</h4>
                <ul>
                    <li><strong>Sync from Database:</strong> Import all published updates from the database into the JSON file</li>
                    <li><strong>Sync to Database:</strong> Create/update database records from the JSON changelog (automatically done when importing JSON)</li>
                    <li><strong>Export JSON:</strong> Download the current JSON changelog file</li>
                    <li><strong>Import JSON:</strong> Replace the current changelog with new JSON data and sync to database</li>
                </ul>

                <h4>JSON Structure</h4>
                <p>The changelog JSON file contains:</p>
                <ul>
                    <li><code>version</code>: Schema version</li>
                    <li><code>last_updated</code>: Last modification timestamp</li>
                    <li><code>updates</code>: Array of update objects</li>
                    <li><code>metadata</code>: Additional information about the changelog</li>
                </ul>

                <h4>Update Object Structure</h4>
                <ul>
                    <li><code>id</code>: Unique identifier</li>
                    <li><code>version</code>: Version number (e.g., "2.1.0")</li>
                    <li><code>title</code>: Update title</li>
                    <li><code>summary</code>: Brief description</li>
                    <li><code>content</code>: Full changelog content (Markdown supported)</li>
                    <li><code>type</code>: "major", "minor", or "patch"</li>
                    <li><code>category</code>: "features", "fixes", "improvements", or "security"</li>
                    <li><code>released_at</code>: ISO 8601 timestamp</li>
                    <li><code>metadata</code>: Optional additional data</li>
                </ul>
            </div>
        </x-filament::section>
    </div>

    <script>
        function copyToClipboard() {
            const jsonContent = @js($this->jsonContent);
            navigator.clipboard.writeText(jsonContent).then(function() {
                // Show success notification
                window.dispatchEvent(new CustomEvent('notify', {
                    detail: {
                        type: 'success',
                        message: 'JSON copied to clipboard!'
                    }
                }));
            });
        }
    </script>
</x-filament-panels::page>
