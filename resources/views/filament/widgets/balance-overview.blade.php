{{-- resources/views/filament/widgets/balance-overview.blade.php --}}
    <div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

            <div class="stat">
                <div class="flex items-center gap-x-2">
                    <span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-500 dark:text-gray-400">
                        {{ $this->getStats()[0]['label'] }}
                    </span>
                </div>

                <div
                    class="fi-wi-stats-overview-stat-value text-3xl font-semibold tracking-tight text-gray-950 dark:text-white">
                    {{ $this->getStats()[0]['value'] }}
                </div>
                <small>{{ $this->getStats()[0]['description'] ?? '' }}</small>
            </div>
        </div>
        <div class="flex justify-end mt-4">
            {{ \Filament\Pages\Actions\ButtonAction::make("Add Balance")->icon("heroicon-m-credit-card")->color("success")->url("/checkout") }}
            &nbsp;
            {{ \Filament\Pages\Actions\ButtonAction::make("Spending History")->icon("heroicon-m-document-text")->color("gray")->url("/credit-logs") }}
        </div>
    </div>

