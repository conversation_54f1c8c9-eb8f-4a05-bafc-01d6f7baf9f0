<div
    x-data="{
        show: false,
        maskedPassword: () => {
            let password = '{{ $getState() }}';
            if (password.length <= 3) {
                return '*'.repeat(password.length);
            } else {
                return password.substr(0, 3) + '*'.repeat(password.length - 3);
            }
        }
    }"
    class="flex items-center"
>
    <span style="margin-right: 5px">
        <span x-show="show">{{ $getState() }}</span>
        <span x-show="!show" x-text="maskedPassword"></span>
    </span>

    <x-filament::icon-button
        x-show="show"
        icon="heroicon-m-eye-slash"
        size="xs"
        color="info"
        @click="show = !show"
    />

    <x-filament::icon-button
        x-show="!show"
        icon="heroicon-m-eye"
        size="xs"
        color="info"
        @click="show = !show"
    />

    <div
        title="{{ $getState() }}"
        class="flex-grow cursor-pointer {{ 1 ? 'favorite' : '' }}"
        style="margin-left: 5px"
        x-on:click="
        @include('scripts.copy-to-clipboard', ['text' => $getState()])
        $tooltip('Password Copied', { timeout: 2000 })
    "
    >
        <x-filament::icon-button
            icon="heroicon-m-clipboard"
            color="info"
            size="xs"
            color="success"
            label="Copy to Clipboard"
        />
    </div>
</div>
