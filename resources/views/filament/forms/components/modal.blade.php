<style>
    .prev {
        background: red;
        display: inline-block;
        position: absolute;
        top: 44%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        padding: 8px;
        border-radius: 50%;
        z-index: 6;
        left: 5px;
        cursor: pointer;
        background: #fff;
        box-shadow: 0 3px 6px #1b1b1b29
    }

    .disabled {
        background: #eee;
    }

    .download-button {
        text-align: center;
        cursor: pointer;
        width: 100%;
    }

    .next {
        background: red;
        display: inline-block;
        position: absolute;
        top: 44%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        padding: 8px;
        border-radius: 50%;
        z-index: 6;
        right: 5px;
        cursor: pointer;
        background: #fff;
        box-shadow: 0 3px 6px #1b1b1b29
    }
</style>
@php
    foreach($templates as $template){
        $template->image_url = $template->image ? Storage::drive('s3')->temporaryUrl($template->image, now()->addMinutes(15)) : null;

    }
    foreach($images as $image){
        $image->image_url = $image->src ? Storage::drive('s3')->temporaryUrl($image->src, now()->addMinutes(15)) : null;

    }

    // Get the current background image from the form data
    $currentBackgroundImage = '';
    if (isset($this) && method_exists($this, 'getRecord')) {
        // For edit mode - get from existing campaign
        $record = $this->getRecord();
        if ($record && $record->ebookFormat && $record->ebookFormat->background_image) {
            $currentBackgroundImage = $record->ebookFormat->background_image;
        }
    } elseif (isset($this) && property_exists($this, 'data') && isset($this->data['ebookFormat']['background_image'])) {
        // For create mode or when data is already loaded
        $currentBackgroundImage = $this->data['ebookFormat']['background_image'];
    }

    // Generate temporary URL for the current background image if it exists
    $initialBackgroundImageUrl = '';
    if ($currentBackgroundImage && $currentBackgroundImage !== '' && $currentBackgroundImage !== 'none') {
        try {
            $initialBackgroundImageUrl = Storage::drive('s3')->temporaryUrl($currentBackgroundImage, now()->addMinutes(15));
        } catch (\Exception $e) {
            $initialBackgroundImageUrl = '';
        }
    }

@endphp

<div x-data="{
    showModal: false,
    showBackgroundModal: false,
    backgroundImage: @js($initialBackgroundImageUrl),
    selectedBackgroundId: null,
    templates: {{ Js::from($templates) }},
    images: {{ Js::from($images) }},
    currentPage: 0,
    perPage: window.screen.width > 500 ? 4 : 1,

    // Initialize selected background from current form state
    init() {
        // Wait a moment for Livewire to be ready, then initialize
        this.$nextTick(() => {
            this.initializeSelectedBackground();
        });
    },

    initializeSelectedBackground() {
        // Try multiple ways to get the current background image
        let currentBg = null;

        // Method 1: Direct wire get
        try {
            currentBg = $wire.get('ebookFormat.background_image');
        } catch (e) {
            // Fallback methods if direct wire get fails
        }

        // Method 2: Check if we have data in the component
        if (!currentBg && $wire.data && $wire.data.ebookFormat) {
            currentBg = $wire.data.ebookFormat.background_image;
        }

        // Method 3: Check form state
        if (!currentBg && $wire.form && $wire.form.data && $wire.form.data.ebookFormat) {
            currentBg = $wire.form.data.ebookFormat.background_image;
        }

        // Check if there's actually a background image set
        if (currentBg && currentBg !== '' && currentBg !== null && currentBg !== 'none') {
            // Find the matching image ID
            const matchingImage = this.images.find(img => img.src === currentBg);
            if (matchingImage) {
                this.selectedBackgroundId = matchingImage.id;
                this.backgroundImage = matchingImage.image_url;
            } else {
                // Background image exists but not in our list, still mark as none
                this.selectedBackgroundId = null;
            }
        } else {
            // No image selected (default) - set to 'none' to show tick mark
            this.selectedBackgroundId = 'none';
        }
    },

    selectBackground(imageId, imageSrc, imageUrl) {
        this.selectedBackgroundId = imageId;
        this.backgroundImage = imageUrl;
        $wire.call('setBackgroundImage', imageSrc);
    },

    selectNoBackground() {
        this.selectedBackgroundId = 'none';
        this.backgroundImage = '';
        $wire.call('setBackgroundImage', '');
    },

    openBackgroundModal() {
        // Reinitialize the selected background when modal opens
        this.initializeSelectedBackground();
        this.showBackgroundModal = true;
    },
}">
    <div class="flex">
        <span class="">
            Configure Ebook Options:
        </span>
        <button style="margin-left: 40px;
        padding: 5px 28px; background-color: #ea580c; color: #ffffff;"
            @click.prevent="showModal = true" class="py-2 text-white bg-orange-600 rounded hover:bg-orange-700">
            Select layout
        </button>
        <button style="margin-left: 40px;
        padding: 5px 28px; background-color: #ea580c; color: #ffffff;"
            @click.prevent="openBackgroundModal()" class="py-2 text-white bg-orange-600 rounded hover:bg-orange-700">
            Change Background image
        </button>


    </div>
    {{-- <img style="width:80px;" :src="backgroundImage" alt=""> --}}


    <div x-show="showModal" x-transition
    class="fixed inset-0 z-50 px-4 py-16 overflow-y-auto bg-gray-900 bg-opacity-50 backdrop-blur-sm"
    style="display: none;">

    <div class="relative w-full p-6 mx-auto bg-white border border-gray-200 shadow-2xl rounded-xl max-w-7xl">

        <!-- Header -->
        <div class="flex items-center justify-between pb-4 mb-6 border-b">
            <h1 class="mx-auto text-2xl font-bold text-gray-800">Choose the formatting design that you like the best</h1>
            <button @click.prevent="showModal = false"
                class="text-3xl leading-none text-gray-700 transition top-6 right-6 hover:text-red-500 hover:bg-red-50 rounded-full w-10 h-10 flex items-center justify-center"
                aria-label="Close">
                &times;
            </button>
        </div>

        <!-- Templates -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
            <template x-for="template in templates.slice(currentPage * perPage, (currentPage * perPage) + perPage)"
                :key="template.id">
                <div @click.prevent="showModal = false"
                    wire:click="setDesign(template.id)"
                    class="p-4 overflow-hidden transition-all duration-200 bg-white border border-gray-300 shadow cursor-pointer rounded-xl hover:shadow-xl"
                    :style="{
                        backgroundImage: template.image_url ? `url('${template.image_url}')` : 'none',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        fontFamily: template.font,
                        fontSize: `${template.font_size}px`,
                        lineHeight: template.line_space,
                        textAlign: template.text_align,
                        padding: `${template.margin_top || 20}px ${template.margin_right || 20}px ${template.margin_bottom || 20}px ${template.margin_left || 20}px`
                    }">

                    <h3 class="mb-1 text-lg font-bold text-center"
                        :style="{ fontSize: `${template.heading}px` }">
                        Chapter 6: Planting Your Tree
                    </h3>
                    <h4 class="mb-2 text-sm text-center text-gray-500"
                        :style="{ fontSize: `${template.sub_heading}px` }">
                        Digging the Hole
                    </h4>
                    <p class="leading-snug text-gray-700" :style="{ marginBottom: `${template.paragraph_space}px` }">
                        As we move to the dense forest, a spectacle of nature's ingenuity unfolds before us...
                        The hole must be as wide as the plant's root ball, allowing for ample growth...
                    </p>
                    <p class="leading-snug text-gray-700" :style="{ marginBottom: `${template.paragraph_space}px` }">
                        In the soft, moist earth, the beetle begins its task. With its hind legs, it shovels the soil,
                        flinging it in miniature arcs that scatter in the undergrowth...
                    </p>
                    <p class="leading-snug text-gray-700" :style="{ marginBottom: `${template.paragraph_space}px` }">
                        The process is painstaking, the progress slow. Yet, the beetle does not falter...
                        a cradle and a larder, a
                    </p>
                    <p class="mt-2 text-xs text-right text-gray-500">Page 36</p>
                </div>
            </template>
        </div>

        <!-- Pagination Controls -->
        {{-- <div class="flex items-center justify-between px-4 mt-8">
            <button type="button"
                x-on:click="currentPage = currentPage > 0 ? currentPage - 1 : 0"
                class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 disabled:opacity-40"
                :disabled="currentPage === 0">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path d="M10.828 12l4.95 4.95-1.414 1.414L8 12l6.364-6.364 1.414 1.414z"/>
                </svg>
                <span>Previous</span>
            </button>

            <button type="button"
                x-on:click="currentPage = currentPage < (Math.ceil(templates.length / perPage) - 1) ? currentPage + 1 : currentPage"
                class="flex items-center space-x-2 text-gray-600 hover:text-blue-600"
                :disabled="currentPage >= (Math.ceil(templates.length / perPage) - 1)">
                <span>Next</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path d="M13.172 12l-4.95-4.95 1.414-1.414L16 12l-6.364 6.364-1.414-1.414z"/>
                </svg>
            </button>
        </div> --}}

        <!-- Footer -->
        <div class="flex justify-end mt-6">
            <button @click.prevent="showModal = false"
                class="px-5 py-2 text-white transition bg-gray-700 rounded-lg hover:bg-gray-800">Close</button>
        </div>

    </div>
</div>

    <div x-show="showBackgroundModal" x-transition
    class="fixed inset-0 z-50 px-4 py-16 overflow-auto bg-gray-900 bg-opacity-50 backdrop-blur-sm"
    style="display: none;">

    <div class="relative w-full max-w-5xl p-8 mx-auto bg-white border border-gray-200 shadow-2xl rounded-xl">

        <!-- Header -->
        <div class="flex items-center justify-between pb-6 mb-8 border-b-2 border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span class="text-white text-lg">🎨</span>
                </div>
                <h2 class="text-2xl font-bold text-gray-800">Choose Background Image</h2>
            </div>
            <button @click.prevent="showBackgroundModal = false"
                class="w-10 h-10 flex items-center justify-center text-2xl text-gray-700 transition hover:text-red-500 hover:bg-red-50 rounded-full focus:outline-none"
                aria-label="Close">
                ✕
            </button>
        </div>

        <!-- Image Grid -->
        <div class="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
            <!-- No Image -->
            <div @click.prevent="showBackgroundModal = false; selectNoBackground()"
                class="group relative overflow-hidden transition-all duration-300 bg-white shadow-lg cursor-pointer rounded-3xl hover:shadow-2xl hover:-translate-y-2 hover:scale-105"
                :class="{
                    'ring-4 ring-blue-500 ring-offset-3 shadow-2xl transform -translate-y-2 scale-105': selectedBackgroundId === 'none',
                    'border-2 border-gray-300 hover:border-blue-400': selectedBackgroundId !== 'none'
                }">
                <div class="relative overflow-hidden" style="height: 180px;">
                    <div class="flex items-center justify-center w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-3xl">
                        <div class="text-center">
                            <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-lg">
                                <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <span class="text-sm font-semibold text-gray-700">No Background</span>
                        </div>
                    </div>

                    <!-- Enhanced Selected Indicator -->
                    <div x-show="selectedBackgroundId === 'none'"
                         x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 scale-50"
                         x-transition:enter-end="opacity-100 scale-100"
                         class="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-blue-600/30 backdrop-blur-sm flex items-center justify-center rounded-t-3xl">
                        <div class="bg-black rounded-full w-16 h-16 flex items-center justify-center text-3xl font-black shadow-2xl border-4 border-white ring-4 ring-green-400 animate-pulse">
                            <span class="text-white">✓</span>
                        </div>
                    </div>
                </div>
                <div class="px-4 py-3 text-center border-t-2"
                     :class="{
                         'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-300': selectedBackgroundId === 'none',
                         'bg-gray-50 border-gray-200': selectedBackgroundId !== 'none'
                     }">
                    <span class="text-sm font-bold"
                          :class="{
                              'text-blue-800': selectedBackgroundId === 'none',
                              'text-gray-600': selectedBackgroundId !== 'none'
                          }">Default</span>
                </div>
            </div>

            <!-- Images -->
            <template x-for="image in images" :key="image.id">
                <div @click.prevent="showBackgroundModal = false; selectBackground(image.id, image.src, image.image_url)"
                    class="group relative overflow-hidden transition-all duration-300 bg-white shadow-lg cursor-pointer rounded-3xl hover:shadow-2xl hover:-translate-y-2 hover:scale-105"
                    :class="{
                        'ring-4 ring-blue-500 ring-offset-3 shadow-2xl transform -translate-y-2 scale-105': selectedBackgroundId === image.id,
                        'border-2 border-gray-300 hover:border-blue-400': selectedBackgroundId !== image.id
                    }">
                    <div class="relative overflow-hidden" style="height: 180px;">
                        <div :style="'background-image: url(' + image.image_url + '); background-size: contain; background-position: center center; background-repeat: no-repeat;'"
                             class="w-full h-full transition-all duration-300 group-hover:scale-105 rounded-t-3xl bg-gradient-to-br from-gray-50 to-gray-100"></div>

                        <!-- Enhanced Selected Indicator with better visibility -->
                        <div x-show="selectedBackgroundId === image.id"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 scale-50"
                             x-transition:enter-end="opacity-100 scale-100"
                             class="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-blue-600/30 backdrop-blur-sm flex items-center justify-center rounded-t-3xl">
                            <div class="bg-black rounded-full w-16 h-16 flex items-center justify-center text-3xl font-black shadow-2xl border-4 border-white ring-4 ring-green-400 animate-pulse">
                                <span class="text-white">✓</span>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-3 text-center border-t-2"
                         :class="{
                             'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-300': selectedBackgroundId === image.id,
                             'bg-gray-50 border-gray-200': selectedBackgroundId !== image.id
                         }">
                        <span class="text-sm font-bold"
                              :class="{
                                  'text-blue-800': selectedBackgroundId === image.id,
                                  'text-gray-600': selectedBackgroundId !== image.id
                              }"
                              x-text="'Background ' + image.id"></span>
                    </div>
                </div>
            </template>
        </div>

        <!-- Footer -->
        <div class="flex justify-end mt-8">
            <button @click.prevent="showBackgroundModal = false"
                class="px-5 py-2 text-white transition bg-gray-700 rounded-lg hover:bg-gray-800">Close</button>
        </div>
    </div>
</div>


</div>
