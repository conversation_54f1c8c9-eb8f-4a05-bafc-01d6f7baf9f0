<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8" />
    <meta name="application-name" content="{{ config('app.name') }}" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Edit Book: {{ $campaign->title ?? $campaign->topic }}</title>

    {{-- Load Inter font from Google Fonts --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>

    {{-- Load Filament styles for proper component styling --}}
    @filamentStyles
    @vite(['resources/css/app.css'])
    @vite(['resources/css/filament/admin/theme.css'])

    {{-- Dark mode initialization script (from Filament) --}}
    <script>
        const theme = localStorage.getItem('theme') ?? 'system'

        if (
            theme === 'dark' ||
            (theme === 'system' &&
                window.matchMedia('(prefers-color-scheme: dark)')
                    .matches)
        ) {
            document.documentElement.classList.add('dark')
        }
    </script>
</head>

<body class="antialiased">
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Full-width container for the editing interface -->
        <div class="w-full h-screen">
            <div id="edit-book-app" data-campaign-id="{{ $campaign->id }}"></div>
        </div>
    </div>

    {{-- Load Filament scripts for any dependencies --}}
    @filamentScripts
    @vite(['resources/js/edit-book.js'])
</body>

</html>
