<div>
    <div>
        @if(request()->route()->getName() == 'filament.app.resources.chapters.edit' || request()->route()->getName() == 'edit-book')
        <style>
            .image-skeleton {
            width: 100%;
            max-width: 100%;
            height: 300px; /* or fixed height matching your expected image size */
            background: #eee;
            position: relative;
        }
        
        .image-skeleton.shimmer::after {
            content: "";
            position: absolute;
            top: 0;
            left: -150px;
            width: 100px;
            height: 100%;
            background: linear-gradient(to right, #eee 0%, #f5f5f5 50%, #eee 100%);
            animation: shimmer 1.2s infinite;
        }
        
        @keyframes shimmer {
            100% {
                transform: translateX(300%);
            }
        }
            /* Confirm Modal - Modern Design */
            #confirmModalOverlay {
                display: none;
                position: fixed;
                inset: 0;
                background: rgba(0, 0, 0, 0.4);
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }
        
            #confirmModalOverlay .ai-modal {
                background: #ffffff;
                padding: 32px;
                border-radius: 20px;
                max-width: 420px;
                width: 90%;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                text-align: center;
                animation: fadeInScale 0.3s ease;
            }
        
            #confirmModalOverlay h2 {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 20px;
                color: #222;
            }
        
            #aiGeneratedImagePreview img {
                max-width: 100%;
                height: auto;
                border-radius: 12px;
                margin-bottom: 20px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            }
        
            .ai-modal-buttons {
                display: flex;
                justify-content: center;
                gap: 12px;
            }
        
            .ai-modal-buttons button {
                flex: 1;
                padding: 10px 16px;
                border: none;
                border-radius: 10px;
                font-size: 1rem;
                font-weight: 600;
                cursor: pointer;
                transition: background-color 0.3s;
            }
        
            .ai-modal-buttons .cancel {
                background: #f1f1f1;
                color: #555;
            }
        
            .ai-modal-buttons .cancel:hover {
                background: #e0e0e0;
            }
        
            .ai-modal-buttons .submit {
                background: #22c55e;
                color: white;
            }
        
            .ai-modal-buttons .submit:hover {
                background: #16a34a;
            }
        
            @keyframes fadeInScale {
                0% {
                    opacity: 0;
                    transform: scale(0.9);
                }
                100% {
                    opacity: 1;
                    transform: scale(1);
                }
            }
        </style>
        <style>
            .ai-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }
        
            .ai-modal {
                background: #fff;
                padding: 20px;
                border-radius: 12px;
                width: 90%;
                max-width: 400px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            }
        
            .ai-modal h2 {
                margin-bottom: 10px;
                font-size: 1.25rem;
                color: #333;
            }
        
            #aiTitleInput {
                width: 100%;
                padding: 8px;
                margin-top: 10px;
                border-radius: 6px;
                border: 1px solid #ccc;
            }
        
            .ai-modal-buttons {
                display: flex;
                justify-content: flex-end;
                margin-top: 15px;
            }
        
            .ai-modal-buttons button {
                padding: 6px 12px;
                margin-left: 8px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
            }
        
            .ai-modal-buttons .cancel {
                background: #ccc;
            }
        
            .ai-modal-buttons .submit {
                background: #4caf50;
                color: white;
            }
        
            /* Loader Modal Styles */
            .modern-loader-modal {
                padding: 24px;
                border-radius: 16px;
                background-color: #ffffff;
                width: 90%;
                max-width: 420px;
                text-align: center;
            }
        
            .skeleton-loader {
                display: flex;
                flex-direction: column;
                gap: 12px;
                margin-bottom: 20px;
                align-items: center;
            }
        
            .skeleton-box {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-loading 1.5s infinite;
                border-radius: 8px;
            }
        
            .image-placeholder {
                width: 100px;
                height: 100px;
            }
        
            .text-line {
                width: 80%;
                height: 14px;
            }
        
            .text-line.short {
                width: 60%;
            }
        
            @keyframes skeleton-loading {
                0% {
                    background-position: 200% 0;
                }
                100% {
                    background-position: -200% 0;
                }
            }
        
            /* Confirm Modal image */
            #aiGeneratedImagePreview img {
                max-width: 100%;
                border-radius: 8px;
            }
        </style>
        
        {{-- AI Image Modal --}}
        <div class="ai-modal-overlay" id="aiImageModalOverlay">
            <div class="ai-modal">
                <h2>Generate AI Image</h2>
                <div class="ai-modal-radio">
                    <input type="radio" id="aiImageRadio" name="aiImageRadio" value="content" checked>
                    <label for="aiImageRadio">Using chapter content</label>
                    <br/>
                    <input type="radio" id="aiImageRadio2" name="aiImageRadio" value="keyword">
                    <label for="aiImageRadio2">Using keyword</label>
                </div>
                <textarea style="display: none;z-index:250000;" id="aiTitleInput" placeholder="Enter image title..." ></textarea>
                <div class="ai-modal-buttons">
                    <button class="cancel" onclick="cancelImageModal()">Cancel</button>
                    <button type="button" class="submit" id="submitAiImage">Generate</button>
                </div>
            </div>
        </div>
        
        {{-- AI Content Modal --}}
        <div class="ai-modal-overlay" id="aiContentModalOverlay">
            <div class="ai-modal">
                <h2>Generate AI Content</h2>
                <div class="ai-modal-radio">
                    <input type="radio" id="aiContentRadio" name="aiContentRadio" value="expand" checked>
                    <label for="aiContentRadio">Expand selected text</label>
                    <br/>
                    <input type="radio" id="aiContentRadio2" name="aiContentRadio" value="custom">
                    <label for="aiContentRadio2">Custom prompt</label>
                </div>
                <textarea style="display: none;z-index:250000;" id="aiContentPromptInput" placeholder="Enter your custom prompt..." ></textarea>
                <div class="ai-modal-buttons">
                    <button class="cancel" onclick="cancelContentModal()">Cancel</button>
                    <button type="button" class="submit" id="submitAiContent">Generate</button>
                </div>
            </div>
        </div>
        
        {{-- Loader Modal --}}
        <div class="ai-modal-overlay" id="loaderModalOverlay">
            <div class="ai-modal modern-loader-modal">
                <div class="skeleton-loader">
                    <div class="skeleton-box image-placeholder"></div>
                    <div class="skeleton-box text-line short"></div>
                    <div class="skeleton-box text-line"></div>
                </div>
                <h2>Generating Image...</h2>
                <p>This usually takes a few seconds depending on complexity.</p>
            </div>
        </div>
        
        {{-- Confirm Modal --}}
        <div class="ai-modal-overlay" id="confirmModalOverlay">
            <div class="ai-modal">
                <h2>Confirm Image</h2>
                <div id="aiGeneratedImagePreview" style="margin: 15px 0; text-align: center;">
                    <!-- Image preview will be here -->
                    {{-- <img src="https://blockons.com/wp-content/uploads/2022/11/pageloader.gif" alt=""> --}}
                </div>
                <div class="ai-modal-buttons">
                    <button class="cancel" id="cancelInsertImage">Cancel</button>
                    <button class="submit" id="confirmInsertImage">Insert</button>
                </div>
            </div>
        </div>
        
        <script>
        // Define variables in the global scope so they're accessible everywhere
        var currentEditor = null;
        var generatedImageHTML = '';
        function cancelImageModal(){
            document.getElementById('aiImageModalOverlay').style.display='none';
            let e=document.getElementById("mountedTableActionsData.0.title");
            if(e){
                e.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.style.display="block"
            }
        }
        
        function cancelContentModal(){
            document.getElementById('aiContentModalOverlay').style.display='none';
            let e=document.getElementById("mountedTableActionsData.0.title");
            if(e){
                e.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.style.display="block"
            }
        }
        document.addEventListener('DOMContentLoaded', function () {
            let editors = document.getElementsByTagName("trix-editor");
        
            if (editors.length > 0) {
                setInterval(() => {
                    for (let i = 0; i < editors.length; i++) {
                        let editor = editors[i];
                        if (!editor.hasAttribute("ai-image-button-added")) {
                            loadAIImageButton(editor);
                            editor.setAttribute("ai-image-button-added", "true");
                        }
                        if (!editor.hasAttribute("ai-content-button-added")) {
                            loadAIContentButton(editor);
                            editor.setAttribute("ai-content-button-added", "true");
                        }
                    }
                }, 2000);
            }
        
            const imageRadios = document.querySelectorAll('input[name="aiImageRadio"]');
            imageRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    document.getElementById("aiTitleInput").style.display = this.value === "keyword" ? "block" : "none";
                    if(this.value === "keyword"){
                        let e=document.getElementById("mountedTableActionsData.0.title");
                        if(e){
                            e.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.style.display="none"
                        }
                    }
                });
            });
        
            const contentRadios = document.querySelectorAll('input[name="aiContentRadio"]');
            contentRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    document.getElementById("aiContentPromptInput").style.display = this.value === "custom" ? "block" : "none";
                    if(this.value === "custom"){
                        let e=document.getElementById("mountedTableActionsData.0.title");
                        if(e){
                            e.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.style.display="none"
                        }
                    }
                });
            });
        
            document.getElementById("submitAiImage").addEventListener("click", function () {
                   let e=document.getElementById("mountedTableActionsData.0.title");
                    if(e){
                        e.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.style.display="block"
                    }
                const selectedRadio = document.querySelector('input[name="aiImageRadio"]:checked');
                const selectedValue = selectedRadio ? selectedRadio.value : null;
        
                if (selectedValue === "keyword" && !document.getElementById("aiTitleInput").value) {
                    alert("Please enter a keyword");
                    return;
                }
        
                let content = "";
                console.log("--------------");
                console.log(currentEditor);
                console.log("--------------");
                if (selectedValue === "content" && currentEditor) {
                    // Get the raw content from the Trix editor
                    content = currentEditor.value || currentEditor.innerHTML || "";
                    // If we still don't have content, try another approach
                    if (!content && currentEditor.editor) {
                        content = currentEditor.editor.getDocument().toString();
                    }
                    // Remove HTML tags
                    content = content.replace(/<[^>]+>/g, '');
                    console.log("Editor content:", content);
                } else {
                    content = document.getElementById("aiTitleInput").value;
                }
        
                document.getElementById('aiImageModalOverlay').style.display = 'none';
                document.getElementById('loaderModalOverlay').style.display = 'flex';
        
                fetch("/generate-image", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    },
                    body: JSON.stringify({ text: content }),
                })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then((data) => {
                    document.getElementById('loaderModalOverlay').style.display = 'none';
        
                    const imageUrl = `${data.url}${data.url.includes("?") ? "&optimized=false" : "?optimized=false"}`;
                    generatedImageHTML = `<img src="${imageUrl}" />`;
        
                    const previewContainer = document.getElementById('aiGeneratedImagePreview');
        
                    // Add skeleton
                    previewContainer.innerHTML = `<div class="image-skeleton shimmer" id="imageSkeleton"></div>`;
        
                    // Create image element
                    const img = new Image();
                    img.src = imageUrl;
                    img.alt = "Generated Image";
                    img.style.display = "none"; // Hide until loaded
        
                    img.onload = () => {
                        // Remove skeleton and show image
                        document.getElementById('imageSkeleton').remove();
                        img.style.display = "block";
                        previewContainer.appendChild(img);
                    };
        
                    // Show modal
                    document.getElementById('confirmModalOverlay').style.display = 'flex';
                    document.getElementById('aiGeneratedImagePreview').innerHTML = `<img src="${imageUrl}" alt="Generated Image" />`;
                    document.getElementById('confirmModalOverlay').style.display = 'flex';
                })
                .catch((error) => {
                    console.error("There was a problem with the fetch operation:", error);
                    document.getElementById('loaderModalOverlay').style.display = 'none';
                });
            });
        
            document.getElementById("submitAiContent").addEventListener("click", function () {
                let e=document.getElementById("mountedTableActionsData.0.title");
                if(e){
                    e.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.style.display="block"
                }
                const selectedRadio = document.querySelector('input[name="aiContentRadio"]:checked');
                const selectedValue = selectedRadio ? selectedRadio.value : null;
        
                if (selectedValue === "custom" && !document.getElementById("aiContentPromptInput").value) {
                    alert("Please enter a custom prompt");
                    return;
                }
        
                let content = "";
                let prompt = "";
        
                if (selectedValue === "expand" && currentEditor) {
                    // Get selected text or current content
                    if (currentEditor.editor && currentEditor.editor.getSelectedDocument) {
                        content = currentEditor.editor.getSelectedDocument().toString();
                    }
                    if (!content && currentEditor.editor) {
                        content = currentEditor.editor.getDocument().toString();
                    }
                    content = content.replace(/<[^>]+>/g, '');
                    prompt = `Please expand and improve the following text with more details and better structure: "${content}"`;
                } else {
                    prompt = document.getElementById("aiContentPromptInput").value;
                }
        
                document.getElementById('aiContentModalOverlay').style.display = 'none';
                document.getElementById('loaderModalOverlay').style.display = 'flex';
        
                fetch("/generate-content", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    },
                    body: JSON.stringify({ prompt: prompt }),
                })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then((data) => {
                    document.getElementById('loaderModalOverlay').style.display = 'none';
        
                    if (data.content && currentEditor) {
                        try {
                            // Insert the generated content into the current editor
                            if (currentEditor.editor && typeof currentEditor.editor.insertHTML === 'function') {
                                currentEditor.editor.insertHTML(data.content);
                                currentEditor.focus();
                            } else if (currentEditor.inputElement && currentEditor.inputElement.editor) {
                                currentEditor.inputElement.editor.insertHTML(data.content);
                            } else {
                                // Try to find the editor object
                                for (let key in currentEditor) {
                                    if (currentEditor[key] && typeof currentEditor[key].insertHTML === 'function') {
                                        currentEditor[key].insertHTML(data.content);
                                        break;
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("Error inserting content:", error);
                        }
                    }
                })
                .catch((error) => {
                    console.error("There was a problem with the fetch operation:", error);
                    document.getElementById('loaderModalOverlay').style.display = 'none';
                });
            });
        
            document.getElementById('confirmInsertImage').addEventListener('click', function () {
                console.log("Confirm Insert Image clicked");
                console.log("Current editor:", currentEditor);
                console.log("Generated image HTML:", generatedImageHTML);
        
                if (generatedImageHTML && currentEditor) {
                    // Insert the image into the current editor
                    console.log("Attempting to insert HTML into editor");
                    
        
                    try {
                        // Try different methods to insert the image
                        if (currentEditor.editor && typeof currentEditor.editor.insertHTML === 'function') {
                            console.log("Using editor.insertHTML method");
                            currentEditor.editor.insertHTML(generatedImageHTML);
                            currentEditor.focus();
                        } else if (currentEditor.inputElement && currentEditor.inputElement.editor) {
                            console.log("Using inputElement.editor method");
                            currentEditor.inputElement.editor.insertHTML(generatedImageHTML);
                     
                        } else {
                            // Try to find the editor object
                            console.log("Trying to find editor object");
                            for (let key in currentEditor) {
                                if (currentEditor[key] && typeof currentEditor[key].insertHTML === 'function') {
                                    console.log("Found insertHTML method in property:", key);
                                    currentEditor[key].insertHTML(generatedImageHTML);
                                    break;
                                }
                            }
                        }
                    } catch (error) {
                        console.error("Error inserting HTML:", error);
                    }
                } else {
                    console.error("No current editor or generated image HTML");
                }
                document.getElementById('confirmModalOverlay').style.display = 'none';
            });
        
            document.getElementById('cancelInsertImage').addEventListener('click', function () {
                generatedImageHTML = '';
                document.getElementById('confirmModalOverlay').style.display = 'none';
            });
        });
        
        function loadAIImageButton(editor) {
            let toolbar = editor.parentNode.childNodes[3].childNodes[1];
            let button = document.createElement("button");
            button.id = "ai-image-button-" + Math.random().toString(36).substr(2, 9); // Generate unique ID
            button.type = "button";
            button.className = "tox-tbtn";
            button.setAttribute("title", "AI Image");
        
            button.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2"
                     stroke-linecap="round" stroke-linejoin="round"
                     style="display: block; margin: auto;"
                     class="feather feather-zap">
                  <path d="M13 2L3 14h9l-1 8L21 10h-9l1-8z"></path>
                </svg>
            `;
        
            button.addEventListener("click", function () {
                // Set the current editor when the button is clicked
                console.log("AI Image button clicked for editor:", editor);
                currentEditor = editor;
                console.log("Current editor set to:", currentEditor);
                document.getElementById("aiImageModalOverlay").style.display = "flex";
            });
        
            toolbar.appendChild(button);
        }
        
        function loadAIContentButton(editor) {
            let toolbar = editor.parentNode.childNodes[3].childNodes[1];
            let button = document.createElement("button");
            button.id = "ai-content-button-" + Math.random().toString(36).substr(2, 9); // Generate unique ID
            button.type = "button";
            button.className = "tox-tbtn";
            button.setAttribute("title", "AI Content");
        
            button.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2"
                     stroke-linecap="round" stroke-linejoin="round"
                     style="display: block; margin: auto;"
                     class="feather feather-edit-3">
                  <path d="M12 20h9"></path>
                  <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                </svg>
            `;
        
            button.addEventListener("click", function () {
                // Set the current editor when the button is clicked
                console.log("AI Content button clicked for editor:", editor);
                currentEditor = editor;
                console.log("Current editor set to:", currentEditor);
                document.getElementById("aiContentModalOverlay").style.display = "flex";
            });
        
            toolbar.appendChild(button);
        }
        </script>
        
        @endif
        
    </div>
</div>