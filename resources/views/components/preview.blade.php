<div x-data="{
    show: false,
    formatData: {},
    debugInfo: {},
    init() {
        // Listen for the 'preview' Livewire event. This syntax should be compatible with Filament v3.
        Livewire.on('preview', (eventData) => {
            console.log('Raw event data:', eventData);

            // Check different possible formats of the event data to maintain compatibility
            // with potentially different ways the event might have been dispatched.
            if (Array.isArray(eventData)) {
                const data = eventData.find(item => item && typeof item === 'object' && item.formatData);
                if (data) {
                    this.formatData = data.formatData;
                    this.debugInfo = data.debug || {};
                } else if (eventData[0] && eventData[0].formatData) {
                    this.formatData = eventData[0].formatData;
                    this.debugInfo = eventData[0].debug || {};
                } else if (eventData.formatData) {
                    this.formatData = eventData.formatData;
                    this.debugInfo = eventData.debug || {};
                }
            } else if (eventData && typeof eventData === 'object' && eventData.formatData) {
                this.formatData = eventData.formatData;
                this.debugInfo = eventData.debug || {};
            }


            this.show = true;
            console.log('Processed format data:', this.formatData);
        });
    }
}" x-show="show" x-cloak class="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
    x-on:click.self="show = false">
    <div   :style="{
        backgroundImage: formatData.background_image
? `url('${formatData.background_image}')`
: 'none',
backgroundImageOpacity: formatData.background_opacity,
backgroundSize:formatData.background_image? 'cover':'contain',
backgroundRepeat: formatData.background_image ? 'no-repeat' : 'repeat',
margin:'10px 30px 10px 30px'
// backgroundPosition: 'center',
    }" class="bg-white">
    <div

    class=" rounded-lg  max-w-2xl max-h-[95vh] overflow-auto p-5 w-full">
        <div class="flex items-center justify-between mb-4">
            <span></span>
            {{-- <h2 class="text-lg font-bold" style="text-align: center;">Ebook Format Preview</h2> --}}
            <button x-on:click="show = false" class="text-black-500 hover:text-black-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        {{--
        <div>
            <h3>Debug Information:</h3>
            <pre x-text="JSON.stringify(debugInfo, null, 2)"></pre>
            <h3>Format Data:</h3>
            <pre x-text="JSON.stringify(formatData, null, 2)"></pre>
        </div>
        --}}

        <div class="preview-content" x-bind:style="`
            {{-- background-color: white; --}}
            color: black;
            font-family: ${formatData.font?.replace('_', ' ') || 'serif'}; /* Use serif for a more book-like feel */
            font-size: ${formatData.font_size || '12'}pt; /* Adjust base font size */
            line-height: ${formatData.line_space || '1.6'}; /* Slightly increased line height for readability */
            text-align: ${formatData.text_align || 'left'};
            padding: ${formatData.margins === 'narrow' ? '1rem' : formatData.margins === 'wide' ? '3rem' : '2rem'}; /* Adjust padding for margins */
            {{-- border: 1px solid #ccc; /* Optional: Add a subtle border */ --}}
        `">
            <h1 x-text="formatData.topic" x-bind:style="`font-size: ${formatData.heading || '24'}pt; margin-bottom: 1.5rem; font-weight: bold;`">

            </h1>

            <h2
                x-bind:style="`font-size: ${formatData.sub_heading || '18'}pt; margin-bottom: 1rem; font-weight: bold;`">
                Chapter 1: Introduction
            </h2>

            <p x-bind:style="`margin-bottom: ${formatData.paragraph_space || '18'}px; text-indent: 1.5em;`">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu
                sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor.
            </p>

            <p x-bind:style="`margin-bottom: ${formatData.paragraph_space || '18'}px; text-indent: 1.5em;`">
                Ut in nulla enim. Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse
                dictum feugiat nisl ut dapibus. Mauris iaculis porttitor posuere. Praesent id metus massa, ut blandit
                odio.
            </p>

            <h2
                x-bind:style="`font-size: ${formatData.sub_heading || '18'}pt; margin-bottom: 1rem; font-weight: bold;`">
                Chapter 2: Key Concepts
            </h2>

            <p x-bind:style="`margin-bottom: ${formatData.paragraph_space || '18'}px; text-indent: 1.5em;`">
                Cras id dui. Aenean ut eros et nisl sagittis vestibulum. Nullam nulla eros, ultricies sit amet, nonummy
                id, imperdiet feugiat, pede. Sed lectus. Donec mollis hendrerit risus.
            </p>
            {{-- more dammy content --}}
            <p x-bind:style="`margin-bottom: ${formatData.paragraph_space || '18'}px; text-indent: 1.5em;`">
                Ut in nulla enim. Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse
                dictum feugiat nisl ut dapibus. Mauris iaculis porttitor posuere. Praesent id metus massa, ut blandit
                odio.
            </p>
            <p x-bind:style="`margin-bottom: ${formatData.paragraph_space || '18'}px; text-indent: 1.5em;`">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu
                sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor.
            </p>

        </div>

        {{-- <div class="mt-4 text-right">
            <button x-on:click="show = false" class="px-4 py-2 text-gray-800 bg-gray-200 rounded hover:bg-gray-300">
                Close
            </button>
        </div> --}}
    </div>
    </div>
</div>
