<div class="flex justify-center">
    <div x-data="{
        audioSrc: '{{ Storage::url('audio_sample/' . $model . '.mp3') }}?v=' + Date.now(),
        audio: null,
        isPlaying: false,
        progress: 0,
        duration: 0,
        canvas: null,
        init() {
            this.audio = new Audio(this.audioSrc);
            this.canvas = this.$refs.waveformCanvas;
            this.drawWaveform();
            this.audio.addEventListener('loadedmetadata', () => {
                this.duration = this.audio.duration;
            });
            this.audio.addEventListener('timeupdate', () => {
                this.progress = (this.audio.currentTime / this.duration) * 100;
            });
            this.audio.addEventListener('ended', () => {
                this.isPlaying = false;
            });
        },
        togglePlay() {
            if (this.isPlaying) {
                this.audio.pause();
            } else {
                this.audio.play();
            }
            this.isPlaying = !this.isPlaying;
        },
        seek(event) {
            const rect = event.target.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const width = rect.width;
            const seekTime = (x / width) * this.duration;
            this.audio.currentTime = seekTime;
        },
        drawWaveform() {
            const context = this.canvas.getContext('2d');
            const audioContext = new(window.AudioContext || window.webkitAudioContext)();
    
            fetch(this.audioSrc)
                .then(response => response.arrayBuffer())
                .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
                .then(audioBuffer => {
                    const data = audioBuffer.getChannelData(0);
                    const step = Math.ceil(data.length / this.canvas.width);
                    const amp = this.canvas.height / 2;
    
                    context.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    context.beginPath();
                    context.moveTo(0, amp);
    
                    for (let i = 0; i < this.canvas.width; i++) {
                        let min = 1.0;
                        let max = -1.0;
                        for (let j = 0; j < step; j++) {
                            const datum = data[(i * step) + j];
                            if (datum < min) min = datum;
                            if (datum > max) max = datum;
                        }
                        context.lineTo(i, amp + (min * amp));
                        context.lineTo(i, amp + (max * amp));
                    }
    
                    context.stroke();
                });
        }
    }" style="margin-left:4px;" class="inline-flex items-center ml-2">
        <button type="button" x-on:click="togglePlay()"
            class="inline-flex items-center justify-center px-2 py-1 text-xs font-medium text-gray-700  rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <template class="mr-2" x-if="!isPlaying">
                <svg fill="#000000" width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                    class="mr-2">
                    <path
                        d="M12,1A11,11,0,1,0,23,12,11.013,11.013,0,0,0,12,1Zm0,20a9,9,0,1,1,9-9A9.011,9.011,0,0,1,12,21ZM10,8l6,4-6,4Z" />
                </svg>
            </template>
            <template x-if="isPlaying">
                <svg fill="#000000" width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                    class="mr-2">
                    <path
                        d="M12,1A11,11,0,1,0,23,12,11.013,11.013,0,0,0,12,1Zm0,20a9,9,0,1,1,9-9A9.011,9.011,0,0,1,12,21ZM9,8h2v8H9Zm4,0h2v8H13Z" />
                </svg>
            </template>
            {{-- <span style="margin-left:4px;" x-text="isPlaying ? 'Pause' : 'Play'"></span> --}}
        </button>
        <div x-on:click="seek($event)" class="relative w-64 h-20 bg-gray-100 rounded-md ml-2 cursor-pointer">
            <canvas x-ref="waveformCanvas" class="absolute top-0 left-0 w-full h-full"></canvas>
            <div class="absolute top-0 left-0 h-full bg-indigo-500 rounded-md"
                :style="`width: ${progress}%; opacity: 0.3;`"></div>
        </div>
    </div>
</div>
