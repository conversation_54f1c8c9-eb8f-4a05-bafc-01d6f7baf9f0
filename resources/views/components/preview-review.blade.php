
<div x-data="{
    show: false,
    formatData: {},
    debugInfo: {},
    init() {
        // Listen for the 'preview' Livewire event. This syntax should be compatible with Filament v3.
        Livewire.on('preview-review', (eventData) => {
            console.log('Raw event data:', eventData);

            // Check different possible formats of the event data to maintain compatibility
            // with potentially different ways the event might have been dispatched.
            if (Array.isArray(eventData)) {
                const data = eventData.find(item => item && typeof item === 'object' && item.formatData);
                if (data) {
                    this.formatData = data.formatData;
                    this.debugInfo = data.debug || {};
                } else if (eventData[0] && eventData[0].formatData) {
                    this.formatData = eventData[0].formatData;
                    this.debugInfo = eventData[0].debug || {};
                } else if (eventData.formatData) {
                    this.formatData = eventData.formatData;
                    this.debugInfo = eventData.debug || {};
                }
            } else if (eventData && typeof eventData === 'object' && eventData.formatData) {
                this.formatData = eventData.formatData;
                this.debugInfo = eventData.debug || {};
            }


            this.show = true;
            console.log('Processed format data:', this.formatData);
        });
    }
}" x-show="show" x-cloak class="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
    x-on:click.self="show = false">
        <style>
        /* Review Modal Styles */
        .review-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .review-modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .close-modal {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-modal:hover {
            color: #000;
        }

        .rating-container {
            margin: 20px 0;
        }

        .star-rating {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stars {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }

        .star {
            font-size: 30px;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .star.active {
            color: #FFD700; /* Gold color for active stars */
        }

        .star:hover {
            color: #FFED85; /* Light gold for hover effect */
        }

        .rating-text {
            font-size: 16px;
            font-weight: bold;
            color: #555;
            margin-top: 5px;
        }

        .submit-review {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            width: 100%;
        }

        .submit-review:hover {
            background-color: #c82333;
        }

        .thank-you-message {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .form-control {
            display: block;
            width: 100%;
            padding: 0.5rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            appearance: none;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            margin-bottom: 1rem;
        }
    </style>
    <div class="bg-white">
    <div class="rounded-lg max-h-[95vh] overflow-auto p-5 w-full" style="width: 500px; padding:10px 20px;">
        <div class="flex items-center justify-between mb-4" >
            <span></span>
            <button x-on:click="show = false" class="text-black-500 hover:text-black-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>



        <div class="preview-content">
            <!-- Review Form Preview -->
            <div id="previewReviewForm">
                <h2 x-text="formatData.title || 'We\'d Love Your Feedback!'" style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;"></h2>
                <p x-text="formatData.subtitle || 'Please take a moment to rate your experience with this eBook.'" style="color: #666; margin-bottom: 1.5rem;"></p>

                <form id="previewEbookReviewForm">
                    <!-- Name Field -->
                    <div id="previewNameField" class="mb-3" x-show="formatData.show_name_field !== false">
                        <label for="previewName" class="form-label">Your Name</label>
                        <input type="text" class="form-control"
                            id="previewName" name="name" x-bind:required="formatData.name_field_required !== false">
                    </div>

                    <!-- Email Field -->
                    <div id="previewEmailField" class="mb-3" x-show="formatData.show_email_field !== false">
                        <label for="previewEmail" class="form-label">Your Email</label>
                        <input type="email" class="form-control"
                            id="previewEmail" name="email" x-bind:required="formatData.email_field_required !== false">
                    </div>

                    <!-- Rating Field -->
                    <div id="previewRatingField" class="mb-3" x-show="formatData.show_rating_field !== false">
                        <label class="form-label">Your Rating</label>
                        <div class="star-rating">
                            <input type="hidden" id="previewRating" name="rating" value="5" x-bind:required="formatData.rating_field_required !== false">
                            <div class="stars">
                              {{-- replace icon with svg --}}

<svg class="star active" data-rating="1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#FFD700" viewBox="0 0 24 24">
  <path d="M12 .587l3.668 7.431L24 9.75l-6 5.847 1.417 8.263L12 19.896l-7.417 3.964L6 15.597 0 9.75l8.332-1.732z"/>
</svg>
<svg class="star active" data-rating="2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#FFD700" viewBox="0 0 24 24">
  <path d="M12 .587l3.668 7.431L24 9.75l-6 5.847 1.417 8.263L12 19.896l-7.417 3.964L6 15.597 0 9.75l8.332-1.732z"/>
</svg>
<svg class="star active" data-rating="3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#FFD700" viewBox="0 0 24 24">
  <path d="M12 .587l3.668 7.431L24 9.75l-6 5.847 1.417 8.263L12 19.896l-7.417 3.964L6 15.597 0 9.75l8.332-1.732z"/>
</svg>
<svg class="star active" data-rating="4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#FFD700" viewBox="0 0 24 24">
  <path d="M12 .587l3.668 7.431L24 9.75l-6 5.847 1.417 8.263L12 19.896l-7.417 3.964L6 15.597 0 9.75l8.332-1.732z"/>
</svg>
<svg class="star active" data-rating="5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#FFD700" viewBox="0 0 24 24">
  <path d="M12 .587l3.668 7.431L24 9.75l-6 5.847 1.417 8.263L12 19.896l-7.417 3.964L6 15.597 0 9.75l8.332-1.732z"/>
</svg>

                            </div>
                            <div class="rating-text">Excellent (5/5)</div>
                        </div>
                    </div>

                    <!-- Comment Field -->
                    <div id="previewCommentField" class="mb-3" x-show="formatData.show_comment_field !== false">
                        <label for="previewComment" class="form-label">Comments</label>
                        <textarea class="form-control"
                            id="previewComment" name="comment" rows="3" x-bind:required="formatData.comment_field_required === true"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <button type="button" id="previewSubmitButton"
                        x-text="formatData.submit_button_text || 'Submit Review'"
                        {{-- @click="
                            $el.disabled = true;
                            setTimeout(() => {
                                document.getElementById('previewReviewForm').style.display = 'none';
                                document.getElementById('previewThankYouMessage').style.display = 'block';
                            }, 800);
                        " --}}
                        class="submit-review">
                    </button>
                </form>
            </div>

            <!-- Thank You Message -->
            <div id="previewThankYouMessage" class="hidden text-center">
                <h2 x-text="formatData.thank_you_message || 'Thank You!'" class="text-xl font-bold mb-2"></h2>
                <p class="mb-4">Your feedback has been submitted successfully.</p>
                <button type="button" id="previewCloseThankYou"
                    x-text="formatData.close_button_text || 'Close'"
                    @click="show = false"
                    class="rounded-md bg-primary-600 px-4 py-2 text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                </button>
            </div>
        </div>

    </div>
    </div>
</div>
