@php
    $audioPath = $campaign->getMeta("audio");
    $downloadUrl = '#'; // Default fallback URL

    if ($audioPath) {
        try {
            $downloadUrl = Storage::disk('s3')->temporaryUrl(
                'audio/' . basename($audioPath),
                now()->addDays(3)
            );
        } catch (\Exception $e) {
            Log::error('Error generating S3 temporary URL: ' . $e->getMessage());
            // Optionally, you can set a default url, or display an error message.
            $downloadUrl = url('/campaign/' . $campaign->id . '/edit'); // Redirect to edit campaign
        }
    } else {
        Log::warning('Audio meta data not found for campaign: ' . $campaign->id);
        $downloadUrl = url('/campaign/' . $campaign->id . '/edit'); // Redirect to edit campaign
    }
@endphp

@component('mail::message')

Hey {{ $campaign->user->name }},

Your audiobook **{{ $campaign->topic }}** has been successfully generated! 🎉
You can download your audiobook or review your campaign details using the links below.

@component('mail::button', ['url' => $downloadUrl])
Download Audiobook
@endcomponent

Enjoy listening to your audiobook and make sure to check the campaign details for any updates.

@component('mail::button', ['url' => url('/campaign/' . $campaign->id . '/edit')])
View Campaign
@endcomponent

Thanks,
{{ config('app.name') }}

@endcomponent