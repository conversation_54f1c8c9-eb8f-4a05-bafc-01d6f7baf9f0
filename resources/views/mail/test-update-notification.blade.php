<x-mail::message>
# {{ $updateTypeDisplayName }} - {{ $update->title }}

@if($isTest ?? false)
<x-mail::panel>
🧪 **This is a test email** - This email was sent for testing purposes only.
</x-mail::panel>
@endif

Hey {{ $user->first_name ?? $user->name }},

We're excited to share that **{{ config('app.name') }} v{{ $update->version }}** is now available!

## What's New

{{ $update->summary }}

@if($update->processed_content)
---

{!! $update->processed_content !!}
@endif

<x-mail::panel>
**{{ $categoryDisplayName }}** • Released {{ $update->released_at ? $update->released_at->format('M j, Y') : 'Today' }}
</x-mail::panel>

---

## Quick Links

@if($isTest ?? false)
- [View All Updates](#) *(Test link - not functional)*
- [Update Preferences](#) *(Test link - not functional)*
- [Support](#) *(Test link - not functional)*
@else
- [View All Updates]({{ route('filament.app.pages.changelog-page') }})
- [Update Preferences]({{ route('user.update-preferences') }})
- [Support](/tickets/create)
@endif

---

<x-mail::subcopy>
@if($isTest ?? false)
This is a test email. In a real notification, users would be able to [unsubscribe](#) from these notifications.
@else
You're receiving this because you've subscribed to {{ config('app.name') }} updates. 
You can [unsubscribe]({{ $unsubscribeUrl }}) from these notifications at any time.
@endif
</x-mail::subcopy>

Thanks,<br>
{{ config('app.name') }} Team
</x-mail::message>
