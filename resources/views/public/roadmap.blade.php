<!DOCTYPE html>
<html
    lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="ltr"
    class="fi min-h-screen"
>
<head>
    <meta charset="utf-8" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Product Roadmap - {{ config('app.name') }}</title>
    <meta name="description" content="Explore our product roadmap to see what features we're working on and what's coming next.">

    <style>
        [x-cloak=''],
        [x-cloak='x-cloak'],
        [x-cloak='1'] {
            display: none !important;
        }

        @media (max-width: 1023px) {
            [x-cloak='-lg'] {
                display: none !important;
            }
        }

        @media (min-width: 1024px) {
            [x-cloak='lg'] {
                display: none !important;
            }
        }

        /* Custom styles for roadmap markdown content */
        .roadmap-content h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }

        .dark .roadmap-content h2 {
            color: #f8fafc;
        }

        .roadmap-content ul {
            list-style-type: disc;
            padding-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .roadmap-content li {
            margin-bottom: 0.1rem;
            color: #475569;
        }
        
        .roadmap-content li::marker {
            color: #cbd5e1;
        }

        .dark .roadmap-content li {
            color: #d1d5db;
        }

        .roadmap-content p {
            margin-bottom: 1rem;
            color: #475569;
        }

        .dark .roadmap-content p {
            color: #d1d5db;
        }

        /* Filtering styles */
        .timeline-item.filtered-hidden {
            display: none !important;
        }

        .timeline-line.filtered-hidden {
            display: none !important;
        }

        .no-results-message.filtered-visible {
            display: block !important;
        }
    </style>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        :root {
            --font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        body {
            font-family: var(--font-family);
        }
    </style>

    <!-- Prevent flash of unstyled content -->
    <script>
        // Apply dark mode immediately to prevent flash
        if (localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    </script>

</head>

<body class="fi-body fi-panel-app min-h-screen bg-gray-50 font-normal text-gray-950 antialiased dark:bg-gray-950 dark:text-white" x-data="roadmapApp()">
    <!-- Simple Auth Header -->
    @auth
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-3">
            <div class="max-w-4xl mx-auto px-4 flex justify-between items-center">
                <span class="text-sm text-slate-600 dark:text-gray-400">Welcome, {{ auth()->user()->name }}</span>
                <div class="flex items-center space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button @click="toggleDarkMode()"
                            class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                            title="Toggle dark mode">
                        <svg x-show="!darkMode" class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        <svg x-show="darkMode" class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </button>
                    <a href="{{ url('/') }}" class="text-sm text-slate-600 hover:text-slate-900 dark:text-gray-400 dark:hover:text-white">Dashboard</a>
                    <form method="POST" action="{{ route('filament.app.auth.logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-sm text-slate-600 hover:text-slate-900 dark:text-gray-400 dark:hover:text-white">Logout</button>
                    </form>
                </div>
            </div>
        </div>
    @else
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-3">
            <div class="max-w-4xl mx-auto px-4 flex justify-between items-center">
                <span class="text-sm text-slate-600 dark:text-gray-400">Public Roadmap</span>
                <div class="flex items-center space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button @click="toggleDarkMode()"
                            class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                            title="Toggle dark mode">
                        <svg x-show="!darkMode" class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        <svg x-show="darkMode" class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </button>
                    <a href="{{ route('filament.app.auth.login') }}" class="text-sm text-blue-600 hover:text-blue-700 font-medium">Login</a>
                </div>
            </div>
        </div>
    @endauth

    <!-- Main Content - Exact copy from Filament page -->
    <div class="roadmap-page-wrapper relative flex min-h-screen flex-col items-center bg-slate-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
        <div class="w-full max-w-4xl mx-auto">
            <!-- Header -->
            <header class="text-center mb-10">
                <div class="flex items-center justify-center gap-4 mb-4">
                    <h1 class="text-4xl md:text-5xl font-extrabold tracking-tight text-slate-900 dark:text-white">Product Roadmap</h1>

                    <!-- Status Summary -->
                    <div class="flex items-center gap-2">
                        @php
                            $statusCounts = [
                                'planned' => $roadmapItems->where('status', 'planned')->count(),
                                'in_progress' => $roadmapItems->where('status', 'in_progress')->count(),
                                'completed' => $roadmapItems->where('status', 'completed')->count(),
                                'cancelled' => $roadmapItems->where('status', 'cancelled')->count(),
                            ];
                        @endphp
                        @if($statusCounts['in_progress'] > 0)
                            <div class="inline-flex items-center px-3 py-2 bg-orange-500 text-white text-sm font-medium rounded-full shadow-lg">
                                <div class="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                                {{ $statusCounts['in_progress'] }} In Progress
                            </div>
                        @endif

                        @if($statusCounts['planned'] > 0)
                            <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm font-medium rounded-full shadow-lg">
                                {{ $statusCounts['planned'] }} Planned
                            </div>
                        @endif
                    </div>
                </div>

                <p class="mt-4 text-sm text-slate-500 dark:text-gray-400 max-w-2xl mx-auto">
                    Here's what we're working on and what's coming next. Stay tuned for exciting updates!
                </p>

                <!-- Quick Stats -->
                <div class="mt-6 flex justify-center gap-6 text-sm text-slate-600 dark:text-gray-400">
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <span>{{ $statusCounts['planned'] + $statusCounts['in_progress'] }} Upcoming</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>{{ $statusCounts['completed'] }} Completed</span>
                    </div>
                </div>
            </header>

            <!-- Search and Filters -->
            <div class="mb-8 space-y-4">
                <!-- Submit Request Button -->
                @auth
                    @if(auth()->user()->hasValidSubscription())
                        <div class="flex justify-center mb-6">
                            <button @click="showRequestModal = true"
                                    class="inline-flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                Submit Request
                            </button>
                        </div>
                    @endif
                @endauth

                <!-- Search Bar -->
                <div class="w-full max-w-2xl mx-auto">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none pl-3">
                            <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </div>
                        <input type="text"
                               x-model="filters.search"
                               @input.debounce.300ms="applyFilters()"
                               placeholder="Search roadmap items..."
                               class="w-full pr-10 py-3 border border-slate-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition bg-white dark:bg-gray-800 text-slate-900 dark:text-white"
                               style="padding-left: 45px;">
                        <button x-show="filters.search"
                                @click="filters.search = ''; applyFilters()"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600 dark:hover:text-gray-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Filter Buttons -->
                <div class="flex flex-wrap justify-center gap-2 px-4">
                    <!-- Status Filters -->
                    <button @click="setStatusFilter('')"
                            :class="filters.status === '' ? 'bg-blue-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600'"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap">
                        All
                    </button>
                    <button @click="setStatusFilter('in_progress')"
                            :class="filters.status === 'in_progress' ? 'bg-orange-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600'"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap">
                        ⚡ In Progress
                    </button>
                    <button @click="setStatusFilter('planned')"
                            :class="filters.status === 'planned' ? 'bg-blue-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600'"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap">
                        📋 Planned
                    </button>
                    <button @click="setStatusFilter('completed')"
                            :class="filters.status === 'completed' ? 'bg-green-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600'"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap">
                        ✅ Completed
                    </button>

                    <!-- Category Filters -->
                    <div class="w-px h-6 bg-slate-300 dark:bg-gray-600 mx-2"></div>
                    <button @click="setCategoryFilter('features')"
                            :class="filters.category === 'features' ? 'bg-purple-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600'"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap">
                        🚀 Features
                    </button>
                    <button @click="setCategoryFilter('improvements')"
                            :class="filters.category === 'improvements' ? 'bg-yellow-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600'"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap">
                        ⚡ Improvements
                    </button>
                    <button @click="setCategoryFilter('fixes')"
                            :class="filters.category === 'fixes' ? 'bg-red-500 text-white shadow-lg' : 'bg-white dark:bg-gray-800 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-700 border border-slate-200 dark:border-gray-600'"
                            class="px-3 py-1 text-sm rounded-full transition whitespace-nowrap">
                        🐛 Fixes
                    </button>

                    <button x-show="filters.status || filters.category || filters.search"
                            @click="clearFilters()"
                            class="px-3 py-1 text-sm rounded-full bg-gray-500 text-white hover:bg-gray-600 transition shadow-lg whitespace-nowrap">
                        Clear All
                    </button>
                </div>
            </div>

            <!-- Timeline Content -->
            <div class="relative">
                <!-- Timeline Line (only show when there are items) -->
                @if($roadmapItems && $roadmapItems->count() > 0)
                    <div class="timeline-line absolute left-4 sm:left-1/2 -ml-0.5 w-1 bg-blue-200 dark:bg-blue-800 h-full rounded-full">
                        <div class="absolute top-0 left-0 w-1 bg-blue-500 dark:bg-blue-400 rounded-full transition-all duration-500 ease-out" style="height: 100%;"></div>
                    </div>
                @endif

                <!-- Roadmap Items List -->
                <div class="space-y-16" id="roadmap-container">
                    @forelse($roadmapItems as $item)
                        <div class="timeline-item sm:space-x-8"
                             data-status="{{ $item->status }}"
                             data-category="{{ $item->category }}"
                             data-priority="{{ $item->priority ?? '' }}">
                            <!-- Timeline Dot -->
                            <div class="absolute left-4 -ml-5 sm:relative sm:left-auto sm:ml-0 z-10">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full ring-8 ring-slate-50 dark:ring-gray-900 transition-all duration-300 hover:scale-110 timeline-dot-{{ $item->status }}">
                                    <span class="text-white text-sm font-bold">
                                        @if($item->status === 'in_progress')
                                            ⚡
                                        @else
                                            {{ $item->status_emoji ?? '📋' }}
                                        @endif
                                    </span>
                                </div>
                            </div>

                            <!-- Roadmap Item Card -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 w-full ml-12 sm:ml-0 shadow-sm roadmap-card transition-all duration-300 hover:shadow-lg hover:-translate-y-1 relative
                                {{ isset($item->is_overdue) && $item->is_overdue && $item->status !== 'completed' ? 'border-l-4 border-red-500' : '' }}
                                {{ isset($item->priority) && $item->priority === 'high' ? 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800' : '' }}
                                {{ isset($item->priority) && $item->priority === 'critical' ? 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800' : '' }}">

                                <!-- High Priority Corner Badge -->
                                @if(isset($item->priority) && ($item->priority === 'high' || $item->priority === 'critical'))
                                    <div class="absolute top-0 right-0 -mt-2 -mr-2">
                                        <div class="inline-flex items-center px-2 py-1 text-xs font-bold rounded-full shadow-lg
                                            {{ $item->priority === 'critical' ? 'bg-red-500 text-white animate-pulse' : '' }}
                                            {{ $item->priority === 'high' ? 'bg-orange-500 text-white' : '' }}">
                                            {{ $item->priority === 'critical' ? '🔥 CRITICAL' : '⚡ HIGH' }}
                                        </div>
                                    </div>
                                @endif

                                <!-- Header -->
                                <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-2 flex-wrap">
                                            <h2 class="text-xl font-bold text-slate-900 dark:text-white">{{ $item->title }}</h2>

                                            <!-- Status Badge -->
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full status-badge-{{ $item->status }}">
                                                {{ $item->status === 'in_progress' ? 'In Progress' : ucfirst(str_replace('_', ' ', $item->status)) }}
                                            </span>

                                            <!-- Timeframe Badge (Eye-catching) -->
                                            @if($item->estimated_date && $item->status !== 'completed')
                                                <div class="inline-flex items-center px-2 py-2 text-xs font-black rounded-full date-badge-enhanced {{ isset($item->is_overdue) && $item->is_overdue ? 'timeframe-badge-overdue' : '' }}">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    @if(isset($item->is_overdue) && $item->is_overdue)
                                                        OVERDUE
                                                    @else
                                                        {{ $item->formatted_estimated_date }}
                                                    @endif
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Category and Additional Info -->
                                        <div class="flex items-center gap-4 text-sm">
                                            <span class="flex items-center gap-1 font-medium text-slate-800 dark:text-gray-100">
                                                {{ $item->category_emoji ?? '🚀' }}
                                                {{ ucfirst($item->category ?? 'Feature') }}
                                            </span>

                                            @if($item->estimated_date && (!isset($item->is_overdue) || !$item->is_overdue) && $item->status !== 'completed')
                                                <span class="flex items-center gap-1 text-blue-700 dark:text-blue-300 font-medium">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                                    </svg>
                                                    {{ $item->time_until }}
                                                </span>
                                            @endif

                                            <!-- Like Button -->
                                            <div class="flex items-center gap-1">
                                                @auth
                                                    <button @click="toggleLike({{ $item->id }})"
                                                            :disabled="likingItem === {{ $item->id }}"
                                                            class="like-button group flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-200 hover:bg-red-50 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed {{ $item->likes->where('user_id', auth()->id())->count() > 0 ? 'liked' : '' }}">
                                                        <svg class="heart-icon w-4 h-4 transition-colors duration-200 {{ $item->likes->where('user_id', auth()->id())->count() > 0 ? 'text-red-500 fill-current liked' : 'text-gray-400 group-hover:text-red-500' }}"
                                                             :fill="likedItems.includes({{ $item->id }}) ? 'currentColor' : 'none'"
                                                             stroke="currentColor"
                                                             viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                        </svg>
                                                        <span class="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-red-500"
                                                              x-text="likeCounts[{{ $item->id }}] || {{ $item->likes_count ?? 0 }}">
                                                            {{ $item->likes_count ?? 0 }}
                                                        </span>
                                                        <span x-show="likingItem === {{ $item->id }}" class="ml-1">
                                                            <svg class="animate-spin w-3 h-3 text-gray-400" fill="none" viewBox="0 0 24 24">
                                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                            </svg>
                                                        </span>
                                                    </button>
                                                @else
                                                    <!-- Non-authenticated users see count only -->
                                                    <div class="flex items-center gap-1 px-2 py-1">
                                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                        </svg>
                                                        <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                                            {{ $item->likes_count ?? 0 }}
                                                        </span>
                                                    </div>
                                                @endauth
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Progress Bar for In Progress Items -->
                                @if($item->status === 'in_progress' && isset($item->metadata['progress']))
                                    <div class="mb-6 p-4 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                                        <div class="flex justify-between items-center text-sm font-medium text-slate-800 dark:text-gray-200 mb-3">
                                            <span class="flex items-center gap-2">
                                                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                </svg>
                                                Progress
                                            </span>
                                            <span class="text-orange-600 dark:text-orange-400 font-bold">{{ $item->metadata['progress'] ?? 0 }}%</span>
                                        </div>
                                        <div class="relative">
                                            <div class="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-3 shadow-inner">
                                                <div class="bg-orange-500 h-3 rounded-full transition-all duration-500 ease-out shadow-sm progress-bar-shimmer"
                                                     style="width: {{ $item->metadata['progress'] ?? 0 }}%">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Summary -->
                                <div class="mb-6">
                                    <p class="text-slate-700 dark:text-gray-300 leading-relaxed item-summary">{{ $item->summary }}</p>
                                </div>

                                <!-- Content -->
                                @if($item->content)
                                    <div class="mb-4 roadmap-content">
                                        @php
                                            $markdownContent = \Illuminate\Support\Str::markdown($item->content);
                                        @endphp
                                        {!! $markdownContent !!}
                                    </div>
                                @endif
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-16">
                            <div class="mx-auto w-24 h-24 bg-slate-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                                <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">No roadmap items found</h3>
                            <p class="text-slate-600 dark:text-gray-400 mb-6">Try adjusting your search or filters to see more items.</p>
                            <button @click="clearFilters()" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Clear Filters
                            </button>
                        </div>
                    @endforelse

                    <!-- Dynamic No Results Message (hidden by default) -->
                    <div class="no-results-message text-center py-16" style="display: none;">
                        <div class="mx-auto w-24 h-24 bg-slate-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                            <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">No items match your filters</h3>
                        <p class="text-slate-600 dark:text-gray-400 mb-6">Try adjusting your search terms or filters to see more results.</p>
                        <button @click="clearFilters()" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Clear Filters
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submit Request Modal -->
    @auth
        @if(auth()->user()->hasValidSubscription())
            <div x-show="showRequestModal" 
                 x-cloak
                 class="fixed inset-0 z-50 overflow-y-auto"
                 style="display: none;">
                
                <!-- Backdrop -->
                <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" 
                     x-show="showRequestModal" 
                     x-transition:enter="ease-out duration-300" 
                     x-transition:enter-start="opacity-0" 
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in duration-200" 
                     x-transition:leave-start="opacity-100" 
                     x-transition:leave-end="opacity-0"
                     @click="showRequestModal = false"></div>

                <!-- Modal -->
                <div class="flex min-h-full items-center justify-center p-4">
                    <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md transform transition-all"
                         x-show="showRequestModal"
                         x-transition:enter="ease-out duration-300"
                         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                         x-transition:leave="ease-in duration-200"
                         x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                         x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                        
                        <!-- Modal Header -->
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Submit Request</h3>
                                <button @click="showRequestModal = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Modal Body -->
                        <form @submit.prevent="submitRequest()" class="p-6 space-y-4">
                            <!-- Title Field -->
                            <div>
                                <label for="request_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="request_title"
                                       x-model="requestForm.title"
                                       placeholder="Enter a descriptive title for your request"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                       maxlength="255"
                                       required>
                            </div>

                            <!-- Content Field -->
                            <div>
                                <label for="request_content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Description <span class="text-red-500">*</span>
                                </label>
                                <textarea id="request_content"
                                          x-model="requestForm.content"
                                          placeholder="Provide detailed information about your request, including use cases and benefits"
                                          rows="6"
                                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                                          required></textarea>
                            </div>

                            <!-- Type Field -->
                            <div>
                                <label for="request_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Type <span class="text-red-500">*</span>
                                </label>
                                <select id="request_type"
                                        x-model="requestForm.type"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        required>
                                    <option value="">Select type...</option>
                                    <option value="feature">🚀 Feature</option>
                                    <option value="improvement">⚡ Improvement</option>
                                    <option value="bug">🐛 Bug Fix</option>
                                </select>
                            </div>

                            <!-- Modal Footer -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                                <button type="button" 
                                        @click="showRequestModal = false"
                                        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                    Cancel
                                </button>
                                <button type="submit"
                                        :disabled="submittingRequest"
                                        class="px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span x-show="!submittingRequest">Submit Request</span>
                                    <span x-show="submittingRequest" class="flex items-center">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Submitting...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endif
    @endauth

    <!-- JavaScript -->
    <script>
        function roadmapApp() {
            return {
                darkMode: localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches),
                showRequestModal: false,
                submittingRequest: false,
                likingItem: null,
                likedItems: @json(auth()->check() ? $roadmapItems->filter(fn($item) => $item->likes->where('user_id', auth()->id())->count() > 0)->pluck('id')->toArray() : []),
                likeCounts: @json($roadmapItems->pluck('likes_count', 'id')->toArray()),
                filters: {
                    search: '{{ $filters['search'] }}',
                    status: '{{ $filters['status'] }}',
                    category: '{{ $filters['category'] }}',
                    priority: '{{ $filters['priority'] }}'
                },
                requestForm: {
                    title: '',
                    content: '',
                    type: ''
                },

                init() {
                    this.updateDarkMode();
                    this.$watch('darkMode', () => this.updateDarkMode());
                    // Apply initial filters if any exist in URL
                    if (this.filters.search || this.filters.status || this.filters.category) {
                        this.filterRoadmapItems();
                    }
                },

                updateDarkMode() {
                    if (this.darkMode) {
                        document.documentElement.classList.add('dark');
                        localStorage.setItem('theme', 'dark');
                    } else {
                        document.documentElement.classList.remove('dark');
                        localStorage.setItem('theme', 'light');
                    }
                },

                toggleDarkMode() {
                    this.darkMode = !this.darkMode;
                },

                setStatusFilter(status) {
                    this.filters.status = status;
                    this.applyFilters();
                },

                setCategoryFilter(category) {
                    this.filters.category = category;
                    this.applyFilters();
                },

                clearFilters() {
                    this.filters = {
                        search: '',
                        status: '',
                        category: '',
                        priority: ''
                    };
                    this.applyFilters();
                },

                applyFilters() {
                    // Update URL without reloading
                    const params = new URLSearchParams();
                    Object.keys(this.filters).forEach(key => {
                        if (this.filters[key]) {
                            params.set(key, this.filters[key]);
                        }
                    });

                    const url = new URL(window.location);
                    url.search = params.toString();
                    window.history.pushState({}, '', url);

                    // Apply filters to roadmap items
                    this.filterRoadmapItems();
                },

                filterRoadmapItems() {
                    const items = document.querySelectorAll('.timeline-item');
                    const timelineLine = document.querySelector('.timeline-line');
                    const noResultsMessage = document.querySelector('.no-results-message');
                    let visibleCount = 0;

                    // Filter items using CSS classes
                    items.forEach(item => {
                        const title = item.querySelector('h2')?.textContent?.toLowerCase() || '';
                        const summary = item.querySelector('.item-summary')?.textContent?.toLowerCase() || '';
                        const content = item.querySelector('.roadmap-content')?.textContent?.toLowerCase() || '';
                        const status = item.dataset.status || '';
                        const category = item.dataset.category || '';

                        // Check search filter
                        const searchMatch = !this.filters.search ||
                            title.includes(this.filters.search.toLowerCase()) ||
                            summary.includes(this.filters.search.toLowerCase()) ||
                            content.includes(this.filters.search.toLowerCase());

                        // Check status filter
                        const statusMatch = !this.filters.status || status === this.filters.status;

                        // Check category filter
                        const categoryMatch = !this.filters.category || category === this.filters.category;

                        // Show/hide item using CSS classes
                        if (searchMatch && statusMatch && categoryMatch) {
                            item.classList.remove('filtered-hidden');
                            visibleCount++;
                        } else {
                            item.classList.add('filtered-hidden');
                        }
                    });

                    // Show/hide timeline line and no results message
                    if (visibleCount === 0) {
                        if (timelineLine) {
                            timelineLine.classList.add('filtered-hidden');
                        }
                        if (noResultsMessage) {
                            noResultsMessage.classList.add('filtered-visible');
                        }
                    } else {
                        if (timelineLine) {
                            timelineLine.classList.remove('filtered-hidden');
                        }
                        if (noResultsMessage) {
                            noResultsMessage.classList.remove('filtered-visible');
                        }
                    }
                },

                async toggleLike(itemId) {
                    if (this.likingItem) return;
                    
                    this.likingItem = itemId;
                    
                    try {
                        const response = await fetch(`/roadmap/like/${itemId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                            }
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            this.likeCounts[itemId] = data.like_count;
                            
                            if (data.is_liked) {
                                if (!this.likedItems.includes(itemId)) {
                                    this.likedItems.push(itemId);
                                }
                            } else {
                                this.likedItems = this.likedItems.filter(id => id !== itemId);
                            }
                            
                            this.showNotification(data.message, 'success');
                        } else {
                            this.showNotification(data.message, 'error');
                        }
                    } catch (error) {
                        this.showNotification('An error occurred. Please try again.', 'error');
                    } finally {
                        this.likingItem = null;
                    }
                },

                async submitRequest() {
                    if (this.submittingRequest) return;
                    
                    this.submittingRequest = true;
                    
                    try {
                        const response = await fetch('/roadmap/submit-request', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                            },
                            body: JSON.stringify(this.requestForm)
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            this.showNotification(data.message, 'success');
                            this.requestForm = { title: '', content: '', type: '' };
                            this.showRequestModal = false;
                        } else {
                            this.showNotification(data.message, 'error');
                        }
                    } catch (error) {
                        this.showNotification('An error occurred. Please try again.', 'error');
                    } finally {
                        this.submittingRequest = false;
                    }
                },

                showNotification(message, type) {
                    // Simple notification - you can enhance this
                    const notification = document.createElement('div');
                    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white`;
                    notification.textContent = message;
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.remove();
                    }, 3000);
                }
            }
        }
    </script>

    <!-- Exact CSS from original Filament roadmap page -->
    <style>
        .roadmap-card {
            border: 1px solid rgba(148, 163, 184, 0.1);
            transition: all 0.3s ease;
        }

        .roadmap-card:hover {
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .timeline-item {
            position: relative;
            display: flex;
            align-items: flex-start;
        }

        @media (max-width: 640px) {
            .timeline-item {
                padding-left: 0;
            }
        }

        /* Timeline dot colors */
        .timeline-dot-completed {
            background-color: #10b981 !important; /* green-500 */
        }

        .timeline-dot-in_progress {
            background-color: #f97316 !important; /* orange-500 */
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .timeline-dot-planned {
            background-color: #3b82f6 !important; /* blue-500 */
        }

        .timeline-dot-cancelled {
            background-color: #9ca3af !important; /* gray-400 */
        }

        /* High priority card animations */
        .roadmap-card.high-priority {
            animation: subtle-glow 3s ease-in-out infinite alternate;
        }

        @keyframes subtle-glow {
            from {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }
            to {
                box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05);
            }
        }

        /* Like button enhanced animations */
        .like-button {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .like-button:hover {
            transform: scale(1.05);
        }

        .like-button:active {
            transform: scale(0.95);
        }

        .like-button.liked .heart-icon {
            animation: heart-beat 0.6s ease-in-out;
        }

        @keyframes heart-beat {
            0% {
                transform: scale(1);
            }
            25% {
                transform: scale(1.2);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        .heart-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .heart-icon:hover {
            transform: scale(1.1);
        }

        .heart-icon.liked {
            color: #ef4444 !important;
            fill: #ef4444 !important;
        }

        .heart-icon:not(.liked):hover {
            color: #ef4444 !important;
            transform: translateY(-2px);
        }

        /* Enhanced progress bar animations */
        .progress-bar-enhanced {
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .progress-bar-enhanced::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* Enhanced date badge styling to match progress bar */
        .date-badge-enhanced {
            background: linear-gradient(135deg, #f97316, #eab308) !important;
            color: white !important;
        }

        .dark .date-badge-enhanced {
            background: linear-gradient(135deg, #f97316, #eab308) !important;
        }

        /* Better text contrast for light mode */
        .roadmap-card .text-slate-500 {
            color: rgb(71 85 105) !important; /* slate-600 equivalent */
        }

        .dark .roadmap-card .text-slate-500 {
            color: rgb(203 213 225) !important;
        }

        .roadmap-card .text-slate-600 {
            color: rgb(51 65 85) !important; /* slate-700 equivalent */
        }

        .dark .roadmap-card .text-slate-600 {
            color: rgb(226 232 240) !important;
        }

        .roadmap-card .text-slate-700 {
            color: rgb(51 65 85) !important; /* slate-700 */
        }

        .dark .roadmap-card .text-slate-700 {
            color: rgb(241 245 249) !important; /* slate-100 */
        }

        .roadmap-card .text-slate-800 {
            color: rgb(30 41 59) !important; /* slate-800 */
        }

        .dark .roadmap-card .text-slate-800 {
            color: rgb(248 250 252) !important; /* slate-50 */
        }

        /* Ensure prose content is visible */
        .roadmap-card .prose {
            color: rgb(51 65 85) !important; /* slate-700 */
        }

        .dark .roadmap-card .prose {
            color: rgb(226 232 240) !important; /* slate-200 */
        }

        .roadmap-card .prose h1,
        .roadmap-card .prose h2,
        .roadmap-card .prose h3,
        .roadmap-card .prose h4,
        .roadmap-card .prose h5,
        .roadmap-card .prose h6 {
            color: rgb(30 41 59) !important; /* slate-800 */
        }

        .dark .roadmap-card .prose h1,
        .dark .roadmap-card .prose h2,
        .dark .roadmap-card .prose h3,
        .dark .roadmap-card .prose h4,
        .dark .roadmap-card .prose h5,
        .dark .roadmap-card .prose h6 {
            color: rgb(248 250 252) !important; /* slate-50 */
        }

        .roadmap-card .prose p,
        .roadmap-card .prose li {
            color: rgb(51 65 85) !important; /* slate-700 */
        }

        .dark .roadmap-card .prose p,
        .dark .roadmap-card .prose li {
            color: rgb(203 213 225) !important; /* slate-300 */
        }

        /* Status badge colors */
        .status-badge-planned {
            background-color: #dbeafe !important;
            color: #1d4ed8 !important;
        }

        .status-badge-in_progress {
            background-color: #fed7aa !important;
            color: #ea580c !important;
        }

        .status-badge-completed {
            background-color: #dcfce7 !important;
            color: #16a34a !important;
        }

        .status-badge-cancelled {
            background-color: #f3f4f6 !important;
            color: #6b7280 !important;
        }

        .dark .status-badge-planned {
            background-color: #1e3a8a !important;
            color: #93c5fd !important;
        }

        .dark .status-badge-in_progress {
            background-color: #9a3412 !important;
            color: #fdba74 !important;
        }

        .dark .status-badge-completed {
            background-color: #166534 !important;
            color: #86efac !important;
        }

        .dark .status-badge-cancelled {
            background-color: #374151 !important;
            color: #9ca3af !important;
        }

        /* Progress bar shimmer effect */
        .progress-bar-shimmer {
            position: relative;
            overflow: hidden;
        }

        .progress-bar-shimmer::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }
    </style>
</body>
</html>
