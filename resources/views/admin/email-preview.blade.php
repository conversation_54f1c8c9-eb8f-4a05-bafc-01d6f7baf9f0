<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Preview - {{ $update->title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="max-w-6xl mx-auto py-8 px-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        📧 Email Preview
                    </h1>
                    <p class="text-gray-600 mt-1">
                        Preview how the email notification will look for: <strong>{{ $update->title }}</strong>
                    </p>
                </div>
                
                <div class="flex gap-3">
                    <button onclick="window.close()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Close
                    </button>
                    
                    <button onclick="window.print()" 
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        🖨️ Print
                    </button>
                </div>
            </div>
        </div>

        <!-- Email Details -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">📋 Email Details</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Email Information</h3>
                    <dl class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <dt class="text-gray-500">Subject:</dt>
                            <dd class="text-gray-900 font-medium">
                                @if(is_object($mailable) && method_exists($mailable, 'envelope'))
                                    {{ $mailable->envelope()->subject }}
                                @else
                                    <span class="">Error: Mailable object not available</span>
                                @endif
                            </dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500">From:</dt>
                            <dd class="text-gray-900">{{ config('mail.from.name') }} &lt;{{ config('mail.from.address') }}&gt;</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500">To:</dt>
                            <dd class="text-gray-900">{{ $sampleUser->email }}</dd>
                        </div>
                    </dl>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Update Information</h3>
                    <dl class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <dt class="text-gray-500">Version:</dt>
                            <dd class="text-gray-900">v{{ $update->version }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500">Type:</dt>
                            <dd>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $update->type === 'major' ? 'bg-green-100 text-green-800' : '' }}
                                    {{ $update->type === 'minor' ? 'bg-blue-100 text-blue-800' : '' }}
                                    {{ $update->type === 'patch' ? 'bg-yellow-100 text-yellow-800' : '' }}">
                                    {{ ucfirst($update->type) }}
                                </span>
                            </dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500">Category:</dt>
                            <dd>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $update->category === 'features' ? 'bg-purple-100 text-purple-800' : '' }}
                                    {{ $update->category === 'fixes' ? 'bg-red-100 text-red-800' : '' }}
                                    {{ $update->category === 'improvements' ? 'bg-indigo-100 text-indigo-800' : '' }}
                                    {{ $update->category === 'security' ? 'bg-orange-100 text-orange-800' : '' }}">
                                    {{ ucfirst($update->category) }}
                                </span>
                            </dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500">Released:</dt>
                            <dd class="text-gray-900">{{ $update->released_at->format('M j, Y') }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Sample User Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        Sample User Data
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>This preview uses sample user data: <strong>{{ $sampleUser->name }}</strong> ({{ $sampleUser->email }})</p>
                        <p>Actual emails will be personalized for each recipient.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Content -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">📧 Email Content</h2>
            </div>
            
            <div class="p-6">
                <!-- Email Frame -->
                <div class="border border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                    <div class="bg-gray-100 px-4 py-2 border-b border-gray-300">
                        <div class="flex items-center justify-between text-sm text-gray-600">
                            <span>📧
                                @if(is_object($mailable) && method_exists($mailable, 'envelope'))
                                    {{ $mailable->envelope()->subject }}
                                @else
                                    Email Preview
                                @endif
                            </span>
                            <span>{{ now()->format('M j, Y g:i A') }}</span>
                        </div>
                    </div>
                    
                    <div class="email-content">
                        {!! $emailContent !!}
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="mt-6 text-center">
            <div class="inline-flex gap-3">
                <a href="/app-updates/{{ $update->id }}/edit" 
                   class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    ✏️ Edit Update
                </a>
            </div>
        </div>
    </div>

    <style>
        /* Email content styling */
        .email-content {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
        }
        
        .email-content h1, .email-content h2, .email-content h3 {
            color: #111827;
            margin-top: 0;
        }
        
        .email-content a {
            color: #3B82F6;
            text-decoration: none;
        }
        
        .email-content a:hover {
            text-decoration: underline;
        }
        
        /* Print styles */
        @media print {
            body {
                background: white;
            }
            
            .no-print {
                display: none !important;
            }
            
            .email-content {
                box-shadow: none;
                border: 1px solid #ccc;
            }
        }
    </style>
</body>
</html>
