<x-grid-layout md=2 title="My Team Lists" description="List of teams where you are currently a member">
    <x-filament::card>
        <div class="filament-forms-field-wrapper" style="margin-left: -15px !important;">
            <div class="bg-gray-900 py-10">
                <div>
                    <div class="mt-8 flow-root">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-700 ">
                                    @if(count($teamLists) === 0)
                                        <tr>
                                            <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-300 text-center">
                                                You are not currently a member of any teams.
                                            </td>
                                        </tr>

                                    @else
                                    <thead>
                                    <tr>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold dark:text-white">ID</th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold dark:text-white">Team Name</th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold dark:text-white">Date</th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold dark:text-white">
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-800">
                                    @foreach($teamLists as $key => $teamList)
                                        <tr>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm dark:text-gray-300">
                                                {{ $key + 1 }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm dark:text-gray-300">
                                                {{ title($teamList->owner->slug) }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm dark:text-gray-300">
                                                {{ $teamList->created_at->format('d/m/Y') }}
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-4 dark:text-gray-300 text-right text-sm font-medium sm:pr-0">
                                                <x-filament::button type="button" class="mr-2" wire:click.prevent="leaveTeam({{ $teamList }})" wire:target="leaveTeam({{ $teamList }})"
                                                >
                                                    Leave
                                                </x-filament::button>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::card>
</x-grid-layout>
