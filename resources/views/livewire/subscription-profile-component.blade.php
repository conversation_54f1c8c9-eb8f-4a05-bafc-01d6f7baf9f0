@php
    $user = auth()->user();
    $limit = $user->credits;
    $available = $user->credits - $user->credits_used;
    $used = $user->credits_used;

    // Check if user is legacy
    $legacyService = new \App\Services\LegacyUserService();
    $isLegacyUser = $legacyService->isLegacyUser($user);

    // Get subscriptions based on user type
    if ($isLegacyUser) {
        $subscriptions = $user->subscriptions()->with('plan')->get();
    } else {
        $subscriptions = $user->subscriptions()->with('plan')->latest()->get();
    }

    // Fix progress bar calculation
    $usagePercentage = $limit > 0 ? min(($used / $limit) * 100, 100) : 0;
    $progressBarWidth = max(1, $usagePercentage); // Minimum 1% width for visibility
@endphp

<div class="space-y-6">
    <!-- Credits Overview Card -->
    <x-filament::card>
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                        <x-heroicon-o-credit-card class="w-6 h-6 text-primary-600 dark:text-primary-400" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Credits Overview</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Your monthly content generation quota</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($available) }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Available</div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="mb-4">
                <div class="flex justify-between items-center text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">
                    <span>Used: {{ number_format($used) }}</span>
                    <span class="text-primary-600 dark:text-primary-400 font-bold">{{ number_format($usagePercentage, 1) }}%</span>
                </div>
                <div class="relative">
                    <div class="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-3 shadow-inner">
                        <div class="bg-primary-500 h-3 rounded-full transition-all duration-500 ease-out shadow-sm"
                             style="width: {{ max(1, $usagePercentage) }}%">
                        </div>
                    </div>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    Total: {{ number_format($limit) }} credits
                </div>
            </div>

            @if($subscriptions->isNotEmpty())
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <div class="flex items-center space-x-2 mb-2">
                        <x-heroicon-o-clock class="w-4 h-4 text-gray-500" />
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Next Reset</span>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Credits will reset on
                        <span class="font-semibold text-gray-900 dark:text-white">
                            {{ \Carbon\Carbon::parse(auth()->user()->credit_updated_at)->addDays(30)->format('M j, Y') }}
                        </span>
                    </p>
                </div>
            @endif
        </div>
    </x-filament::card>

    <!-- Active Subscriptions Card -->
    @if($subscriptions->isNotEmpty())
        <x-filament::card>
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <x-heroicon-o-check-circle class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Active Subscriptions</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Your current subscription plans</p>
                    </div>
                </div>

                <div class="space-y-4">
                    @foreach($subscriptions as $subscription)
                        @if (!$subscription->plan->hasPermissionAudioBookGeneration() && !$subscription->plan->hasPermissionResearchTools() || !$isLegacyUser)
                            @php
                                $quota = $subscription->plan->credits;
                                $status = $subscription->cancelled_at ? "Cancelled" : title($subscription->status);
                                $statusColor = ($subscription->cancelled_at || $subscription->status === 'expired') ? 'danger' : 'success';
                            @endphp
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3">
                                            <h4 class="font-semibold text-gray-900 dark:text-white">
                                                {{ $subscription->plan_name }}
                                            </h4>
                                            <x-filament::badge :color="$statusColor" size="sm">
                                                {{ $status }}
                                            </x-filament::badge>
                                        </div>
                                        @if($quota)
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                                <span class="font-medium">{{ number_format($quota) }} credits</span> per month
                                            </p>
                                        @endif
                                    </div>
                                    <div class="flex-shrink-0">
                                        <x-filament::button
                                            tag="a"
                                            href="{{ $subscription->manage_page_url }}"
                                            target="_blank"
                                            size="sm"
                                            color="gray"
                                            outlined
                                            icon="heroicon-o-cog-6-tooth"
                                        >
                                            Manage
                                        </x-filament::button>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </x-filament::card>
    @endif

    <!-- Quick Actions Card -->
    <x-filament::card>
        <div class="p-6">
            <div class="flex items-center space-x-3 mb-6">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <x-heroicon-o-rocket-launch class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Manage your subscription and explore addons</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <x-filament::button
                    tag="a"
                    href="https://ebookwriter.ai/"
                    target="_blank"
                    color="primary"
                    size="lg"
                    class="justify-center"
                    icon="heroicon-o-arrow-trending-up"
                >
                    Upgrade Plan
                </x-filament::button>
                <x-filament::button
                    tag="a"
                    href="{{ route('filament.app.resources.credit-logs.index') }}"
                    color="gray"
                    size="lg"
                    class="justify-center"
                    icon="heroicon-o-puzzle-piece"
                >
                    Credit Logs
                </x-filament::button>
                <x-filament::button
                    tag="a"
                    href="{{ route('filament.app.pages.addons') }}"
                    color="gray"
                    size="lg"
                    class="justify-center"
                    icon="heroicon-o-puzzle-piece"
                >
                    View Addons
                </x-filament::button>
            </div>

            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div class="text-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        Need to manage or cancel your subscription?
                    </p>
                    <x-filament::button
                        tag="a"
                        href="https://warriorplus.com/account/purchases"
                        target="_blank"
                        color="gray"
                        outlined
                        size="sm"
                        icon="heroicon-o-link"
                    >
                        WarriorPlus Account
                    </x-filament::button>
                </div>
            </div>
        </div>
    </x-filament::card>
</div>
