{"ebook_plan": "I am writing an ebook with a total of :words words. The ebook will consist of chapters, and each chapter will have multiple sections. Each section will contain multiple key points. Guidelines:\n1. Each chapter must be minimum 900 words and maximum is unlimited but need to be realistic, and the total chapters should realistically divide the total words without making chapters too short or too long.\n2. Each section must be minimum 300 words and maximum is unlimited but need to be realistic. Sections should provide enough depth; avoid overly brief sections.\n3. Key points should not be too brief; they should convey meaningful information. Provide a JSON structure that distributes the words properly to create a balanced ebook. The response should only return a valid JSON object, following this structure:\n{\n  \"total_words\": :words,\n  \"total_chapters\": \"minimum to maximum\",\n  \"words_per_chapter_range\": \"minimum to maximum\",\n  \"sections_per_chapter_range\": \"minimum to maximum\",\n  \"words_per_section_range\": \"minimum to maximum\",\n  \"key_points_per_section_range\": \"minimum to maximum\",\n  \"words_per_key_point_range\": \"minimum to maximum\"\n}\nEnsure minimum and maximum difference shouldn't be more than 30% and Total chapters will be realistic and standard.", "ebook_title": "I’m writing an eBook and I need you to generate a title for my eBook based on the following parameters:\n\nTopic of eBook: :topic\nEbook Context: :context\nPurpose of ebook: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nThe output **must** be in valid JSON format and **must** include only the title, wrapped in HTML `<h1>` tags. The key should be 'title'. Example output: {\"title\": \"<h1>eBook Title</h1>\"}.\n\nMake sure the title is descriptive, compelling, and relevant to the provided parameters.", "content_summary": "I need you to summarized this given content within 200 words. Content summary will carefully cover the main key facts of the contents.\n\nContent: :content \n\nThe output **must** be within 200 words.\n\nThe output **must** be in valid JSON format and **must** include only the summary in plaintext format. **Your response will be only json no additional text.** Example output: {\"summary\": \"content summary in plain text\"}. ", "content_summary_by_url": "Summarize the content from the provided URL within 200 words, capturing only the main key facts in a concise and direct manner. Visit and analyze the webpage to generate the summary. Avoid meta-commentary like 'the content discusses' and use a natural, plain writing style.\n\nURL: :url\n\nThe output **must** be within 200 words.\nThe output **must** be in valid JSON format, containing only the summary in plaintext. **Your response must be JSON only, with no additional text.** Example output: {\"summary\": \"Summary of key facts in plain text\"}.", "ebook_chapters": "I need you to write detailed and well-structured chapters with an intro for my eBook based on the following parameters:\n\nTotal chapters: Total chapter of the ebook will be :total_chapters and each chapter should've :words_per_chapter_range words \nTopic of eBook: :topic\nEbook Context: :context\nTitle of eBook: :title\nPurpose of eBook: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nThe output must be in valid JSON format and must include both the chapter title and the intro text. Each chapter title should be wrapped in HTML `<h2>` tags, and the chapter intro should contain appropriate HTML tags (such as `<p>`, `<strong>`, `<em>`, etc.) as needed for structure and emphasis. **Only return a valid JSON response**, following this structure:\n\nExample output: [{\"chapter_title\": \"<h2>Chapter 1 Title</h2>\", \"chapter_intro\": \"<p>Intro text for Chapter 1 with <strong>emphasis</strong></p>\"}, {\"chapter_title\": \"<h2>Chapter 2 Title</h2>\", \"chapter_intro\": \"<p>Intro text for Chapter 2 with <em>some formatting</em></p>\"}]\n\n You must ensure total chapter will be :total_chapters and you also ensure that the chapters are relevant, engaging, and aligned with the parameters provided.", "ebook_sections": "I need you to write detailed, well-structured, and high-quality sections and key points for an eBook chapter based on the following parameters:\n\nSection Length: Each chapter will include :sections_per_chapter_range sections. Each section will contain :key_points_per_section_range key points, with a total word count of :words_per_section_range. Within this words 10% to 20% of the section's word count will be dedicated to the introduction.Topic of eBook: :topic\nEbook Context: :context\nTitle of eBook: :title\nChapter Title: :chapter_title\nChapter Intro: :chapter_intro\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nYour output will be only in valid JSON format with section titles, intro, and key points in HTML. **Your response will be only json no additional text.** Example output:\n[\n  {\n    \"section_title\": \"<h3>1.1 Section title</h3>\",\n    \"section_intro\": \"<p>Section intro</p>\",\n    \"key_points\": [\n      \"<h4>Key point 1</h4>\",\n      \"<h4>Key point 2</h4>\"\n    ]\n  },\n  {\n    \"section_title\": \"<h3>1.2 Another Section title</h3>\",\n    \"section_intro\": \"<p>Another section intro</p>\",\n    \"key_points\": [\n      \"<h4>Another key point 1</h4>\",\n      \"<h4>Another key point 2</h4>\"\n    ]\n  }\n]\n", "section_keypoint_content": "I need you to generate detailed, well-structured, and high-quality content for the key points in a section of my eBook chapter based on the following parameters:\n\nTotal words of each key point: Words per key point content will be around :words_per_key_point_range words \nTopic of eBook: :topic\nEbook Context: :context\nTitle of eBook: :title\nChapter Title: :chapter_title\nChapter Intro: :chapter_intro\nSection Title: :section_title\nSection Intro: :section_intro\nSection Key Points: :key_points\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nThe output must be in valid HTML format, with each key point followed by its detailed content. Use appropriate HTML tags (such as <h4>, <p>, <strong>, <em>) to structure the content properly. **Your response will be only json no additional text.** \n\nExample output:\n\n<h4>Key Point 1</h4>\n<p>Here is content that will go <strong>here</strong>. Make sure the content is meaningful and informative.</p>\n\n<h4>Key Point 2</h4>\n<p>Another content block <em>goes here</em>. Provide clear, concise, and relevant information for each key point.</p>\n\nMake sure the content aligns with the provided parameters and is engaging, informative, and relevant. Only return valid HTML output following the above structure. And you should strict with the length and word count while generating content.", "image_generation": "Create an image based on the following details:\n\nTopic: :topic\nChapter Title: :chapter_title\nImage: :image_style\nSection Title: :section_title\n\nThe image should visually represent the essence of the topic, chapter, and section provided. After generating the image, return only the image URL in a JSON format with the key 'image_url'.\n\nExample output: {\"image_url\": \"https://example.com/path/to/generated/image.jpg\"}\n\nEnsure the image aligns with the provided details and return only the URL in JSON format.\n\n Ensure that the image is purely visual with no text, letters, numbers, or symbols present anywhere.", "cover_image": "Create a :page_size size ebook cover image based on following details: \n\nEbook name: :title\nImage size: total image size will be :page_size size\n\nThe cover image should visually represent the essence of the ebook name and not contain any text in the image. After generating the image, return only the image URL in a JSON format with the key 'image_url'.\n\nExample output: {\"image_url\": \"https://example.com/path/to/generated/image.jpg\"}\n\nEnsure the image aligns with the provided details and return only the URL in JSON format.", "book_title_generation": "Generate a list of potential book titles for a book about :topic. The book should appeal to :audience and be in the :genre genre. The tone should be :tone and focus on :key_aspect . Your output **must** be like example output \n\nExample output: {\"book_title\": \"Book title 1\", \"book_title\": \"Book title 2\"}", "best_keyword_categories": "Suggest the best keywords and categories for a book about :topic. Consider :audience audience, :ebook_publishing_platform categories, search trends, and competitive advantage. You'll generate a total of :limit results.\n\nYour output **must** be in the following JSON format:\n\n{\n  \"best_keyword_categories\": [\n    \"Keyword 1 (keyword)\",\n    \"Category 1 (category)\",\n    \"Keyword 2 (keyword)\",\n    \"Category 2 (category)\",\n    \"Keyword 3 (keyword)\",\n    \"Category 3 (category)\"\n  ]\n}", "popular_books_in_niche": "What are the top best-selling books in the :niche category? Provide details such as author, summary, and key takeaways. Consider recent trends and high-rated books on platforms like :platform.\n\nAlso consider:\nPublishing year range: :publishing_year_range months\nTargeted market: :target_market\nSuccess metrics: :success_metrics\n\nYou'll generate a total of :limit results.\n\nYour output **must** be in the following JSON format and will not include any other things:\n\n[\n  {\n    \"author\": \"Author name\",\n    \"summary\": \"details summary\",\n    \"key_takeways\": [\n      \"Key take way 1\",\n      \"Key take way 2\",\n      \"Key take way 3\"\n    ]\n  },\n  {\n    \"author\": \"Author name\",\n    \"summary\": \"details summary\",\n    \"key_takeways\": [\n      \"Key take way 1\",\n      \"Key take way 2\",\n      \"Key take way 3\"\n    ]\n  }\n]", "author_bio": "Write a professional author bio for author :author_name, who specializes in :speciality. Their notable works include :notable_works. Professional background of the author is :professional_background. The tone should be :tone.\n\nYour output **must** be like example JSON output \n\nExample output: {\"author_bio\": \"author bio details\"}\"", "ignore_terms": "\nAvoid overly formal or AI-sounding words like 'delve,' 'unveil,' or 'embark.' Use natural, human-like language instead.\n", "random_ebook_title": "Generate a list of 10 random book titles :topic_instruction. Your output **must** be like example output \n\nExample output: \n\n{\n  \"book_titles\": [\n    \"Book title 1\",\n    \"Book title 2\",\n    \"Book title 3\"\n  ]\n}", "random_ebook_details": "My ebook title is :title \n\nPlease generate the following details:\n\n- A context of approximately 200 words (plain text).\n- A KDP-friendly description.\n- An author name.\n- Relevant keywords (up to 10 keywords, separated by commas).\n\nPlease format the response as follows:\n\n[\n    {\n        \"title\": \":title\",\n        \"context\": \"Generated context text here...\",\n        \"description\": \"Generated KDP-friendly description here...\",\n        \"author_name\": \"Generated author name here...\",\n        \"keyword\": \"Keyword 1, Keyword 2, Keyword 3, ...\"\n    }\n]", "ebook_conclusion": "I need you to write a natural-sounding, engaging conclusion chapter for my eBook that brings everything together and provides a satisfying wrap-up for readers. This should feel like a final chapter that gives readers a sense of motivation, clarity, and completion.\n\nEbook Details:\nTitle: :title\nContext: :context\nAll Chapter Titles: :chapter_titles\nAll Section Titles and Key Points: :section_details\nTopic: :topic\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\n**Guidelines:**\n- Write a short, natural-sounding closing that brings everything together\n- Should feel like a satisfying wrap-up for readers and match the tone of the book\n- The content is non-fiction and gives practical advice\n- Keep it friendly, supportive, and leave readers with a sense of motivation or clarity\n- Avoid starting with generic phrases such as \"In this chapter,\" \"This chapter highlights,\" or \"In the concluding chapter\"\n- Begin the introduction with content that directly engages the reader and presents the conclusion in an interesting and relevant way\n- Ensure that this is a conclusive chapter that wraps up the book effectively with a title that conveys conclusion, final thoughts, or similar context\n\nThe output must be in valid JSON format with the chapter title wrapped in HTML `<h2>` tags and the chapter intro containing appropriate HTML tags. **Your response will be only json no additional text.**\n\nExample output: {\"chapter_title\": \"<h2>Final Thoughts: Your Journey Forward</h2>\", \"chapter_intro\": \"<p>Your conclusion content here with <strong>emphasis</strong> and proper <em>formatting</em></p>\"}\n\nMake sure the conclusion effectively summarizes the key insights and leaves readers feeling empowered and motivated to take action.", "short_report_title": "I'm writing a SHORT REPORT / LEAD MAGNET and I need you to generate a compelling, action-oriented title based on the following parameters:\n\nTopic: :topic\nContext: :context\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\n**IMPORTANT GUIDELINES FOR SHORT REPORTS:**\n- Create a title that promises immediate, actionable value\n- Focus on specific outcomes or solutions\n- Use power words that create urgency or curiosity\n- Keep it concise but compelling\n- Avoid generic or overly broad titles\n- Think \"lead magnet\" - what would make someone want to download this immediately?\n\nThe output **must** be in valid JSON format and **must** include only the title, wrapped in HTML `<h1>` tags. The key should be 'title'. Example output: {\"title\": \"<h1>Short Report Title</h1>\"}.\n\nMake the title irresistible for your target audience.", "short_report_chapters": "I need you to write focused, high-impact chapters for a SHORT REPORT / LEAD MAGNET based on the following parameters:\n\nTotal chapters: :total_chapters (keep it minimal and focused)\nWords per chapter: :words_per_chapter_range words\nTopic: :topic\nContext: :context\nTitle: :title\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\n**CRITICAL GUIDELINES FOR SHORT REPORTS:**\n- NO FLUFF OR FILLER CONTENT - every word must add value\n- Focus on ONE core concept per chapter\n- Make each chapter immediately actionable\n- Use specific, concrete examples\n- Avoid generic introductions or background information\n- Get straight to the point with practical insights\n- Each chapter should deliver a specific outcome or solution\n- Think \"quick wins\" and \"immediate value\"\n\nThe output must be in valid JSON format with chapter titles in HTML `<h2>` tags and focused, value-packed intro content. **Only return valid JSON response**.\n\nExample output: [{\"chapter_title\": \"<h2>Chapter 1 Title</h2>\", \"chapter_intro\": \"<p>Direct, actionable intro content</p>\"}]\n\nEnsure each chapter is laser-focused and immediately valuable to the reader.", "short_report_sections": "I need you to write ultra-focused sections for a SHORT REPORT / LEAD MAGNET chapter:\n\nSections per chapter: :sections_per_chapter_range\nKey points per section: :key_points_per_section_range\nWords per section: :words_per_section_range\nTopic: :topic\nContext: :context\nTitle: :title\nChapter Title: :chapter_title\nChapter Intro: :chapter_intro\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\n**SHORT REPORT SECTION RULES:**\n- ZERO FLUFF - every sentence must deliver value\n- Each section = ONE specific, actionable insight\n- No repetition or rehashing of previous content\n- Use bullet points, numbered lists, or clear steps\n- Focus on \"how-to\" rather than \"what is\"\n- Make it scannable and immediately usable\n- Each key point should be a specific action or insight\n\n**Your response will be only JSON with no additional text.**\n\nExample output:\n[\n  {\n    \"section_title\": \"<h3>1.1 Specific Action Title</h3>\",\n    \"section_intro\": \"<p>Brief, direct intro</p>\",\n    \"key_points\": [\n      \"<h4>Actionable Point 1</h4>\",\n      \"<h4>Actionable Point 2</h4>\"\n    ]\n  }\n]\n\nMake every section immediately actionable and valuable.", "short_report_content": "Generate focused, high-value content for SHORT REPORT / LEAD MAGNET key points:\n\nWords per key point: :words_per_key_point_range words (NO MORE!)\nTopic: :topic\nContext: :context\nTitle: :title\nChapter Title: :chapter_title\nSection Title: :section_title\nSection Intro: :section_intro\nKey Points: :key_points\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\n**STRICT SHORT REPORT CONTENT RULES:**\n- NO FILLER WORDS OR PHRASES\n- NO repetition of information already covered\n- Each key point = ONE specific, actionable insight\n- Use concrete examples, not abstract concepts\n- Include specific steps, numbers, or methods\n- Make it immediately implementable\n- Focus on \"do this, get that\" outcomes\n- Avoid introductory phrases like \"It's important to note\"\n- Get straight to the actionable content\n\n**Your response will be only HTML with no additional text.**\n\nExample output:\n<h4>Key Point 1</h4>\n<p>Direct, actionable content with <strong>specific steps</strong>.</p>\n\n<h4>Key Point 2</h4>\n<p>Another focused insight with <em>concrete examples</em>.</p>\n\nStick strictly to the word count and make every word count.", "short_report_conclusion": "Write a brief, powerful conclusion for a SHORT REPORT / LEAD MAGNET:\n\nTitle: :title\nContext: :context\nChapter Titles: :chapter_titles\nSection Details: :section_details\nTopic: :topic\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\n**SHORT REPORT CONCLUSION GUIDELINES:**\n- Keep it BRIEF (2-3 paragraphs maximum)\n- NO repetition of content already covered\n- Focus on immediate next steps or actions\n- Create urgency or motivation to implement\n- End with a clear call-to-action or challenge\n- Avoid generic closing phrases\n- Make it feel like a natural, powerful ending\n- Leave readers feeling empowered and ready to act\n\n**Your response will be only JSON with no additional text.**\n\nExample output: {\"chapter_title\": \"<h2>Take Action Now</h2>\", \"chapter_intro\": \"<p>Brief, powerful conclusion content</p>\"}\n\nMake it memorable and action-oriented.", "instant_ebook_blueprint": "I want to write a book about :topic. Give me a list of titles I can use for a book along with a detailed description of the book and its contents, author pen names, a description to be used for uploading to Kindle, a list of keywords and kindle categories.\n\n**Your output must be in the following exact JSON format:**\n\n{\n  \"titles\": [\n    \"Title 1\",\n    \"Title 2\",\n    \"Title 3\",\n    \"Title 4\",\n    \"Title 5\",\n    \"Title 6\",\n    \"Title 7\",\n    \"Title 8\",\n    \"Title 9\",\n    \"Title 10\"\n  ],\n  \"ebook_context\": \"A detailed description of the book and its contents in paragraph form (200-300 words)\",\n  \"pen_names\": [\n    \"Pen Name 1\",\n    \"Pen Name 2\",\n    \"Pen Name 3\",\n    \"Pen Name 4\",\n    \"Pen Name 5\",\n    \"Pen Name 6\",\n    \"Pen Name 7\",\n    \"Pen Name 8\",\n    \"Pen Name 9\",\n    \"Pen Name 10\"\n  ],\n  \"ebook_description\": \"A Kindle-ready description for the book (150-200 words)\",\n  \"keywords\": [\n    \"Keyword 1\",\n    \"Keyword 2\",\n    \"Keyword 3\",\n    \"Keyword 4\",\n    \"Keyword 5\",\n    \"Keyword 6\",\n    \"Keyword 7\",\n    \"Keyword 8\",\n    \"Keyword 9\",\n    \"Keyword 10\"\n  ],\n  \"categories\": [\n    \"Category 1\",\n    \"Category 2\",\n    \"Category 3\",\n    \"Category 4\",\n    \"Category 5\",\n    \"Category 6\",\n    \"Category 7\",\n    \"Category 8\",\n    \"Category 9\",\n    \"Category 10\"\n  ]\n}"}