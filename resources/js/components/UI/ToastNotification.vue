<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 translate-y-2 scale-95"
      enter-to-class="opacity-100 translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0 scale-100"
      leave-to-class="opacity-0 translate-y-2 scale-95"
    >
      <div
        v-if="visible"
        class="fixed top-4 right-4 z-50 max-w-sm w-full"
      >
        <div
          :class="[
            'rounded-lg shadow-lg border backdrop-blur-sm',
            'transform transition-all duration-300',
            typeClasses
          ]"
        >
          <div class="p-4">
            <div class="flex items-start">
              <!-- Icon -->
              <div class="flex-shrink-0">
                <!-- Success Icon -->
                <svg v-if="type === 'success'" :class="['w-5 h-5', iconClasses]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>

                <!-- Error Icon -->
                <svg v-else-if="type === 'error'" :class="['w-5 h-5', iconClasses]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>

                <!-- Warning Icon -->
                <svg v-else-if="type === 'warning'" :class="['w-5 h-5', iconClasses]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>

                <!-- Info Icon -->
                <svg v-else :class="['w-5 h-5', iconClasses]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              
              <!-- Content -->
              <div class="ml-3 flex-1">
                <p :class="['text-sm font-medium', titleClasses]">
                  {{ title }}
                </p>
                <p v-if="message" :class="['mt-1 text-sm', messageClasses]">
                  {{ message }}
                </p>
              </div>
              
              <!-- Close button -->
              <div class="ml-4 flex-shrink-0">
                <button
                  @click="close"
                  :class="[
                    'inline-flex rounded-md p-1.5 transition-colors',
                    'hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-offset-2',
                    closeButtonClasses
                  ]"
                >
                  <span class="sr-only">Dismiss</span>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Progress bar for auto-dismiss -->
          <div
            v-if="autoDismiss && duration > 0"
            class="h-1 bg-black/10 rounded-b-lg overflow-hidden"
          >
            <div
              class="h-full bg-current transition-all ease-linear"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'ToastNotification',
  props: {
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
    },
    title: {
      type: String,
      required: true
    },
    message: {
      type: String,
      default: ''
    },
    duration: {
      type: Number,
      default: 4000 // 4 seconds
    },
    autoDismiss: {
      type: Boolean,
      default: true
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const visible = ref(false)
    const progress = ref(100)
    let timer = null
    let progressTimer = null

    // Show notification on mount
    onMounted(() => {
      visible.value = true
      
      if (props.autoDismiss && props.duration > 0) {
        startAutoDismiss()
      }
    })

    const startAutoDismiss = () => {
      // Progress bar animation
      const progressInterval = 50 // Update every 50ms
      const progressDecrement = (100 / props.duration) * progressInterval
      
      progressTimer = setInterval(() => {
        progress.value -= progressDecrement
        if (progress.value <= 0) {
          progress.value = 0
          clearInterval(progressTimer)
        }
      }, progressInterval)

      // Auto dismiss timer
      timer = setTimeout(() => {
        close()
      }, props.duration)
    }

    const close = () => {
      visible.value = false
      if (timer) clearTimeout(timer)
      if (progressTimer) clearInterval(progressTimer)
      
      // Wait for transition to complete before emitting close
      setTimeout(() => {
        emit('close')
      }, 200)
    }

    // Computed classes based on type
    const typeClasses = computed(() => {
      const classes = {
        success: 'bg-green-50/90 border-green-200 text-green-800 dark:bg-green-900/90 dark:border-green-700 dark:text-green-100',
        error: 'bg-red-50/90 border-red-200 text-red-800 dark:bg-red-900/90 dark:border-red-700 dark:text-red-100',
        warning: 'bg-yellow-50/90 border-yellow-200 text-yellow-800 dark:bg-yellow-900/90 dark:border-yellow-700 dark:text-yellow-100',
        info: 'bg-blue-50/90 border-blue-200 text-blue-800 dark:bg-blue-900/90 dark:border-blue-700 dark:text-blue-100'
      }
      return classes[props.type]
    })

    const iconClasses = computed(() => {
      const classes = {
        success: 'text-green-500 dark:text-green-400',
        error: 'text-red-500 dark:text-red-400',
        warning: 'text-yellow-500 dark:text-yellow-400',
        info: 'text-blue-500 dark:text-blue-400'
      }
      return classes[props.type]
    })

    const titleClasses = computed(() => {
      const classes = {
        success: 'text-green-800 dark:text-green-100',
        error: 'text-red-800 dark:text-red-100',
        warning: 'text-yellow-800 dark:text-yellow-100',
        info: 'text-blue-800 dark:text-blue-100'
      }
      return classes[props.type]
    })

    const messageClasses = computed(() => {
      const classes = {
        success: 'text-green-700 dark:text-green-200',
        error: 'text-red-700 dark:text-red-200',
        warning: 'text-yellow-700 dark:text-yellow-200',
        info: 'text-blue-700 dark:text-blue-200'
      }
      return classes[props.type]
    })

    const closeButtonClasses = computed(() => {
      const classes = {
        success: 'text-green-500 hover:text-green-600 focus:ring-green-500 dark:text-green-400',
        error: 'text-red-500 hover:text-red-600 focus:ring-red-500 dark:text-red-400',
        warning: 'text-yellow-500 hover:text-yellow-600 focus:ring-yellow-500 dark:text-yellow-400',
        info: 'text-blue-500 hover:text-blue-600 focus:ring-blue-500 dark:text-blue-400'
      }
      return classes[props.type]
    })



    onUnmounted(() => {
      if (timer) clearTimeout(timer)
      if (progressTimer) clearInterval(progressTimer)
    })

    return {
      visible,
      progress,
      close,
      typeClasses,
      iconClasses,
      titleClasses,
      messageClasses,
      closeButtonClasses
    }
  }
}
</script>
