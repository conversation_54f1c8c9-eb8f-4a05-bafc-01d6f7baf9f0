<template>
  <div class="edit-book-container min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto ">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <!-- Show back button only in non-standalone mode -->
            <a :href="backUrl"
               class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
              </svg>
              Back
            </a>
            <div>
              <h1 class="text-xl font-semibold text-gray-900 dark:text-white"
                  v-text="sanitizedTitle">
              </h1>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <UnsavedChangesIndicator :has-unsaved-changes="hasUnsavedChanges" />

            <a
              :href="`${backUrl}?activeRelationManager=3`"
              class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              Downloads
            </a>

            <button
              @click="regenerateEbook"
              :disabled="isLoading || isRegenerating"
              class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
              <svg v-if="isRegenerating" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              Re-generate Ebook
            </button>

            <button
              @click="saveCurrentContent"
              :disabled="!hasUnsavedChanges || isLoading || hasActiveImageGeneration"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
              <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Save All Changes (Ctrl+S)
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto py-6">
      <div class="flex gap-6 h-[calc(100vh-150px)]">
        <!-- Left Sidebar - Chapter/Section Navigation (1/4 width) -->
        <BookStructureSidebar
          :chapters="chapters"
          :selected-chapter="selectedChapter"
          :selected-section="selectedSection"
          :editing-chapter="editingChapter"
          :unsaved-changes="unsavedChanges"
          @select-chapter="selectChapter"
          @select-section="selectSection"
          @edit-chapter="editChapter"
          @add-chapter="addChapter"
          @delete-chapter="deleteChapter"
          @add-section="addSection"
          @delete-section="deleteSection"
          @reorder-sections="reorderSections"
          @reorder-chapters="reorderChapters"
        />

        <!-- Main Content Area (3/4 width) -->
        <div class="w-full bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <ChapterEditor
            v-if="editingChapter && selectedChapter"
            :chapter="selectedChapter"
            :unsaved-data="getUnsavedDataForChapter(selectedChapter.id)"
            @update="updateChapter"
            @cancel="cancelChapterEdit"
            @content-changed="(data, hasChanges) => setUnsavedChangesForChapter(selectedChapter.id, data, hasChanges)"
          />

          <SectionEditor
            v-else-if="selectedSection"
            :section="selectedSection"
            :unsaved-data="getUnsavedDataForSection(selectedSection.id)"
            @update="updateSection"
            @content-changed="(data, hasChanges) => setUnsavedChangesForSection(selectedSection.id, data, hasChanges)"
          />

          <div v-else class="h-full flex items-center justify-center">
            <div class="text-center">
              <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No content selected</h3>
              <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Select a chapter or section from the sidebar to start editing.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <LoadingOverlay :show="isLoading" />
  </div>
</template>

<script>
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import BookStructureSidebar from './BookStructureSidebar.vue';
import ChapterEditor from './ChapterEditor.vue';
import SectionEditor from './SectionEditor.vue';
import UnsavedChangesIndicator from './UnsavedChangesIndicator.vue';
import LoadingOverlay from './LoadingOverlay.vue';
import { useAIImageGeneration } from '../../composables/useAIImageGeneration.js';
import { useToast } from '../../composables/useToast.js';
import { useConfirmation } from '../../composables/useConfirmation.js';

export default {
  name: 'EditBookApp',
  components: {
    BookStructureSidebar,
    ChapterEditor,
    SectionEditor,
    UnsavedChangesIndicator,
    LoadingOverlay,
  },
  setup() {
    // Initialize composables
    const imageGeneration = useAIImageGeneration();
    const toast = useToast();
    const confirmation = useConfirmation();

    // URL utility functions
    const getUrlParams = () => {
      const urlParams = new URLSearchParams(window.location.search);
      return {
        chapterId: urlParams.get('chapter'),
        sectionId: urlParams.get('section'),
        editMode: urlParams.get('edit') === 'true'
      };
    };

    const updateUrl = (params = {}) => {
      const url = new URL(window.location);

      // Clear existing params
      url.searchParams.delete('chapter');
      url.searchParams.delete('section');
      url.searchParams.delete('edit');

      // Set new params
      if (params.chapterId) {
        url.searchParams.set('chapter', params.chapterId);
      }
      if (params.sectionId) {
        url.searchParams.set('section', params.sectionId);
      }
      if (params.editMode) {
        url.searchParams.set('edit', 'true');
      }

      // Update URL without page reload
      window.history.pushState({}, '', url);
    };

    // Reactive state
    const campaign = ref(null);
    const chapters = ref([]);
    const selectedChapter = ref(null);
    const selectedSection = ref(null);
    const editingChapter = ref(false);
    const hasUnsavedChanges = ref(false);
    const isLoading = ref(false);
    const isRegenerating = ref(false);

    // Centralized unsaved changes store
    const unsavedChanges = ref({
      chapters: {}, // chapterId: { title, intro, hasChanges }
      sections: {}  // sectionId: { title, intro, body, hasChanges }
    });

    // Check if there are active image generations
    const hasActiveImageGeneration = computed(() => {
      return imageGeneration.isGenerating.value || imageGeneration.activeGenerations.size > 0;
    });

    // Sanitize HTML tags from campaign title
    const sanitizedTitle = computed(() => {
      const stripHtml = (html) => {
        if (!html) return '';
        // Create a temporary div element to parse HTML
        const temp = document.createElement('div');
        temp.innerHTML = html;
        return temp.textContent || temp.innerText || '';
      };

      const title = campaign.value?.title || campaign.value?.topic || '';
      const cleanTitle = stripHtml(title);

      return isStandalone ? cleanTitle : `Edit Book: ${cleanTitle}`;
    });

    // Detect if we're in standalone mode
    const isStandalone = window.location.pathname.includes('/standalone/');

    // Get campaign ID from data attribute or URL as fallback
    const campaignElement = document.getElementById('edit-book-app');
    const campaignId = campaignElement?.dataset.campaignId || window.location.pathname.split('/').pop();

    const backUrl = ref(`/campaign/${campaignId}` );

    // Helper methods for unsaved changes management
    const saveCurrentToUnsavedStore = () => {
      // This function is called before navigation to ensure current editor state is preserved
      // The actual content tracking is handled by the content-changed events from child components
      // This is a placeholder for any additional state preservation logic if needed
    };

    const updateGlobalUnsavedState = () => {
      // Check if any chapter or section has unsaved changes
      const hasChapterChanges = Object.values(unsavedChanges.value.chapters).some(ch => ch.hasChanges);
      const hasSectionChanges = Object.values(unsavedChanges.value.sections).some(sec => sec.hasChanges);
      hasUnsavedChanges.value = hasChapterChanges || hasSectionChanges;
    };

    const setUnsavedChangesForChapter = (chapterId, data, hasChanges) => {
      if (hasChanges) {
        unsavedChanges.value.chapters[chapterId] = { ...data, hasChanges: true };
      } else {
        delete unsavedChanges.value.chapters[chapterId];
      }
      updateGlobalUnsavedState();
    };

    const setUnsavedChangesForSection = (sectionId, data, hasChanges) => {
      if (hasChanges) {
        unsavedChanges.value.sections[sectionId] = { ...data, hasChanges: true };
      } else {
        delete unsavedChanges.value.sections[sectionId];
      }
      updateGlobalUnsavedState();
    };

    const getUnsavedDataForChapter = (chapterId) => {
      return unsavedChanges.value.chapters[chapterId] || null;
    };

    const getUnsavedDataForSection = (sectionId) => {
      return unsavedChanges.value.sections[sectionId] || null;
    };

    const hasUnsavedChangesForChapter = (chapterId) => {
      return !!unsavedChanges.value.chapters[chapterId]?.hasChanges;
    };

    const hasUnsavedChangesForSection = (sectionId) => {
      return !!unsavedChanges.value.sections[sectionId]?.hasChanges;
    };

    // Methods
    const loadCampaignData = async () => {
      isLoading.value = true;
      try {
        // Then test authentication
        console.log('Testing authentication...');
        const authResponse = await axios.get('/api/debug/auth');
        console.log('Auth status:', authResponse.data);

        if (!authResponse.data.authenticated) {
          throw new Error('User is not authenticated');
        }

        console.log('Loading campaign data for ID:', campaignId);
        const response = await axios.get(`/api/edit-book/campaign/${campaignId}`);
        console.log('Campaign data loaded:', response.data);
        campaign.value = response.data.campaign;
        chapters.value = response.data.chapters;

        // Initialize selection from URL parameters after data is loaded
        initializeFromUrl();
      } catch (error) {
        console.error('Error loading campaign data:', error);
        if (error.response) {
          console.error('Response status:', error.response.status);
          console.error('Response data:', error.response.data);
          console.error('Response headers:', error.response.headers);
        }
        // Handle error - show notification
        alert('Error loading campaign data. Please check the console for details.');
      } finally {
        isLoading.value = false;
      }
    };

    const initializeFromUrl = () => {
      const params = getUrlParams();

      if (params.sectionId) {
        // Find section by ID across all chapters
        for (const chapter of chapters.value) {
          if (chapter.sections) {
            const section = chapter.sections.find(s => s.id == params.sectionId);
            if (section) {
              selectedSection.value = section;
              selectedChapter.value = null;
              editingChapter.value = false;
              return;
            }
          }
        }
      } else if (params.chapterId) {
        // Find chapter by ID
        const chapter = chapters.value.find(c => c.id == params.chapterId);
        if (chapter) {
          selectedChapter.value = chapter;
          selectedSection.value = null;
          editingChapter.value = params.editMode;
          return;
        }
      }

      // If no valid selection from URL, clear selection
      selectedChapter.value = null;
      selectedSection.value = null;
      editingChapter.value = false;
    };

    const selectChapter = (chapter) => {
      selectedChapter.value = chapter;
      selectedSection.value = null;
      editingChapter.value = false;
      updateGlobalUnsavedState();

      // Update URL
      updateUrl({ chapterId: chapter.id });
    };

    const selectSection = (section) => {
      selectedSection.value = section;
      selectedChapter.value = null;
      editingChapter.value = false;
      updateGlobalUnsavedState();

      // Update URL
      updateUrl({ sectionId: section.id });
    };

    const editChapter = (chapter) => {
      selectedChapter.value = chapter;
      selectedSection.value = null;
      editingChapter.value = true;
      updateGlobalUnsavedState();

      // Update URL
      updateUrl({ chapterId: chapter.id, editMode: true });
    };

    const cancelChapterEdit = () => {
      // Clear unsaved changes for the current chapter if any
      if (selectedChapter.value) {
        delete unsavedChanges.value.chapters[selectedChapter.value.id];
      }

      editingChapter.value = false;
      selectedChapter.value = null;
      updateGlobalUnsavedState();

      // Clear URL parameters
      updateUrl();
    };

    const updateChapter = async (chapterData) => {
      isLoading.value = true;
      try {
        const response = await axios.put(`/api/edit-book/campaign/${campaignId}/chapter/${selectedChapter.value.id}`, chapterData);

        // Update local chapter data
        const chapterIndex = chapters.value.findIndex(c => c.id === selectedChapter.value.id);
        if (chapterIndex !== -1) {
          chapters.value[chapterIndex] = { ...chapters.value[chapterIndex], ...response.data.chapter };
          selectedChapter.value = chapters.value[chapterIndex];
        }

        // Clear unsaved changes for this chapter
        delete unsavedChanges.value.chapters[selectedChapter.value.id];
        updateGlobalUnsavedState();

        // Show success notification
        showNotification('Chapter updated successfully', 'success');
      } catch (error) {
        console.error('Error updating chapter:', error);
        showNotification('Error updating chapter. Please try again.', 'error');
      } finally {
        isLoading.value = false;
      }
    };

    const updateSection = async (sectionData) => {
      isLoading.value = true;
      try {
        const response = await axios.put(`/api/edit-book/campaign/${campaignId}/section/${selectedSection.value.id}`, sectionData);

        // Update local section data
        const chapter = chapters.value.find(c => c.sections.some(s => s.id === selectedSection.value.id));
        if (chapter) {
          const sectionIndex = chapter.sections.findIndex(s => s.id === selectedSection.value.id);
          if (sectionIndex !== -1) {
            chapter.sections[sectionIndex] = { ...chapter.sections[sectionIndex], ...response.data.section };
            selectedSection.value = chapter.sections[sectionIndex];
          }
        }

        // Clear unsaved changes for this section
        delete unsavedChanges.value.sections[selectedSection.value.id];
        updateGlobalUnsavedState();

        // Show success notification
        showNotification('Section updated successfully', 'success');
      } catch (error) {
        console.error('Error updating section:', error);
        showNotification('Error updating section. Please try again.', 'error');
      } finally {
        isLoading.value = false;
      }
    };

    const addChapter = async () => {
      isLoading.value = true;
      try {
        const response = await axios.post(`/api/edit-book/campaign/${campaignId}/chapter`);

        // Add new chapter to local data
        chapters.value.push(response.data.chapter);

        // Select the new chapter for editing
        editChapter(response.data.chapter);

        // Show success notification
        showNotification('Chapter added successfully', 'success');
      } catch (error) {
        console.error('Error adding chapter:', error);
        showNotification('Error adding chapter. Please try again.', 'error');
      } finally {
        isLoading.value = false;
      }
    };

    const deleteChapter = async (chapterId) => {
      isLoading.value = true;
      try {
        await axios.delete(`/api/edit-book/campaign/${campaignId}/chapter/${chapterId}`);

        // Remove chapter from local data
        const chapterIndex = chapters.value.findIndex(c => c.id === chapterId);
        if (chapterIndex !== -1) {
          chapters.value.splice(chapterIndex, 1);
        }

        // Clear selection if deleted chapter was selected
        if (selectedChapter.value?.id === chapterId) {
          selectedChapter.value = null;
          editingChapter.value = false;
        }

        // Show success notification
      } catch (error) {
        console.error('Error deleting chapter:', error);
        // Show error notification
      } finally {
        isLoading.value = false;
      }
    };

    const addSection = async (chapterId) => {
      isLoading.value = true;
      try {
        const response = await axios.post(`/api/edit-book/campaign/${campaignId}/chapter/${chapterId}/section`);

        // Add new section to local data
        const chapter = chapters.value.find(c => c.id === chapterId);
        if (chapter) {
          chapter.sections.push(response.data.section);
          selectSection(response.data.section);
        }

        // Show success notification
        showNotification('Section added successfully', 'success');
      } catch (error) {
        console.error('Error adding section:', error);
        showNotification('Error adding section. Please try again.', 'error');
      } finally {
        isLoading.value = false;
      }
    };

    const deleteSection = async (sectionId) => {
      // Find the section to get its title for the confirmation
      let sectionTitle = 'this section';
      chapters.value.forEach(chapter => {
        const section = chapter.sections.find(s => s.id === sectionId);
        if (section && section.title) {
          sectionTitle = `"${section.title}"`;
        }
      });

      // Show modern confirmation modal
      const confirmed = await confirmation.confirmDeleteSection(sectionTitle);
      if (!confirmed) {
        return;
      }

      isLoading.value = true;
      try {
        await axios.delete(`/api/edit-book/campaign/${campaignId}/section/${sectionId}`);

        // Remove section from local data
        chapters.value.forEach(chapter => {
          const sectionIndex = chapter.sections.findIndex(s => s.id === sectionId);
          if (sectionIndex !== -1) {
            chapter.sections.splice(sectionIndex, 1);
          }
        });

        // Clear selection if deleted section was selected
        if (selectedSection.value?.id === sectionId) {
          selectedSection.value = null;
        }

        // Show success notification
      } catch (error) {
        console.error('Error deleting section:', error);
        // Show error notification
      } finally {
        isLoading.value = false;
      }
    };

    const reorderSections = async (sections) => {
      try {
        await axios.post(`/api/edit-book/campaign/${campaignId}/reorder-sections`, { sections });

        // Update local state to reflect the new order
        sections.forEach(sectionData => {
          const chapter = chapters.value.find(c => c.sections.some(s => s.id === sectionData.id));
          if (chapter) {
            const section = chapter.sections.find(s => s.id === sectionData.id);
            if (section) {
              section.order = sectionData.order;
            }
          }
        });

        // Re-sort sections in each chapter by order
        chapters.value.forEach(chapter => {
          chapter.sections.sort((a, b) => a.order - b.order);
        });

        console.log('Sections reordered successfully');
      } catch (error) {
        console.error('Error reordering sections:', error);
        alert('Error reordering sections. Please try again.');
      }
    };

    const reorderChapters = async (chaptersOrder) => {
      try {
        await axios.post(`/api/edit-book/campaign/${campaignId}/reorder-chapters`, { chapters: chaptersOrder });

        // Update local state to reflect the new order
        chaptersOrder.forEach(chapterData => {
          const chapter = chapters.value.find(c => c.id === chapterData.id);
          if (chapter) {
            chapter.chapter_number = chapterData.order;
          }
        });

        // Re-sort chapters by chapter_number
        chapters.value.sort((a, b) => a.chapter_number - b.chapter_number);

        console.log('Chapters reordered successfully');
      } catch (error) {
        console.error('Error reordering chapters:', error);
        showNotification('Error reordering chapters. Please try again.', 'error');
      }
    };

    const setUnsavedChanges = (value = true) => {
      // This method is kept for backward compatibility but now uses the new system
      hasUnsavedChanges.value = value;
    };

    const saveAllUnsavedChanges = async () => {
      if (!hasUnsavedChanges.value) return;

      isLoading.value = true;
      const errors = [];

      try {
        // Save all unsaved chapters
        for (const [chapterId, chapterData] of Object.entries(unsavedChanges.value.chapters)) {
          if (chapterData.hasChanges) {
            try {
              const response = await axios.put(`/api/edit-book/campaign/${campaignId}/chapter/${chapterId}`, {
                title: chapterData.title,
                intro: chapterData.intro
              });

              // Update local chapter data
              const chapterIndex = chapters.value.findIndex(c => c.id == chapterId);
              if (chapterIndex !== -1) {
                chapters.value[chapterIndex] = { ...chapters.value[chapterIndex], ...response.data.chapter };
                if (selectedChapter.value?.id == chapterId) {
                  selectedChapter.value = chapters.value[chapterIndex];
                }
              }

              // Remove from unsaved changes
              delete unsavedChanges.value.chapters[chapterId];
            } catch (error) {
              console.error(`Error saving chapter ${chapterId}:`, error);
              errors.push(`Chapter ${chapterId}`);
            }
          }
        }

        // Save all unsaved sections
        for (const [sectionId, sectionData] of Object.entries(unsavedChanges.value.sections)) {
          if (sectionData.hasChanges) {
            try {
              const response = await axios.put(`/api/edit-book/campaign/${campaignId}/section/${sectionId}`, {
                title: sectionData.title,
                intro: sectionData.intro,
                body: sectionData.body
              });

              // Update local section data
              const chapter = chapters.value.find(c => c.sections.some(s => s.id == sectionId));
              if (chapter) {
                const sectionIndex = chapter.sections.findIndex(s => s.id == sectionId);
                if (sectionIndex !== -1) {
                  chapter.sections[sectionIndex] = { ...chapter.sections[sectionIndex], ...response.data.section };
                  if (selectedSection.value?.id == sectionId) {
                    selectedSection.value = chapter.sections[sectionIndex];
                  }
                }
              }

              // Remove from unsaved changes
              delete unsavedChanges.value.sections[sectionId];
            } catch (error) {
              console.error(`Error saving section ${sectionId}:`, error);
              errors.push(`Section ${sectionId}`);
            }
          }
        }

        updateGlobalUnsavedState();

        if (errors.length > 0) {
          alert(`Some items failed to save: ${errors.join(', ')}`);
        }
      } catch (error) {
        console.error('Error during batch save:', error);
        alert('An error occurred while saving. Please try again.');
      } finally {
        isLoading.value = false;
      }
    };

    const saveCurrentContent = saveAllUnsavedChanges;

    // Modern toast notification method
    const showNotification = (message, type = 'info') => {
      if (type === 'error') {
        toast.error('Error', message)
      } else if (type === 'success') {
        toast.success('Success', message)
      } else if (type === 'warning') {
        toast.warning('Warning', message)
      } else {
        toast.info('Info', message)
      }
    };

    const regenerateEbook = async () => {
      if (!campaign.value) return;

      isRegenerating.value = true;
      try {
        const response = await axios.post(`/api/edit-book/campaign/${campaignId}/regenerate-ebook`);

        // Show success notification
        showNotification('Ebook regeneration started successfully. Please wait a few moments and check back.', 'success');
      } catch (error) {
        console.error('Error regenerating ebook:', error);

        // Show error notification
        if (error.response?.data?.error) {
          showNotification(error.response.data.error, 'error');
        } else {
          showNotification('An error occurred while regenerating the ebook. Please try again.', 'error');
        }
      } finally {
        isRegenerating.value = false;
      }
    };

    // Keyboard shortcuts
    const handleKeydown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        saveCurrentContent();
      }
    };

    // Browser tab close warning
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges.value) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    // Handle browser back/forward navigation
    const handlePopState = () => {
      // Re-initialize selection from URL when user navigates back/forward
      if (chapters.value.length > 0) {
        initializeFromUrl();
      }
    };

    // Lifecycle
    onMounted(() => {
      loadCampaignData();
      document.addEventListener('keydown', handleKeydown);
      window.addEventListener('beforeunload', handleBeforeUnload);
      window.addEventListener('popstate', handlePopState);
    });

    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeydown);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    });

    return {
      campaign,
      chapters,
      selectedChapter,
      selectedSection,
      editingChapter,
      hasUnsavedChanges,
      hasActiveImageGeneration,
      isLoading,
      isRegenerating,
      isStandalone,
      sanitizedTitle,
      backUrl,
      unsavedChanges,
      selectChapter,
      selectSection,
      editChapter,
      cancelChapterEdit,
      updateChapter,
      updateSection,
      addChapter,
      deleteChapter,
      addSection,
      deleteSection,
      reorderSections,
      reorderChapters,
      setUnsavedChanges,
      setUnsavedChangesForChapter,
      setUnsavedChangesForSection,
      getUnsavedDataForChapter,
      getUnsavedDataForSection,
      hasUnsavedChangesForChapter,
      hasUnsavedChangesForSection,
      saveCurrentContent,
      saveAllUnsavedChanges,
      regenerateEbook,
    };
  },
};
</script>

<style scoped>
.edit-book-container {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.edit-book-container * {
  box-sizing: border-box;
}
</style>
