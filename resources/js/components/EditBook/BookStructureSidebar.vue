<template>
  <div class="w-2/4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden flex flex-col h-full">
    <!-- Fixed Header -->
    <div class="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">Book Structure</h2>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Chapters and sections</p>
        </div>
        <button
          @click="addChapter"
          class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-900 dark:text-primary-200 dark:hover:bg-primary-800">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Chapter
        </button>
      </div>
    </div>

    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto min-h-0">
      <div v-if="chapters.length === 0" class="p-4">
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No chapters</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            This campaign doesn't have any chapters yet. Please generate the content first.
          </p>
        </div>
      </div>

      <div v-else class="p-4">
        <draggable
          v-model="localChapters"
          @end="onChapterReorder"
          item-key="id"
          handle=".chapter-drag-handle"
          class="space-y-2">
          <template #item="{ element: chapter }">
            <div class="chapter-item border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
              <!-- Chapter Header -->
              <div
                class="flex items-center p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                :class="{
                  'bg-primary-50 dark:bg-primary-900/50': selectedChapter?.id === chapter.id,
                  'bg-gray-50 dark:bg-gray-700 ': selectedChapter?.id !== chapter.id
                }"
                @click.stop="$emit('edit-chapter', chapter)">

                <div class="chapter-drag-handle flex-shrink-0 mr-2 cursor-move text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                  </svg>
                </div>

                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate flex items-center min-w-0 flex-1">
                    </h3>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate" v-html="getPlainText(chapter.title)"></p>
                </div>

                <div class="flex items-center space-x-1 ml-2">
                    <svg v-if="hasUnsavedChangesForChapter(chapter.id)"
                         class="w-3 h-3 ml-1 text-amber-500 flex-shrink-0"
                         fill="currentColor"
                         viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zm2 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                    </svg>
                  <button
                    @click.stop="deleteChapter(chapter)"
                    class="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Sections -->
              <div v-if="chapter.sections && chapter.sections.length > 0" class="border-t border-gray-200 dark:border-gray-600">
                <draggable
                  v-model="chapter.sections"
                  @end="(evt) => onSectionReorder(chapter.id, evt)"
                  item-key="id"
                  handle=".section-drag-handle"
                  class="divide-y divide-gray-200 dark:divide-gray-600">
                  <template #item="{ element: section }">
                    <div
                      class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                      :class="{ 'bg-primary-50 dark:bg-primary-900/50': selectedSection?.id === section.id }"
                      @click="$emit('select-section', section)">

                      <div class="section-drag-handle flex-shrink-0 mr-2 cursor-move text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                        </svg>
                      </div>

                      <div class="flex-1 min-w-0">
                        <div class="flex items-center">
                          <h4 class="text-sm text-gray-700 dark:text-gray-300 truncate flex-1 min-w-0" v-html="getPlainText(section.title)"></h4>
                          <svg v-if="hasUnsavedChangesForSection(section.id)"
                               class="w-3 h-3 ml-1 text-amber-500 flex-shrink-0"
                               fill="currentColor"
                               viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zm2 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                      </div>

                      <button
                        @click.stop="$emit('delete-section', section.id)"
                        class="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors ml-2">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                      </button>
                    </div>
                  </template>
                </draggable>
              </div>

              <!-- Add Section Button -->
              <div class="p-3 border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
                <button
                  @click="$emit('add-section', chapter.id)"
                  class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                  Add Section
                </button>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';
import draggable from 'vuedraggable';
import { useConfirmation } from '../../composables/useConfirmation.js';

export default {
  name: 'BookStructureSidebar',
  components: {
    draggable,
  },
  props: {
    chapters: {
      type: Array,
      required: true,
    },
    selectedChapter: {
      type: Object,
      default: null,
    },
    selectedSection: {
      type: Object,
      default: null,
    },
    editingChapter: {
      type: Boolean,
      default: false,
    },
    unsavedChanges: {
      type: Object,
      default: () => ({ chapters: {}, sections: {} }),
    },
  },
  emits: [
    'select-chapter',
    'select-section',
    'edit-chapter',
    'add-chapter',
    'delete-chapter',
    'add-section',
    'delete-section',
    'reorder-sections',
    'reorder-chapters',
  ],
  setup(props, { emit }) {
    const localChapters = ref([...props.chapters]);
    const confirmation = useConfirmation();

    // Watch for changes in props.chapters
    watch(() => props.chapters, (newChapters) => {
      localChapters.value = [...newChapters];
    }, { deep: true });

    const getStatusClasses = (status) => {
      switch (status) {
        case 'DONE':
          return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200';
        case 'IN PROGRESS':
          return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200';
        case 'PENDING':
          return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
        case 'ERROR':
        case 'FAILED':
          return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200';
        default:
          return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      }
    };

    const getPlainText = (html) => {
      if (!html) return '';
      const div = document.createElement('div');
      div.innerHTML = html;
      return div.textContent || div.innerText || '';
    };

    const onChapterReorder = (evt) => {
      const reorderedChapters = localChapters.value.map((chapter, index) => ({
        id: chapter.id,
        order: index + 1,
      }));
      emit('reorder-chapters', reorderedChapters);
    };

    const onSectionReorder = (chapterId, evt) => {
      const chapter = localChapters.value.find(c => c.id === chapterId);
      if (chapter) {
        const reorderedSections = chapter.sections.map((section, index) => ({
          id: section.id,
          order: index + 1,
        }));
        emit('reorder-sections', reorderedSections);
      }
    };

    const addChapter = () => {
      emit('add-chapter');
    };

    const deleteChapter = async (chapter) => {
      const confirmed = await confirmation.confirmDeleteChapter(chapter.chapter_number, chapter.title);
      if (confirmed) {
        emit('delete-chapter', chapter.id);
      }
    };

    const hasUnsavedChangesForChapter = (chapterId) => {
      return !!props.unsavedChanges.chapters[chapterId]?.hasChanges;
    };

    const hasUnsavedChangesForSection = (sectionId) => {
      return !!props.unsavedChanges.sections[sectionId]?.hasChanges;
    };

    return {
      localChapters,
      getStatusClasses,
      getPlainText,
      onChapterReorder,
      onSectionReorder,
      addChapter,
      deleteChapter,
      hasUnsavedChangesForChapter,
      hasUnsavedChangesForSection,
    };
  },
};
</script>

<style scoped>
.chapter-item {
  transition: all 0.2s ease;
}

.chapter-item:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  transform: rotate(5deg);
}
</style>
