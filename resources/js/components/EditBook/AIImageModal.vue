<template>
  <div v-if="isVisible" class="ai-modal-overlay" @click="closeModal">
    <div class="ai-modal" @click.stop>
      <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Generate AI Image</h2>

      <!-- AI Model Selection -->
      <div class="mb-4">
        <label for="ai-model-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          AI Model
        </label>
        <select
          id="ai-model-select"
          v-model="selectedModel"
          class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          :disabled="isLoadingModels"
        >
          <option value="" disabled>
            {{ isLoadingModels ? 'Loading models...' : 'Select AI Model' }}
          </option>
          <option
            v-for="(label, value) in availableModels"
            :key="value"
            :value="value"
          >
            {{ label }}
          </option>
        </select>
        <p v-if="!isLoadingModels && Object.keys(availableModels).length === 0" class="text-sm text-red-600 dark:text-red-400 mt-1">
          No AI image models available. Please configure your OpenAI API key in settings.
        </p>
      </div>

      <div class="mb-4">
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              type="radio"
              v-model="generationType"
              value="content"
              class="mr-2"
            >
            <span class="text-gray-700 dark:text-gray-300">Using current content</span>
          </label>
          <label class="flex items-center">
            <input
              type="radio"
              v-model="generationType"
              value="keyword"
              class="mr-2"
            >
            <span class="text-gray-700 dark:text-gray-300">Using custom prompt</span>
          </label>
        </div>
      </div>

      <div v-if="generationType === 'keyword'" class="mb-4">
        <textarea
          v-model="customPrompt"
          placeholder="Enter your image description..."
          class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none"
          rows="3"
          @keydown.enter.prevent="generateImage"
        ></textarea>
      </div>

      <div class="flex justify-end space-x-3">
        <button
          @click="closeModal"
          class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="generateImage"
          :disabled="!canGenerate"
          class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Generate
        </button>
      </div>
    </div>
  </div>
</template>

<script>
// Global cache for AI models to prevent multiple API calls
let globalModelsCache = null;
let globalModelsPromise = null;

async function loadAIModels() {
  // If we already have cached models, return them
  if (globalModelsCache) {
    return globalModelsCache;
  }

  // If there's already a request in progress, wait for it
  if (globalModelsPromise) {
    return await globalModelsPromise;
  }

  // Create a new request
  globalModelsPromise = (async () => {
    try {
      const response = await fetch('/ai-image-models', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load AI models');
      }

      const data = await response.json();
      globalModelsCache = {
        models: data.models || {},
        default: data.default
      };

      return globalModelsCache;
    } catch (error) {
      console.error('Error loading AI models:', error);
      globalModelsCache = { models: {}, default: null };
      return globalModelsCache;
    } finally {
      // Clear the promise so future calls can make new requests if needed
      globalModelsPromise = null;
    }
  })();

  return await globalModelsPromise;
}

export default {
  name: 'AIImageModal',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    editorContent: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'generate-image'],
  data() {
    return {
      generationType: 'content',
      customPrompt: '',
      selectedModel: '',
      availableModels: {},
      isLoadingModels: true
    }
  },
  computed: {
    canGenerate() {
      const hasModel = !!this.selectedModel;
      const hasValidPrompt = this.generationType === 'content' ||
                            (this.generationType === 'keyword' && this.customPrompt.trim());
      return hasModel && hasValidPrompt && !this.isLoadingModels;
    }
  },
  async mounted() {
    await this.loadAvailableModels();
  },
  methods: {
    async loadAvailableModels() {
      try {
        this.isLoadingModels = true;

        // Use the global cache function
        const data = await loadAIModels();
        this.availableModels = data.models;

        // Set default model if available
        if (data.default && this.availableModels[data.default]) {
          this.selectedModel = data.default;
        } else if (Object.keys(this.availableModels).length > 0) {
          // If no default, select the first available model
          this.selectedModel = Object.keys(this.availableModels)[0];
        }
      } catch (error) {
        console.error('Error loading AI models:', error);
        this.availableModels = {};
      } finally {
        this.isLoadingModels = false;
      }
    },

    closeModal() {
      this.$emit('close');
      this.resetModal();
    },

    resetModal() {
      this.generationType = 'content';
      this.customPrompt = '';
      // Don't reset selectedModel to preserve user's choice
    },

    generateImage() {
      if (!this.canGenerate) {
        return;
      }

      const content = this.generationType === 'content'
        ? this.editorContent.replace(/<[^>]+>/g, '').trim()
        : this.customPrompt.trim();

      // Include the selected model in the emission
      this.$emit('generate-image', {
        prompt: content,
        model: this.selectedModel
      });
      this.closeModal();
    }
  }
}
</script>

<style scoped>
.ai-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.ai-modal {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.dark .ai-modal {
  background: #374151;
}
</style>
