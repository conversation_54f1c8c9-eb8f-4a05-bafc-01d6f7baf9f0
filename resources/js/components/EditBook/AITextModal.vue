<template>
  <div v-if="isVisible" class="ai-modal-overlay" @click="closeModal">
    <div class="ai-modal" @click.stop>
      <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Generate AI Content</h2>

      <div class="mb-4" v-if="hasSelectedText">
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              type="radio"
              v-model="generationType"
              value="expand"
              class="mr-2"
            >
            <span class="text-gray-700 dark:text-gray-300">Extend selected text</span>
          </label>
          <label class="flex items-center">
            <input
              type="radio"
              v-model="generationType"
              value="custom"
              class="mr-2"
            >
            <span class="text-gray-700 dark:text-gray-300">Custom prompt</span>
          </label>
        </div>
      </div>

      <div class="mb-4" v-if="!hasSelectedText">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
          No text selected. AI will generate content based on the context around your cursor position.
        </p>
      </div>

      <div v-if="(hasSelectedText && generationType === 'custom') || !hasSelectedText" class="mb-4">
        <textarea
          v-model="customPrompt"
          :placeholder="hasSelectedText ? 'Enter your custom prompt...' : 'Enter your prompt for text generation...'"
          class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none"
          rows="3"
          @keydown.enter.prevent="generateText"
        ></textarea>
      </div>

      <div class="flex justify-end space-x-3">
        <button
          @click="closeModal"
          class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="generateText"
          :disabled="shouldDisableGenerate"
          class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Generate
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AITextModal',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    selectedText: {
      type: String,
      default: ''
    },
    editorContent: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'generate-text'],
  data() {
    return {
      generationType: 'expand',
      customPrompt: ''
    }
  },
  computed: {
    hasSelectedText() {
      return this.selectedText && this.selectedText.trim().length > 0
    },
    shouldDisableGenerate() {
      if (!this.hasSelectedText) {
        // No selection - require custom prompt
        return !this.customPrompt.trim()
      } else {
        // Has selection - only disable if custom type and no prompt
        return this.generationType === 'custom' && !this.customPrompt.trim()
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('close');
      this.resetModal();
    },

    resetModal() {
      this.generationType = 'expand';
      this.customPrompt = '';
    },

    generateText() {
      if (this.shouldDisableGenerate) {
        return;
      }

      let prompt = "Respond only with partial HTML content, suitable for use inside a blog post. Improve and expand the following text for clarity, structure, and readability — but keep it concise. Use only content-level tags such as <p>, <ul>, <li>, <strong>, <em>, <code>, <a>, etc. Do not include <div>, <section>, or any inline styles or layout tags. \n";

      if (this.hasSelectedText) {
        if (this.generationType === 'expand') {
          prompt += this.selectedText;
        } else {
          prompt += this.customPrompt.trim();
        }
      } else {
        // No selection - use custom prompt for context-based generation
        prompt += this.customPrompt.trim();
      }

      this.$emit('generate-text', prompt);
      this.closeModal();
    }
  }
}
</script>

<style scoped>
.ai-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.ai-modal {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.dark .ai-modal {
  background: #374151;
}
</style>
