<template>
  <div class="rich-text-editor border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 flex flex-col">
    <!-- Toolbar -->
    <div class="border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 p-2">
      <div class="flex items-center space-x-2 flex-wrap">
        <!-- Text Formatting Group -->
        <div class="flex items-center space-x-1 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600 p-1">
          <!-- Bold -->
          <button
            @click="editor?.chain().focus().toggleBold().run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('bold') }"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Bold">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 4h8a4 4 0 014 4 4 4 0 01-4 4H6z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 12h9a4 4 0 014 4 4 4 0 01-4 4H6z"/>
            </svg>
          </button>

          <!-- Italic -->
          <button
            @click="editor?.chain().focus().toggleItalic().run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('italic') }"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Italic">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 4l4 16M6 8h12M4 16h12"/>
            </svg>
          </button>

          <!-- Underline -->
          <button
            @click="editor?.chain().focus().toggleUnderline().run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('underline') }"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Underline">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v8a5 5 0 0010 0V4M5 20h14"/>
            </svg>
          </button>

          <!-- Strike -->
          <button
            @click="editor?.chain().focus().toggleStrike().run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('strike') }"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Strikethrough">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 12h12M6 8h12M6 16h12"/>
            </svg>
          </button>
        </div>

        <!-- Headings Group -->
        <div class="flex items-center space-x-1 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600 p-1">
          <!-- Heading 1 -->
          <button
            @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('heading', { level: 1 }) }"
            class="px-2 py-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            title="Heading 1">
            H1
          </button>

          <!-- Heading 2 -->
          <button
            @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('heading', { level: 2 }) }"
            class="px-2 py-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            title="Heading 2">
            H2
          </button>

          <!-- Heading 3 -->
          <button
            @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('heading', { level: 3 }) }"
            class="px-2 py-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xs font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            title="Heading 3">
            H3
          </button>
        </div>

        <!-- Lists Group -->
        <div class="flex items-center space-x-1 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600 p-1">
          <!-- Bullet List -->
          <button
            @click="editor?.chain().focus().toggleBulletList().run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('bulletList') }"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Bullet List">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>

          <!-- Ordered List -->
          <button
            @click="editor?.chain().focus().toggleOrderedList().run()"
            :disabled="!editor"
            :class="{ 'bg-primary-100 dark:bg-primary-900': editor?.isActive('orderedList') }"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Numbered List">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </button>
        </div>

        <!-- History Group -->
        <div class="flex items-center space-x-1 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600 p-1">
          <!-- Undo -->
          <button
            @click="editor?.chain().focus().undo().run()"
            :disabled="!editor || !editor?.can().undo()"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"/>
            </svg>
          </button>

          <!-- Redo -->
          <button
            @click="editor?.chain().focus().redo().run()"
            :disabled="!editor || !editor?.can().redo()"
            class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Redo">
            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6"/>
            </svg>
          </button>
        </div>

          <!-- Image Tools Group -->
          <div class="flex items-center space-x-1">
            <!-- Manual Image Upload -->
            <button
                @click="triggerImageUpload"
                :disabled="!editor || isUploadingImage"
                class="flex items-center gap-1 px-2 py-1.5 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs font-medium border border-gray-300 dark:border-gray-600"
                title="Upload Image from Device">
                <svg v-if="!isUploadingImage" class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                </svg>
                <svg v-else class="w-3.5 h-3.5 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{{ isUploadingImage ? 'Uploading...' : 'Upload' }}</span>
            </button>

            <!-- AI Image Generation -->
            <button
                v-if="!hasActiveImageGeneration"
                @click="showImageModal = true"
                :disabled="!editor"
                class="flex items-center gap-1 px-2 py-1.5 rounded hover:bg-blue-50 dark:hover:bg-blue-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs font-medium border border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300"
                title="Generate Image with AI">
                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 2L3 14h9l-1 8L21 10h-9l1-8z" fill="currentColor" opacity="0.6"/>
                </svg>
                <span>AI Image</span>
            </button>

            <!-- Cancel Image Generation -->
            <button
                v-if="hasActiveImageGeneration"
                @click="cancelImageGeneration"
                class="flex items-center gap-1 px-2 py-1.5 rounded hover:bg-red-50 dark:hover:bg-red-900 transition-colors text-xs font-medium border border-red-300 dark:border-red-600 text-red-700 dark:text-red-300"
                title="Cancel Image Generation">
                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
                <span>Cancel</span>
            </button>
          </div>

          <!-- AI Text Generation -->
          <button
              v-if="!isGeneratingText"
              @click="showTextModal = true"
              :disabled="!editor"
              class="flex items-center gap-1 px-2 py-1.5 rounded hover:bg-purple-50 dark:hover:bg-purple-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs font-medium border border-purple-300 dark:border-purple-600 text-purple-700 dark:text-purple-300"
              title="Generate Text with AI">
              <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 2L3 14h9l-1 8L21 10h-9l1-8z" fill="currentColor" opacity="0.6"/>
              </svg>
              <span>AI Text</span>
          </button>

          <!-- Stop Text Generation -->
          <button
              v-if="isGeneratingText"
              @click="stopTextGeneration"
              class="flex items-center gap-1 px-2 py-1.5 rounded hover:bg-red-50 dark:hover:bg-red-900 transition-colors text-xs font-medium border border-red-300 dark:border-red-600 text-red-700 dark:text-red-300"
              title="Stop Text Generation">
              <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
              <span>Stop</span>
          </button>
      </div>
    </div>

    <!-- Editor Content -->
    <div class="flex-1 overflow-y-auto">
      <editor-content
        :editor="editor"
        class="prose prose-sm max-w-none focus:outline-none dark:prose-invert h-full"
        :style="{ minHeight: minHeight + 'px' }"
      />
    </div>
  </div>

  <!-- Hidden File Input for Image Upload -->
  <input
    ref="imageFileInput"
    type="file"
    accept="image/*"
    @change="handleImageUpload"
    class="hidden"
  />

  <!-- AI Modals -->
  <AIImageModal
    :is-visible="showImageModal"
    :editor-content="modelValue"
    @close="showImageModal = false"
    @generate-image="handleImageGeneration"
  />

  <AITextModal
    :is-visible="showTextModal"
    :selected-text="getSelectedTextForModal()"
    :editor-content="modelValue"
    @close="showTextModal = false"
    @generate-text="handleTextGeneration"
  />
</template>

<script>
import { watch, ref, computed } from 'vue';
import { useEditor, EditorContent } from '@tiptap/vue-3';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Bold from '@tiptap/extension-bold';
import Italic from '@tiptap/extension-italic';
import Underline from '@tiptap/extension-underline';
import Strike from '@tiptap/extension-strike';
import Heading from '@tiptap/extension-heading';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';
import History from '@tiptap/extension-history';
import HardBreak from '@tiptap/extension-hard-break';
import Image from '@tiptap/extension-image';
import AIImageModal from './AIImageModal.vue';
import AITextModal from './AITextModal.vue';
import { useAITextGeneration } from '../../composables/useAITextGeneration.js';
import { useAIImageGeneration } from '../../composables/useAIImageGeneration.js';
import { useEditorUtils } from '../../composables/useEditorUtils.js';

export default {
  name: 'RichTextEditor',
  components: {
    EditorContent,
    AIImageModal,
    AITextModal,
  },
  props: {
    modelValue: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: 'Start typing...',
    },
    minHeight: {
      type: Number,
      default: 200,
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    // Initialize composables
    const textGeneration = useAITextGeneration()
    const imageGeneration = useAIImageGeneration()
    const { getSelectedText } = useEditorUtils()

    // Modal state
    const showImageModal = ref(false)
    const showTextModal = ref(false)

    // Image upload state
    const isUploadingImage = ref(false)
    const imageFileInput = ref(null)

    const editor = useEditor({
      content: props.modelValue || '',
      extensions: [
        Document,
        Paragraph,
        Text,
        Bold,
        Italic,
        Underline,
        Strike,
        Heading.configure({
          levels: [1, 2, 3],
        }),
        BulletList,
        OrderedList,
        ListItem,
        History,
        HardBreak,
        Image.configure({
          inline: true,
          allowBase64: true,
        }),
      ],
      editorProps: {
        attributes: {
          class: 'prose prose-sm max-w-none focus:outline-none dark:prose-invert',
          style: `min-height: ${props.minHeight}px; padding: 1rem;`,
        },
      },
      onUpdate: ({ editor }) => {
        const content = editor.getHTML();
        emit('update:modelValue', content);
      },
    });

    // Watch for external changes to modelValue
    watch(() => props.modelValue, (newValue) => {
      if (!editor.value) return;

      const currentContent = editor.value.getHTML();
      const newContent = newValue || '';

      // Only update if content is actually different
      if (currentContent !== newContent) {
        editor.value.commands.setContent(newContent, false);
      }
    });

    // Computed properties
    const hasActiveImageGeneration = computed(() => {
      return imageGeneration.isGenerating.value || imageGeneration.activeGenerations.size > 0
    })

    const isGeneratingText = computed(() => {
      return textGeneration.isGenerating.value
    })

    return {
      editor,
      showImageModal,
      showTextModal,
      hasActiveImageGeneration,
      isGeneratingText,
      isUploadingImage,
      imageFileInput,
      textGeneration,
      imageGeneration,
      getSelectedText
    };
  },
  methods: {
    // Handle text generation from modal
    handleTextGeneration(prompt) {
      if (!this.editor) return
      this.textGeneration.generateText(this.editor, prompt)
    },

    // Handle image generation from modal
    handleImageGeneration(prompt) {
      if (!this.editor) return
      this.imageGeneration.generateImage(this.editor, prompt)
    },

    // Stop text generation
    stopTextGeneration() {
      this.textGeneration.stopGeneration()
    },

    // Cancel image generation
    cancelImageGeneration() {
      // Remove all placeholders for active generations
      for (const [generationId, { placeholderId }] of this.imageGeneration.activeGenerations) {
        this.imageGeneration.removeImagePlaceholder(this.editor, placeholderId)
      }
      this.imageGeneration.cancelAllGenerations()
    },

    // Get selected text for modal
    getSelectedTextForModal() {
      return this.getSelectedText(this.editor)
    },

    // Trigger image upload
    triggerImageUpload() {
      if (this.imageFileInput) {
        this.imageFileInput.click()
      }
    },

    // Handle image upload
    async handleImageUpload(event) {
      const file = event.target.files[0]
      if (!file || !this.editor) return

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.showNotification('Please select a valid image file.', 'error')
        return
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        this.showNotification('Image file size must be less than 10MB.', 'error')
        return
      }

      this.isUploadingImage = true

      try {
        // Create FormData for file upload
        const formData = new FormData()
        formData.append('image', file)

        // Upload image to server
        const response = await fetch('/api/upload-image', {
          method: 'POST',
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
          },
          body: formData
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || 'Failed to upload image')
        }

        const data = await response.json()

        if (!data.url) {
          throw new Error('No image URL received from server')
        }

        // Insert image into editor
        this.editor.chain().focus().setImage({ src: data.url, alt: file.name }).run()
        this.showNotification('Image uploaded successfully!', 'success')

      } catch (error) {
        console.error('Error uploading image:', error)
        this.showNotification(`Error uploading image: ${error.message}`, 'error')
      } finally {
        this.isUploadingImage = false
        // Clear the file input
        if (this.imageFileInput) {
          this.imageFileInput.value = ''
        }
      }
    },

    // Modern toast notification method
    showNotification(message, type = 'info') {
      if (window.toast) {
        if (type === 'error') {
          window.toast.error('Error', message)
        } else if (type === 'success') {
          window.toast.success('Success', message)
        } else if (type === 'warning') {
          window.toast.warning('Warning', message)
        } else {
          window.toast.info('Info', message)
        }
      } else {
        // Fallback to alert if toast is not available
        if (type === 'error') {
          alert(`❌ ${message}`)
        } else if (type === 'success') {
          alert(`✅ ${message}`)
        } else {
          alert(`ℹ️ ${message}`)
        }
      }
    }
  }
};
</script>

<style scoped>
.rich-text-editor {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

/* TipTap editor styles */
:deep(.ProseMirror) {
  outline: none;
  padding: 1rem;
  min-height: 200px;
  overflow-y: auto;
  max-height: none;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

:deep(.ProseMirror h1) {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 2.25rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

:deep(.ProseMirror h2) {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 2rem;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

:deep(.ProseMirror h3) {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.75rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror ul) {
  list-style-type: disc;
  margin-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  margin-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror li) {
  margin-bottom: 0.25rem;
}

:deep(.ProseMirror p) {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror p:first-child) {
  margin-top: 0;
}

:deep(.ProseMirror p:last-child) {
  margin-bottom: 0;
}

/* Prevent extra spacing in list items */
:deep(.ProseMirror li p) {
  margin-top: 0;
  margin-bottom: 0;
}

:deep(.ProseMirror strong) {
  font-weight: 700;
}

:deep(.ProseMirror em) {
  font-style: italic;
}

:deep(.ProseMirror u) {
  text-decoration: underline;
}

:deep(.ProseMirror s) {
  text-decoration: line-through;
}

/* AI Generation Styles */
:deep(.ProseMirror img[src^="data:image/svg+xml"]) {
  border: 2px dashed #3b82f6;
  border-radius: 8px;
  background: #f8fafc;
  opacity: 0.8;
}

.dark :deep(.ProseMirror img[src^="data:image/svg+xml"]) {
  background: #1e293b;
  border-color: #60a5fa;
}



/* AI Generation Placeholder */
:deep(.ai-generation-placeholder) {
  background: #3b82f6 !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  display: inline-block !important;
  animation: pulse 1.5s infinite !important;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.dark :deep(.ai-generation-placeholder) {
  background: #60a5fa !important;
  color: #1e293b !important;
}
</style>
