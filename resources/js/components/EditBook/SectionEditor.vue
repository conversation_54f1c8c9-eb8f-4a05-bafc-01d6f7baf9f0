<template>
  <div class="h-full flex flex-col">
    <!-- Section Header -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">
            Edit Section
          </h2>
        </div>
        <div class="flex items-center space-x-2 ml-4">
          <button
            @click="saveSection"
            :disabled="!hasChanges || isSaving || hasActiveImageGeneration"
            class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Save Section
          </button>
        </div>
      </div>
    </div>

    <!-- Section Editor Area -->
    <div class="flex-1 p-4 overflow-y-auto">
      <div class="h-full flex flex-col space-y-4">
        <!-- Section Title -->
        <div>
          <label for="section-title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Section Title
          </label>
          <RichTextEditor
            v-model="formData.title"
            :placeholder="'Enter section title...'"
            :min-height="100"
            @update:modelValue="onContentChange"
          />
        </div>

        <!-- Section Intro -->
        <div>
          <label for="section-intro" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Section Introduction
          </label>
          <RichTextEditor
            v-model="formData.intro"
            :placeholder="'Enter section introduction...'"
            :min-height="150"
            @update:modelValue="onContentChange"
          />
        </div>

        <!-- Section Content -->
        <div class="flex-1 flex flex-col min-h-0">
          <label for="section-content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Section Content
          </label>
          <div class="flex-1 min-h-0">
            <RichTextEditor
              v-model="formData.body"
              :placeholder="'Enter section content...'"
              :min-height="400"
              @update:modelValue="onContentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch, computed, onMounted, onBeforeUnmount } from 'vue';
import RichTextEditor from './RichTextEditor.vue';
import { useAIImageGeneration } from '../../composables/useAIImageGeneration.js';

export default {
  name: 'SectionEditor',
  components: {
    RichTextEditor,
  },
  props: {
    section: {
      type: Object,
      required: true,
    },
    unsavedData: {
      type: Object,
      default: null,
    },
  },
  emits: ['update', 'content-changed'],
  setup(props, { emit }) {
    // Initialize AI image generation composable
    const imageGeneration = useAIImageGeneration();

    const isSaving = ref(false);
    const originalData = ref({});

    const formData = reactive({
      title: '',
      intro: '',
      body: '',
    });

    // Initialize form data
    const initializeForm = () => {
      // Use unsaved data if available, otherwise use section data
      if (props.unsavedData && props.unsavedData.hasChanges) {
        formData.title = props.unsavedData.title || '';
        formData.intro = props.unsavedData.intro || '';
        formData.body = props.unsavedData.body || '';
      } else {
        formData.title = props.section.title || '';
        formData.intro = props.section.intro || '';
        formData.body = props.section.body || '';
      }

      // Store original data for comparison (always from the saved section)
      originalData.value = {
        title: props.section.title || '',
        intro: props.section.intro || '',
        body: props.section.body || '',
      };
    };

    // Check if there are unsaved changes
    const hasChanges = computed(() => {
      return formData.title !== originalData.value.title ||
             formData.intro !== originalData.value.intro ||
             formData.body !== originalData.value.body;
    });

    // Check if there are active image generations
    const hasActiveImageGeneration = computed(() => {
      return imageGeneration.isGenerating.value || imageGeneration.activeGenerations.size > 0;
    });

    // Watch for changes and emit to parent
    watch(hasChanges, (newValue) => {
      emit('content-changed', {
        title: formData.title,
        intro: formData.intro,
        body: formData.body,
      }, newValue);
    });

    // Also watch for immediate content changes to emit updates
    watch([() => formData.title, () => formData.intro, () => formData.body], () => {
      emit('content-changed', {
        title: formData.title,
        intro: formData.intro,
        body: formData.body,
      }, hasChanges.value);
    });



    const onContentChange = () => {
      // This will trigger the hasChanges computed property
      // which will then emit the content-changed event
    };

    const saveSection = async () => {
      if (!hasChanges.value) return;

      // Basic validation
      if (!formData.title.trim()) {
        alert('Section title is required');
        return;
      }

      isSaving.value = true;

      try {
        await emit('update', {
          title: formData.title,
          intro: formData.intro,
          body: formData.body,
        });

        // Update original data after successful save
        originalData.value = {
          title: formData.title,
          intro: formData.intro,
          body: formData.body,
        };
      } catch (error) {
        console.error('Error saving section:', error);
      } finally {
        isSaving.value = false;
      }
    };

    // Lifecycle
    onMounted(() => {
      initializeForm();
    });

    // Watch for prop changes
    watch(() => props.section, () => {
      initializeForm();
    }, { deep: true });

    return {
      formData,
      hasChanges,
      hasActiveImageGeneration,
      isSaving,
      onContentChange,
      saveSection,
    };
  },
};
</script>

<style scoped>
/* Component-specific styles */
</style>
