<template>
  <div v-if="hasUnsavedChanges" class="flex items-center space-x-2">
    <div class="flex items-center space-x-1 text-amber-600 dark:text-amber-400">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
      </svg>
      <span class="text-sm font-medium">Unsaved changes</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UnsavedChangesIndicator',
  props: {
    hasUnsavedChanges: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped>
/* Component-specific styles */
</style>
