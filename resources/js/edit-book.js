import { createApp } from 'vue';
import EditBookApp from '@/components/EditBook/EditBookApp.vue';
import axios from 'axios';
import { useToast } from '@/composables/useToast.js';

// Configure axios defaults
window.axios = axios;
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Get CSRF token from meta tag
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Note: withCredentials is not needed for same-origin requests
// window.axios.defaults.withCredentials = true;

// Make toast available globally
window.toast = useToast();

// Create and mount the Vue app
const app = createApp(EditBookApp);

// Global error handler
app.config.errorHandler = (err, instance, info) => {
    console.error('Vue error:', err, info);
};

// Mount the app
app.mount('#edit-book-app');
