import { ref } from 'vue'

/**
 * Editor utility functions for text manipulation and cursor management
 */
export function useEditorUtils() {
  /**
   * Get selected text from editor
   */
  const getSelectedText = (editor) => {
    if (!editor) return ''

    const { from, to } = editor.state.selection
    return editor.state.doc.textBetween(from, to, ' ')
  }

  /**
   * Extract context around cursor position (500 words before and after)
   */
  const getContextAroundCursor = (editor) => {
    if (!editor) return { before: '', after: '' }

    const { from } = editor.state.selection
    const doc = editor.state.doc
    const fullText = doc.textBetween(0, doc.content.size, ' ')

    // Find cursor position in plain text
    const textBeforeCursor = doc.textBetween(0, from, ' ')
    const cursorPosition = textBeforeCursor.length

    // Split text into words
    const words = fullText.split(/\s+/)
    const wordsBeforeCursor = textBeforeCursor.split(/\s+/)
    const cursorWordIndex = wordsBeforeCursor.length - 1

    // Extract 500 words before and after
    const startIndex = Math.max(0, cursorWordIndex - 500)
    const endIndex = Math.min(words.length, cursorWordIndex + 500)

    const beforeWords = words.slice(startIndex, cursorWordIndex)
    const afterWords = words.slice(cursorWordIndex, endIndex)

    return {
      before: beforeWords.join(' '),
      after: afterWords.join(' ')
    }
  }

  /**
   * Insert content at cursor position
   */
  const insertContentAtCursor = (editor, content) => {
    if (!editor) return

    editor.chain()
      .focus()
      .insertContent(content)
      .run()
  }

  /**
   * Insert content on next line (create new line if cursor is mid-line)
   */
  const insertContentOnNextLine = (editor, content) => {
    if (!editor) return

    const { from } = editor.state.selection
    const doc = editor.state.doc
    const $pos = doc.resolve(from)

    // Check if cursor is at the end of a line
    const isAtEndOfLine = $pos.parentOffset === $pos.parent.content.size

    if (isAtEndOfLine) {
      // Just add a new line and insert content
      editor.chain()
        .focus()
        .insertContent('\n' + content)
        .run()
    } else {
      // Move to end of current line, then add new line and content
      editor.chain()
        .focus()
        .command(({ tr, state }) => {
          const lineEnd = $pos.end()
          tr.setSelection(state.selection.constructor.near(doc.resolve(lineEnd)))
          return true
        })
        .insertContent('\n' + content)
        .run()
    }
  }

  /**
   * Replace selected text with new content
   */
  const replaceSelectedText = (editor, content) => {
    if (!editor) return

    const { from, to } = editor.state.selection

    if (from === to) {
      // No selection, just insert
      insertContentAtCursor(editor, content)
    } else {
      // Replace selection
      editor.chain()
        .focus()
        .deleteSelection()
        .insertContent(content)
        .run()
    }
  }

  /**
   * Create a loading placeholder text (plain text that TipTap can handle)
   */
  const createLoadingPlaceholder = (text = 'Loading...') => {
    return `🤖 ${text}`
  }

  /**
   * Remove loading placeholder from editor content using ProseMirror transactions
   */
  const removePlaceholder = (editor, placeholderText) => {
    if (!editor) return

    const { state } = editor
    const { doc } = state
    let tr = state.tr
    let found = false

    // Walk through the document to find placeholder text nodes
    doc.descendants((node, pos) => {
      if (node.isText && node.text) {
        // Check if this text node contains the placeholder text
        if (node.text.includes(placeholderText) || node.text.includes('🤖 Generating...')) {
          // If the entire node is just the placeholder, delete the whole node
          if (node.text.trim() === placeholderText.trim() || node.text.trim() === '🤖 Generating...') {
            tr = tr.delete(pos, pos + node.nodeSize)
            found = true
            return false
          }
          // If the placeholder is part of a larger text node, replace just that part
          else {
            const newText = node.text.replace(placeholderText, '').replace('🤖 Generating...', '')
            tr = tr.replaceWith(pos, pos + node.nodeSize, state.schema.text(newText))
            found = true
            return false
          }
        }
      }
    })

    if (found) {
      editor.view.dispatch(tr)
    }
  }

  /**
   * Clean HTML content by removing markdown code blocks and extra formatting
   */
  const cleanHtmlContent = (content) => {
    let cleaned = content
      .replace(/\\"/g, '"')
      .replace(/\\\//g, '/')
      .replace(/```html\s*/gi, '')
      .replace(/```\s*/gi, '')
      .trim()

    // Remove paragraph tags around list items
    cleaned = cleaned.replace(/<p>\s*(<li[^>]*>.*?<\/li>)\s*<\/p>/gi, '$1')

    // Remove empty paragraphs
    cleaned = cleaned.replace(/<p>\s*<\/p>/gi, '')

    // Clean up excessive whitespace between tags
    cleaned = cleaned.replace(/>\s+</g, '><')

    return cleaned
  }

  /**
   * Replace image placeholder with actual image using ProseMirror transactions
   */
  const replaceImagePlaceholder = (editor, placeholderSrc, newImageSrc, altText = 'Generated image') => {
    if (!editor) return false

    const { state } = editor
    const { doc } = state
    let tr = state.tr
    let found = false

    // Walk through the document to find image nodes with placeholder src
    doc.descendants((node, pos) => {
      if (node.type.name === 'image' && node.attrs.src === placeholderSrc) {
        // Replace the image src attribute
        tr = tr.setNodeMarkup(pos, null, {
          ...node.attrs,
          src: newImageSrc,
          alt: altText
        })
        found = true
        return false // Stop after finding the first match
      }
    })

    if (found) {
      editor.view.dispatch(tr)
      return true
    }

    return false
  }

  /**
   * Remove image placeholder using ProseMirror transactions
   */
  const removeImagePlaceholder = (editor, placeholderSrc) => {
    if (!editor) return false

    const { state } = editor
    const { doc } = state
    let tr = state.tr
    let found = false

    // Walk through the document to find image nodes with placeholder src
    doc.descendants((node, pos) => {
      if (node.type.name === 'image' && node.attrs.src === placeholderSrc) {
        // Remove the entire image node
        tr = tr.delete(pos, pos + node.nodeSize)
        found = true
        return false // Stop after finding the first match
      }
    })

    if (found) {
      editor.view.dispatch(tr)
      return true
    }

    return false
  }

  /**
   * Check if content contains HTML tags
   */
  const isHtmlContent = (content) => {
    const htmlPattern = /<\/?(?:section|div|p|h[1-6]|span|strong|em|ul|ol|li|br|img|a)\b[^>]*>/i
    return htmlPattern.test(content) || content.includes('```html')
  }

  return {
    getSelectedText,
    getContextAroundCursor,
    insertContentAtCursor,
    insertContentOnNextLine,
    replaceSelectedText,
    createLoadingPlaceholder,
    removePlaceholder,
    replaceImagePlaceholder,
    removeImagePlaceholder,
    cleanHtmlContent,
    isHtmlContent
  }
}
