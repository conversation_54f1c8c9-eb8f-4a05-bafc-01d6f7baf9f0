import { createApp, h } from 'vue'
import ConfirmationModal from '../components/UI/ConfirmationModal.vue'

export function useConfirmation() {
  const showConfirmation = (options) => {
    return new Promise((resolve) => {
      // Create a container for the modal
      const container = document.createElement('div')
      document.body.appendChild(container)

      // Create the Vue app instance
      const app = createApp({
        render() {
          return h(ConfirmationModal, {
            title: options.title,
            message: options.message,
            confirmText: options.confirmText,
            cancelText: options.cancelText,
            onConfirm: () => {
              cleanup()
              resolve(true)
            },
            onCancel: () => {
              cleanup()
              resolve(false)
            }
          })
        }
      })

      // Mount the app
      app.mount(container)

      // Cleanup function
      const cleanup = () => {
        setTimeout(() => {
          app.unmount()
          document.body.removeChild(container)
        }, 300) // Wait for exit animation
      }
    })
  }

  const confirmDeleteChapter = (chapterNumber, chapterTitle) => {
    const title = chapterTitle ? `"${chapterTitle}"` : `Chapter ${chapterNumber}`
    return showConfirmation({
      title: 'Delete Chapter',
      message: `Are you sure you want to delete ${title}? This will also delete all sections in this chapter.`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    })
  }

  const confirmDeleteSection = (sectionTitle) => {
    return showConfirmation({
      title: 'Delete Section',
      message: `Are you sure you want to delete ${sectionTitle}?`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    })
  }

  const confirmGeneral = (title, message, confirmText = 'Confirm', cancelText = 'Cancel') => {
    return showConfirmation({
      title,
      message,
      confirmText,
      cancelText
    })
  }

  return {
    confirmDeleteChapter,
    confirmDeleteSection,
    confirmGeneral,
    showConfirmation
  }
}
