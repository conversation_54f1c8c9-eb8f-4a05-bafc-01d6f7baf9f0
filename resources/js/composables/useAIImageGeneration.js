import { ref, reactive } from 'vue'
import { useEditorUtils } from './useEditorUtils.js'

// Global shared state for image generation (singleton pattern)
const globalImageGenerationState = {
  isGenerating: ref(false),
  error: ref(null),
  activeGenerations: reactive(new Map()) // Map<generationId, { controller, placeholderId }>
}

/**
 * AI Image Generation Composable
 * Handles image generation with proper error handling and loading states
 * Uses shared global state so all components can track the same generation status
 */
export function useAIImageGeneration() {
  const { insertContentOnNextLine, replaceImagePlaceholder, removeImagePlaceholder } = useEditorUtils()

  // Use the global shared state instead of creating new instances
  const isGenerating = globalImageGenerationState.isGenerating
  const error = globalImageGenerationState.error
  const activeGenerations = globalImageGenerationState.activeGenerations

  /**
   * Generate unique ID for tracking image generation
   */
  const generateId = () => {
    return 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * Create image placeholder using external SVG with unique identifier
   */
  const createImagePlaceholder = (placeholderId) => {
    // Return a unique placeholder URL that we can easily identify and replace
    return `/images/ai-image-placeholder.svg?id=${placeholderId}`
  }

  /**
   * Generate AI image
   */
  const generateImage = async (editor, data) => {
    // Handle both old string format and new object format for backward compatibility
    const prompt = typeof data === 'string' ? data : data.prompt;
    const model = typeof data === 'object' ? data.model : null;
    if (!editor) return

    const generationId = generateId()
    const placeholderId = 'placeholder_' + generationId
    const controller = new AbortController()

    // Track this generation
    activeGenerations.set(generationId, {
      controller,
      placeholderId
    })

    isGenerating.value = true
    error.value = null

    try {
      // Insert placeholder image on next line
      const placeholderSrc = createImagePlaceholder(placeholderId)
      insertContentOnNextLine(editor, `<img src="${placeholderSrc}" alt="Generating image...">`)

      // Make API call
      const requestBody = { text: prompt };
      if (model) {
        requestBody.model = model;
      }

      const response = await fetch('/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to generate image')
      }

      const data = await response.json()

      if (!data.url) {
        throw new Error('No image URL received from API')
      }

      // Replace placeholder with actual image using the new utility function
      const success = replaceImagePlaceholder(editor, placeholderSrc, data.url, 'Generated image')

      if (!success) {
        console.warn('Failed to replace image placeholder, trying fallback method')
        // Fallback: remove placeholder and insert new image
        removeImagePlaceholder(editor, placeholderSrc)
        insertContentOnNextLine(editor, `<img src="${data.url}" alt="Generated image">`)
      }

    } catch (err) {
      // Remove placeholder on error
      const placeholderSrc = createImagePlaceholder(placeholderId)
      removeImagePlaceholder(editor, placeholderSrc)

      if (err.name !== 'AbortError') {
        console.error('Image generation error:', err)
        error.value = err.message || 'Failed to generate image'

        // Show user-friendly error message
        if (err.message.includes('OpenAI API key')) {
          error.value = 'OpenAI API key not configured. Please add your OpenAI API key in your account settings.'
        } else if (err.message.includes('HTTP 429')) {
          error.value = 'Rate limit exceeded. Please try again in a moment.'
        } else if (err.message.includes('HTTP 401')) {
          error.value = 'Authentication failed. Please check your OpenAI API key.'
        } else {
          error.value = 'Failed to generate image. Please try again.'
        }
      }
    } finally {
      // Clean up tracking
      activeGenerations.delete(generationId)

      // Update generating state
      if (activeGenerations.size === 0) {
        isGenerating.value = false
      }
    }
  }



  /**
   * Cancel all active image generations
   */
  const cancelAllGenerations = () => {
    for (const [generationId, { controller, placeholderId }] of activeGenerations) {
      controller.abort()
      // Note: placeholder removal should be handled by the editor component
      // since it needs access to the editor instance
    }

    activeGenerations.clear()
    isGenerating.value = false
    error.value = null
  }

  /**
   * Cancel specific image generation
   */
  const cancelGeneration = (generationId) => {
    const generation = activeGenerations.get(generationId)
    if (generation) {
      generation.controller.abort()
      activeGenerations.delete(generationId)

      if (activeGenerations.size === 0) {
        isGenerating.value = false
      }
    }
  }

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    isGenerating,
    error,
    activeGenerations,
    generateImage,
    cancelAllGenerations,
    cancelGeneration,
    removeImagePlaceholder,
    clearError
  }
}
