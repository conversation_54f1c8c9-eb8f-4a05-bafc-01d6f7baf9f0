import { ref } from 'vue'
import { useEditorUtils } from './useEditorUtils.js'

/**
 * AI Text Generation Composable
 * Handles text generation with proper error handling and loading states
 */
export function useAITextGeneration() {
  const { 
    getSelectedText, 
    getContextAroundCursor, 
    replaceSelectedText, 
    insertContentOnNextLine,
    createLoadingPlaceholder,
    removePlaceholder,
    cleanHtmlContent,
    isHtmlContent
  } = useEditorUtils()

  const isGenerating = ref(false)
  const error = ref(null)
  const controller = ref(null)

  /**
   * Generate AI text based on selection or context
   */
  const generateText = async (editor, prompt) => {
    if (!editor || isGenerating.value) return

    isGenerating.value = true
    error.value = null
    controller.value = new AbortController()

    const selectedText = getSelectedText(editor)
    const hasSelection = selectedText.trim().length > 0

    let placeholderText = ''
    let insertPosition = null

    try {
      if (hasSelection) {
        // Replace selected text with loading placeholder
        placeholderText = createLoadingPlaceholder('🤖 Generating...')
        replaceSelectedText(editor, placeholderText)
      } else {
        // Insert loading placeholder on next line
        placeholderText = createLoadingPlaceholder('🤖 Generating...')
        insertContentOnNextLine(editor, placeholderText)
      }

      // Prepare context for API call
      let contextPrompt = prompt
      if (!hasSelection) {
        const context = getContextAroundCursor(editor)
        contextPrompt += `\n\nContext before cursor: ${context.before}\nContext after cursor: ${context.after}`
      }

      // Make API call (non-streaming)
      const response = await fetch('/generate-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
          prompt: contextPrompt,
          stream: false
        }),
        signal: controller.value.signal
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to generate content`)
      }

      const data = await response.json()
      
      if (!data.content) {
        throw new Error('No content received from API')
      }

      // Remove placeholder
      removePlaceholder(editor, placeholderText)

      // Process and insert generated content
      const content = data.content.trim()
      
      if (hasSelection) {
        // Replace the original selection area with generated content
        if (isHtmlContent(content)) {
          const cleanedContent = cleanHtmlContent(content)
          replaceSelectedText(editor, cleanedContent)
        } else {
          replaceSelectedText(editor, content)
        }
      } else {
        // Insert on next line
        if (isHtmlContent(content)) {
          const cleanedContent = cleanHtmlContent(content)
          insertContentOnNextLine(editor, cleanedContent)
        } else {
          insertContentOnNextLine(editor, content)
        }
      }

    } catch (err) {
      // Remove placeholder on error
      if (placeholderText) {
        removePlaceholder(editor, placeholderText)
      }

      if (err.name !== 'AbortError') {
        console.error('Text generation error:', err)
        error.value = err.message || 'Failed to generate text'
        
        // Show user-friendly error message
        if (err.message.includes('OpenAI API key')) {
          error.value = 'OpenAI API key not configured. Please add your OpenAI API key in your account settings.'
        } else if (err.message.includes('HTTP 429')) {
          error.value = 'Rate limit exceeded. Please try again in a moment.'
        } else if (err.message.includes('HTTP 401')) {
          error.value = 'Authentication failed. Please check your OpenAI API key.'
        } else {
          error.value = 'Failed to generate text. Please try again.'
        }
      }
    } finally {
      isGenerating.value = false
      controller.value = null
    }
  }

  /**
   * Stop text generation
   */
  const stopGeneration = () => {
    if (controller.value) {
      controller.value.abort()
    }
    isGenerating.value = false
    error.value = null
  }

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    isGenerating,
    error,
    generateText,
    stopGeneration,
    clearError
  }
}
