import { ref, reactive } from 'vue'
import { createApp } from 'vue'
import ToastNotification from '../components/UI/ToastNotification.vue'

// Global toast state
const toasts = ref([])
let toastId = 0

export function useToast() {
  const showToast = (options) => {
    const id = ++toastId
    
    // Default options
    const defaultOptions = {
      type: 'info',
      title: '',
      message: '',
      duration: 4000,
      autoDismiss: true
    }
    
    const toastOptions = { ...defaultOptions, ...options, id }
    
    // Create a container for this toast
    const container = document.createElement('div')
    document.body.appendChild(container)
    
    // Create Vue app instance for this toast
    const app = createApp(ToastNotification, {
      ...toastOptions,
      onClose: () => {
        // Remove from DOM
        app.unmount()
        document.body.removeChild(container)
        
        // Remove from toasts array
        const index = toasts.value.findIndex(t => t.id === id)
        if (index > -1) {
          toasts.value.splice(index, 1)
        }
      }
    })
    
    // Mount the toast
    app.mount(container)
    
    // Add to toasts array for tracking
    toasts.value.push({ id, ...toastOptions, app, container })
    
    return id
  }

  const success = (title, message = '', options = {}) => {
    return showToast({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  const error = (title, message = '', options = {}) => {
    return showToast({
      type: 'error',
      title,
      message,
      duration: 6000, // Errors stay longer
      ...options
    })
  }

  const warning = (title, message = '', options = {}) => {
    return showToast({
      type: 'warning',
      title,
      message,
      duration: 5000,
      ...options
    })
  }

  const info = (title, message = '', options = {}) => {
    return showToast({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  const dismiss = (id) => {
    const toast = toasts.value.find(t => t.id === id)
    if (toast) {
      toast.app.unmount()
      document.body.removeChild(toast.container)
      
      const index = toasts.value.findIndex(t => t.id === id)
      if (index > -1) {
        toasts.value.splice(index, 1)
      }
    }
  }

  const dismissAll = () => {
    toasts.value.forEach(toast => {
      toast.app.unmount()
      document.body.removeChild(toast.container)
    })
    toasts.value = []
  }

  return {
    showToast,
    success,
    error,
    warning,
    info,
    dismiss,
    dismissAll,
    toasts: toasts.value
  }
}
