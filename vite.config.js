import { defineConfig } from 'vite';
import laravel, { refreshPaths } from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
    plugins: [
        vue(),
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/js/edit-book.js',
                'resources/css/filament/admin/theme.css'
            ],
            refresh: [
                ...refreshPaths,
                'app/Http/Livewire/**',
                'resources/js/components/**',
            ],
        }),
    ],
    server: {
        host: '0.0.0.0',
        port: 5174,
        strictPort: true,
        cors: {
            origin: ['http://localhost:8005', 'http://ebookwriter.test'],
            credentials: true,
        },
        hmr: {
            host: 'localhost',
            port: 5174,
        },
    },
    resolve: {
        alias: {
            '@': '/resources/js',
        },
    },
});
