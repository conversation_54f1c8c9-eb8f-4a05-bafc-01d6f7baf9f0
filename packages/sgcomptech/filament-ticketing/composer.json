{"name": "sgcomptech/filament-ticketing", "description": "A Laravel Filament plugin to support issue tracking and ticketing system.", "keywords": ["sgcomptech", "laravel", "ticketing", "messaging", "filament", "filament-ticketing", "issue tracking"], "homepage": "https://github.com/sgcomptech/filament-ticketing", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.0", "filament/filament": "^2.16", "illuminate/contracts": "^9.0|^10.0", "laravel/framework": "^9.0|^10.0", "spatie/laravel-package-tools": "^1.14"}, "require-dev": {"filament/spatie-laravel-settings-plugin": "^2.16", "filament/spatie-laravel-translatable-plugin": "^2.16", "laravel/pint": "^1.0", "nunomaduro/collision": "^6.0|^7.0", "nunomaduro/larastan": "^2.0.1", "orchestra/testbench": "^7.0", "pestphp/pest": "^1.22", "pestphp/pest-plugin-laravel": "^1.1", "pestphp/pest-plugin-livewire": "^1.0", "phpunit/phpunit": "^9.5|^10.0"}, "autoload": {"psr-4": {"Sgcomptech\\FilamentTicketing\\": "src"}}, "autoload-dev": {"psr-4": {"Sgcomptech\\FilamentTicketing\\Tests\\": "tests"}}, "scripts": {"post-autoload-dump": ["@php vendor/bin/testbench package:discover --ansi"], "analyse": "vendor/bin/phpstan analyse", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/pest --coverage", "format": "vendor/bin/pint"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"providers": ["Sgcomptech\\FilamentTicketing\\FilamentTicketingServiceProvider"], "aliases": {"FilamentTicketing": "Sgcomptech\\FilamentTicketing\\Facades\\FilamentTicketing"}}}, "minimum-stability": "dev", "prefer-stable": true}