<?php

namespace App\Policies;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CampaignPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Campaign $campaign): bool
    {
        return $user->isRecordOwner($campaign->user_id) || $user->isAdmin();
    }

    public function create(User $user): bool
    {
//        return $user->remainCredit()>0;
        return true;
    }

    public function update(User $user, Campaign $campaign): bool
    {
        return ($user->isRecordOwner($campaign->user_id) && in_array($campaign->status->value, [CampaignStatusEnum::DONE->value, CampaignStatusEnum::DRAFT->value])) || $user->isAdmin();
    }

    public function delete(User $user, Campaign $campaign): bool
    {
        return $user->isRecordOwner($campaign->user_id) || $user->isAdmin();
    }

    public function restore(User $user, Campaign $campaign): bool
    {
        return $user->isRecordOwner($campaign->user_id) || $user->isAdmin();
    }

    public function forceDelete(User $user, Campaign $campaign): bool
    {
        return $user->isRecordOwner($campaign->user_id) || $user->isAdmin();
    }
}
