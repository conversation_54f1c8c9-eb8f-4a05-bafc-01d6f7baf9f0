<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Sgcomptech\FilamentTicketing\Interfaces\TicketPolicies;
use App\Models\Ticket;


class TicketPolicy implements TicketPolicies
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Ticket $ticket): bool
    {
        return $user->id == $ticket->user_id
            || $user->id == $ticket->assigned_to_id
            || $user->isAdmin()
            || $user->isSupport();
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, Ticket $ticket): bool
    {
        return $user->isAdmin() || $user->isSupport();
    }

    public function delete(User $user, Ticket $ticket): bool
    {
        return $user->id == $ticket->user_id || $user->isAdmin() || $user->isSupport();
    }

    public function manageAllTickets($user): bool
    {
        return $user->isAdmin() || $user->isSupport();
    }

    public function manageAssignedTickets($user): bool
    {
        return $user->isAdmin() || $user->isSupport();
    }

    public function assignTickets($user): bool
    {
        return $user->isAdmin() || $user->isSupport();
    }
}
