<?php

namespace App\Filament\Resources;

use App\Actions\HighestRequiredCreditCalculator;
use App\Actions\ReGenerateEbookAction;
use App\Actions\RunCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource\AddErrorForm;
use App\Filament\Resources\CampaignResource\Forms\AudienceStyleWizardStep;
use App\Filament\Resources\CampaignResource\Forms\BasicInfoWizardStep;
use App\Filament\Resources\CampaignResource\Forms\EbookFormatWizardStep;
use App\Filament\Resources\CampaignResource\Forms\ConfigurePrintBookForm;
use App\Filament\Resources\CampaignResource\Forms\ConfigureAudioBookForm;
use App\Filament\Resources\CampaignResource\Forms\ConfigureChapterWiseAudioForm;
use App\Filament\Pages\Addons;
use App\Filament\Resources\CampaignResource\Pages;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use App\Filament\Resources\CampaignResource\RelationManagers\DownloadsRelationManager;
use App\Filament\Resources\CampaignResource\ReviewFormConfig;
use App\Filament\Resources\CampaignResource\RelationManagers\ChaptersRelationManager;
use App\Filament\Resources\CampaignResource\RelationManagers\ReviewsRelationManager;
use App\Filament\Resources\CampaignResource\RelationManagers\SectionsRelationManager;
use App\Services\LegacyUserService;
use App\Forms\Components\LivewireAudioPreview;
use App\Jobs\GenerateCoverImage;
use App\Jobs\GenerateEbookJob;
use App\Jobs\MergeAudioChunksJob;
use App\Models\Campaign;
use App\Models\EbookFormat;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use RyanChandler\FilamentProgressColumn\ProgressColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Get;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;
use Filament\Forms\Components\KeyValue;

class CampaignResource extends Resource
{
    protected static ?string $model = Campaign::class;

    protected static ?string $navigationIcon = 'heroicon-o-queue-list';

    protected static ?int $navigationSort = 1;

    protected static ?string $slug = 'campaign';

    public static function form(Form $form): Form
    {
        $submitButtonLabel = "";

        return $form
            ->schema(function (Form $form) {
                $context = $form->getOperation();

                $wizard = Wizard::make([
                    BasicInfoWizardStep::make(),
                    AudienceStyleWizardStep::make(),
                    EbookFormatWizardStep::make(),
                ])->nextAction(function (Forms\Components\Actions\Action $action, $get, $context){
                    $pageLength = $get('page_length');

                    if (!$pageLength) return null;

                    $user = auth()->user();

                    // Check subscription status for non-legacy users
                    $legacyService = app(LegacyUserService::class);
                    if (!$legacyService->isLegacyUser($user) && !$user->hasValidSubscription() && $context == 'create') {
                        return $action->disabled(true);
                    }

                    $required = HighestRequiredCreditCalculator::getHighestRequiredCredits((int) $pageLength);
                    $remaining = $user->remainCredit();

                    if ($remaining < $required && $context == 'create') {
                        return $action->disabled(true);
                    }

                    return $action->disabled(false);
                });

                // Add submitAction only if not in 'view' context
                if ($context == 'create') {
                    $submitButtonLabel = "Save & Run Campaign";
                } elseif ($context == 'edit') {
                    $submitButtonLabel = "Save Campaign";
                }
                if ($context == 'create' || $context == 'edit') {
                    $wizard->submitAction(new HtmlString(Blade::render(<<<BLADE
                    <x-filament::button
                        type="submit"
                        size="sm"
                        form="create"
                    >
                        $submitButtonLabel
                    </x-filament::button>
                BLADE)));
                }

                return [
                    Section::make([
                        KeyValue::make('meta.errors')
                        ->label('Errors')
                        ->visible(function ($record){
                            return $record->getMeta("errors");
                        })
                    ])->visible(fn($context, $record) => ($context == 'view') && $record->getMeta('errors')),

                    $wizard,

                    Section::make([
                        Placeholder::make('status')
                            ->visibleOn('view')
                            ->content(fn(?Campaign $record): string => ucfirst($record?->status?->value) ?? '-'),

                        Placeholder::make('ai_model')
                                   ->visibleOn('view')
                                   ->content(fn(?Campaign $record): string => ucfirst($record?->ai_model) ?? '-'),

                        Placeholder::make('total_image')
                                   ->visibleOn('view')
                                   ->content(fn(?Campaign $record): string => $record?->getForm("image_count") ?? '-'),

                        Placeholder::make('required_words')
                            ->label("Required words")
                            ->visibleOn('view')
                            ->visible(function () {
                                return auth()->user()->isAdmin();
                            })
                            ->content(fn(?Campaign $record): string => $record->required_word_length ?? '-'),

                        Placeholder::make('total_words')
                            ->label("Total words")
                            ->visibleOn('view')
                            ->visible(function () {
                                return auth()->user()->isAdmin();
                            })
                            ->content(fn(?Campaign $record): string => $record->total_word_length ?? '-'),

                        Placeholder::make('created_at')
                            ->visibleOn('view')
                            ->label('Created Date')
                            ->content(fn(?Campaign $record): string => $record?->created_at?->diffForHumans() ?? '-'),
                    ])->columns(4)->visibleOn('view'),
                ];
            })->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable()->visible(auth()->user()->isAdmin() || auth()->user()->isSupport()),

                Tables\Columns\TextColumn::make('topic')
                    ->searchable()->wrap()
                    ->limit(30)
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(function (Campaign $record){
                        if ($record->status == CampaignStatusEnum::FAILED && $record->hasOpenAIErrors()) {
                            return "OpenAI Error";
                        }
                        return $record->status->getLabel();
                    })
                    // ->color(fn($state): string => $state->getColor())
                    ->sortable(),

                ProgressColumn::make('progress')
                    ->progress(function (Campaign $record) {
                        if ($record->status == CampaignStatusEnum::AUDIO_PROCESSING) {
                            // Check if this is chapter-wise audio generation
                            $chapterProgress = $record->getMeta('chapter_audio_progress');
                            if ($chapterProgress) {
                                return $chapterProgress['percentage'] ?? 0;
                            }

                            // Regular audio book generation
                            $totalChunks = $record->getMeta("total_chunk") ?? 1;
                            // Ensure we never divide by zero
                            if ($totalChunks <= 0) {
                                $totalChunks = 1;
                            }
                            return round((count($record->getMeta('chunks', [])) / $totalChunks) * 100);
                        }
                        $chapters = $record->chapters;
                        if ($record->status == CampaignStatusEnum::DONE) {
                            return 100;
                        }

                        if (
                            $record->status == CampaignStatusEnum::IMAGE_PROCESSING ||
                            $record->status == CampaignStatusEnum::IMAGE_PROCESSING_FAILED
                        ) {
                            return 90;
                        }

                        $totalChapters = $chapters->count();
                        $completedChapters = $chapters->where('status', CampaignStatusEnum::DONE)->count();

                        if ($totalChapters > 0) {
                            return round(($completedChapters / $totalChapters) * 100);
                        }

                        return 0;

                    })->width(220),

                Tables\Columns\TextColumn::make('user.name')
                    ->words(1, '')
                    ->searchable()
                    ->url(fn(Campaign $record): string => "/users/{$record->user_id}/edit")
                    ->sortable()
                    ->color('primary')
                    ->visible(auth()->user()->isAdmin() || auth()->user()->isSupport()),

                //                Tables\Columns\TextColumn::make("required_word_length")
//                    ->formatStateUsing(function ($record) {
//                        return $record->required_word_length . "-" . $record->total_word_length;
//                    })->label("Required & Total Words")
//                    ->visible(auth()->user()->isAdmin()),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->searchable()
                    ->sortable()
                    ->alignCenter()
                    ->formatStateUsing(fn(Campaign $record) => $record->created_at->diffForHumans()),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->searchable()
                    ->sortable()
                    ->alignCenter()
                    ->visible(auth()->user()?->isAdmin() || auth()->user()?->isSupport())
                    ->formatStateUsing(fn(Campaign $record) => $record->updated_at->diffForHumans()),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(CampaignStatusEnum::class)
                    ->label('Status')
                    ->placeholder('Select Status')
                    ->multiple(),
                // SelectFilter::make('form.image_generate_model')
                // ->options(ImageGenerateModelEnum::getLabels())
                // ->label('Model')
                // ->placeholder('Select Model')
                // ->multiple(),
            ])
            ->actions([
                //                Tables\Actions\Action::make('Download PDF')->label("Download PDF")
//                                     ->icon('heroicon-o-arrow-down-tray')
//                                     ->action(function ($record) {
//                                         $filePath = storage_path('app/' . $record->url);
//                                         if (!file_exists($filePath)) {
//                                             Notification::make()
//                                                         ->title('Ebook file not found.')->danger()->send();
//                                         }
//                                         return response()->download($filePath);
//                                     })
//                                     ->color('gray')->button()
//                                     ->visible(function ($record) {
//                                         $filePath = storage_path('app/' . $record->url);
//                                         return $record->url != null && file_exists($filePath);
//                                     }),

                Tables\Actions\Action::make('Re Generate eBook')->label("Re-generate Ebook")
                    ->icon('heroicon-o-document-text')
                    ->action(function ($record) {
                        return ReGenerateEbookAction::regenerateAction($record);
                    })
                    ->color('gray')->button()
                    ->visible(function ($record) {
                        return auth()->user()->isUser() && $record->status == CampaignStatusEnum::DONE &&
                            ($record->fileCount("pdf") < 1 || $record->fileCount("epub") < 1) && !($record->getMeta('chunks') && !$record->getMeta("audio"));
                    }),


                \Filament\Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\Action::make('Re-Generate eBook')->label("Re-generate Ebook")
                        ->icon('heroicon-o-document-text')
                        ->action(function ($record) {
                            return ReGenerateEbookAction::regenerateAction($record);
                        })
                        ->color('gray')
                        ->visible(function ($record) {
                            return $record->status == CampaignStatusEnum::DONE;
                        }),

                    Tables\Actions\Action::make('configure_review_form')
                        ->label('Configure Lead Collection Form')
                        ->icon('heroicon-o-star')
                        ->form(function (Campaign $record) {
                            if (! auth()->user()->getUser()->hasLeadCollection()){
                                // redirect to addons page
                                return [];
                            }
                            return ReviewFormConfig::getForm($record);
                        })
                        ->action(function (Campaign $record, array $data, $action) {
                            if (! auth()->user()->getUser()->hasLeadCollection()){
                                // redirect based on user type
                                Notification::make()->title('Upgrade Required')
                                    ->body('You need to upgrade your plan to access lead collection. Please visit our sales page to purchase a plan that includes lead collection.')
                                    ->danger()
                                    ->send();

                                $legacyService = new \App\Services\LegacyUserService();
                                if ($legacyService->isLegacyUser(auth()->user())) {
                                    return redirect()->away(Addons::getUrl(['addon' => 'flipbook-lead-collection']));
                                } else {
                                    return redirect()->away(\App\Filament\Pages\Pricing::getUrl());
                                }
                            }
                            // Check if the "Save as Default" checkbox is checked
                            $saveAsDefault = $data['save_as_default'] ?? false;

                            // Remove the checkbox value from the data before saving
                            unset($data['save_as_default']);

                            // Call the action method with the saveAsDefault flag
                            ReviewFormConfig::action($record, $data, $saveAsDefault);
                        })
                        ->color('success')
                        ->visible(function ($record) {
                            return $record->status == CampaignStatusEnum::DONE &&
                            isCampaignTypeEnabled( FeatureEnum::LEAD_COLLECTION->value);
                        }),

                    Tables\Actions\Action::make('share_flipbook')
                        ->label('Share Flipbook')
                        ->icon('heroicon-o-globe-americas')
                        ->form(function (Campaign $record) {
                            if (! auth()->user()->getUser()->hasLinkSharing()){
                                // redirect to addons page
                                return [];
                            }
                            $pdfDownload = $record->downloads()->where('type', 'pdf')->latest()->first();
                            if (!$pdfDownload) {
                                return [];
                            }
                            return PublicShareForm::getForm($pdfDownload);
                        })
                        ->action(function (Campaign $record, array $data) {
                            if (! auth()->user()->getUser()->hasLinkSharing()){
                                // redirect based on user type
                                Notification::make()->title('Upgrade Required')
                                    ->body('You need to upgrade your plan to access flipbook sharing. Please visit our sales page to purchase a plan that includes flipbook features.')
                                    ->danger()
                                    ->send();

                                $legacyService = new \App\Services\LegacyUserService();
                                if ($legacyService->isLegacyUser(auth()->user())) {
                                    return redirect()->away(Addons::getUrl(['addon' => 'flipbook-lead-collection']));
                                } else {
                                    return redirect()->away(\App\Filament\Pages\Pricing::getUrl());
                                }
                            }
                            $pdfDownload = $record->downloads()->where('type', 'pdf')->latest()->first();
                            if ($pdfDownload) {
                                PublicShareForm::action($pdfDownload, $data);
                            }
                        })
                        ->color('info')
                        ->visible(function ($record) {
                            return $record->status == CampaignStatusEnum::DONE &&
                                   $record->downloads()->where('type', 'pdf')->exists() &&
                                   isCampaignTypeEnabled(FeatureEnum::LINK_SHARING->value);
                        }),


                    Tables\Actions\Action::make('configure_print_book')
                        ->label('Configure Print Book')
                        ->icon('heroicon-o-book-open')
                        ->form(function (Campaign $record) {
                            return ConfigurePrintBookForm::getForm($record);
                        })
                        ->action(function (Campaign $record, array $data) {
                            ConfigurePrintBookForm::action($record, $data);
                        })
                        ->color('info')
                        ->visible(function ($record) {
                            return $record->status == CampaignStatusEnum::DONE  && isCampaignTypeEnabled( FeatureEnum::LULU_INTEGRATION->value);
                        })
                        ->disabled(fn() => ! auth()->user()->getUser()->hasLuluIntegration()),


//                    Tables\Actions\Action::make('Download PDF')->label("Download PDF")
//                        ->icon('heroicon-o-arrow-down-tray')
//                        ->action(function ($record) {
//                            if (!isS3FileExist($record->getPdf())) {
//                                Notification::make()
//                                    ->title('Ebook pdf file not found.')->danger()->send();
//                            }
//                            return Storage::disk('s3')->download($record->getPdf());
//                        })
//                        ->color('gray')
//                        ->visible(function ($record) {
//                            return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                        }),


//                    Tables\Actions\Action::make('Download Epub')->label("Download Epub")
//                        ->icon('heroicon-o-arrow-down-tray')
//                        ->action(function ($record) {
//                            if (!isS3FileExist($record->getEpub())) {
//                                Notification::make()
//                                    ->title('Ebook epub file not found.')->danger()->send();
//                            }
//                            return Storage::disk('s3')->download($record->getEpub());
//                        })
//                        ->color('gray')
//                        ->visible(function ($record) {
//                            return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                        }),

//                    Tables\Actions\Action::make('share_ebook')
//                                        ->label("Share eBook")
//                                        ->icon('heroicon-o-globe-americas')
//                                        ->form(function (Campaign $record) {
//                                            return PublicShareForm::getForm($record);
//                                        })
//                                        ->action(function (Campaign $record, array $data) {
//                                            PublicShareForm::action($record, $data);
//                                        })
//                                         ->color('gray')
//                                         ->visible(function ($record) {
//                                            return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                                         }),

                    // Tables\Actions\Action::make('Generate Audio Book')
                    //     ->label(fn($record) => ConfigureAudioBookForm::getLabel($record))
                    //     ->icon('heroicon-o-speaker-wave')
                    //     ->form(fn(Campaign $campaign) => ConfigureAudioBookForm::getForm($campaign))
                    //     ->action(fn(Campaign $campaign, array $data) => ConfigureAudioBookForm::action($campaign, $data))
                    //     ->color('gray')
                    //     ->visible(ConfigureAudioBookForm::getVisibility()),

                    Tables\Actions\Action::make('Generate Audio Book')
                        ->label(fn($record) => ConfigureChapterWiseAudioForm::getLabel($record))
                        ->icon('heroicon-o-musical-note')
                        ->form(fn(Campaign $campaign) => ConfigureChapterWiseAudioForm::getForm($campaign))
                        ->action(fn(Campaign $campaign, array $data) => ConfigureChapterWiseAudioForm::action($campaign, $data))
                        ->color('gray')
                        ->visible(ConfigureChapterWiseAudioForm::getVisibility()),

                    // Multiple Chapter Audio ZIP Downloads
                    ...self::getChapterAudioDownloadActions(),

                    Tables\Actions\Action::make('complete_audio_processing')
                        ->label('Complete Audio Processing')
                        ->icon('heroicon-o-play')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('Complete Audio Processing')
                        ->modalDescription('This will manually trigger the audio merging process. Use this if the campaign is stuck at 100% completion.')
                        ->modalSubmitActionLabel('Complete Processing')
                        ->action(function (Campaign $record) {
                            try {
                                // Clear any stuck merge flags and dispatch the merge job
                                $record->saveMeta('merging_in_progress', false);
                                $record->saveMeta('merging_started_at', null);
                                MergeAudioChunksJob::dispatch($record);

                                Notification::make()
                                    ->title('Audio merge job queued successfully')
                                    ->body('The audio processing will complete shortly.')
                                    ->success()
                                    ->send();

                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Failed to queue merge job')
                                    ->body('Error: ' . $e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->visible(function (Campaign $record) {
                            // Check if campaign is in audio processing status
                            if ($record->status !== CampaignStatusEnum::AUDIO_PROCESSING) {
                                return false;
                            }

                            // Check if progress is 100% or greater
                            $chunks = $record->getMeta('chunks', []);
                            $totalChunks = $record->getMeta('total_chunk', 1);
                            if ($totalChunks <= 0) {
                                $totalChunks = 1;
                            }
                            $progress = (count($chunks) / $totalChunks) * 100;

                            if ($progress < 100) {
                                return false;
                            }

                            // Check if last activity was more than 5 minutes ago
                            $lastActivity = $record->updated_at;

                            // Also check the most recent chunk creation time if available
                            if (!empty($chunks)) {
                                // Get the most recent chunk file and check its creation time
                                $latestChunk = collect($chunks)->sortByDesc('chunk_index')->first();
                                if ($latestChunk && isset($latestChunk['file_name'])) {
                                    $chunkPath = storage_path("app/public/audio/{$latestChunk['file_name']}");
                                    if (file_exists($chunkPath)) {
                                        $chunkTime = \Carbon\Carbon::createFromTimestamp(filemtime($chunkPath));
                                        if ($chunkTime->gt($lastActivity)) {
                                            $lastActivity = $chunkTime;
                                        }
                                    }
                                }
                            }

                            // Show button if last activity was more than 5 minutes ago
                            return $lastActivity->lt(now()->subMinutes(5));
                        }),


                    // Tables\Actions\Action::make('Download Audio Book')
                    //     ->label("Download Audio Book")
                    //     ->icon('heroicon-o-arrow-down-tray')
                    //     ->url(fn($record) => Storage::disk('s3')->temporaryUrl(
                    //         'audio/' . basename($record->getMeta("audio")),
                    //         now()->addMinutes(30) // Generates a signed URL valid for 30 minutes
                    //     )) // Assuming 'audio' meta contains the file URL
                    //     ->openUrlInNewTab()
                    //     ->color('success')
                    //     ->visible(fn($record) => $record->getMeta("audio")),

                    Tables\Actions\Action::make('Logs')
                        ->icon('heroicon-o-clipboard-document-list')
                        ->url(fn(Campaign $record): string => config('logging.channels.papertrail.query_log') . 'Campaign:' . $record->id . ':')
                        ->openUrlInNewTab()
                        ->color('gray')
                        ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\Action::make('Retry')
                        ->label("Re-try")
                        ->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                            $record->log("Campaign retry starting for campaign: {$record->id}");
                            $runCampaign->execute($record, true);
                            return Notification::make()->success()->title('Campaign retrying')->send();
                        })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                            return ($record->status != CampaignStatusEnum::DONE &&
                                    $record->status != CampaignStatusEnum::IN_PROGRESS &&
                                    $record->status != CampaignStatusEnum::DRAFT
                            );
                        }),

                    \Filament\Tables\Actions\ReplicateAction::make()->label('Clone')
                        ->beforeReplicaSaved(function (Campaign $replica, array $data) {
                            $replica->status = CampaignStatusEnum::DRAFT->value;
                            $replica->total_word_length = null;
                            $replica->required_word_length = null;
                            $replica->title = null;
                            $replica->url = null;
                            $replica->meta = null;
                            $replica->cover_image = null;
                            $replica->user_id = auth()->user()->getUser()->id;

                            $ebookFormat = EbookFormat::find($replica->ebook_format_id);
                            $newEbookFormat = EbookFormat::create($ebookFormat->toArray());
                            $replica->ebook_format_id = $newEbookFormat->id;
                        })->form([
                                Wizard::make([
                                    BasicInfoWizardStep::make(),
                                    AudienceStyleWizardStep::make(),
                                ]),
                            ]),

                    Tables\Actions\Action::make('Start')->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                        $runCampaign->execute($record, true);
                        return Notification::make()->success()->title('Campaign starting!')->send();
                    })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                        return $record->status == CampaignStatusEnum::DRAFT;
                    }),

                    Tables\Actions\Action::make('Add Error Message')
                        ->label('Add Error Message')
                        ->color('danger')
                        ->form(function () {
                            return AddErrorForm::getForm();
                        })
                        ->action(function (Campaign $record, array $data) {
                            return AddErrorForm::action($record, $data);
                        })
                        ->icon('heroicon-o-exclamation-triangle')
                        ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\ViewAction::make()
                        ->url(fn(Campaign $record): string => route('filament.app.resources.campaign.view', $record->id))
                        ->visible(fn(Campaign $record): bool => $record->user_id == auth()->user()->getUser()->id || auth()->user()->isAdmin() || auth()->user()->isSupport()),

                    Tables\Actions\EditAction::make()
                        ->url(fn(Campaign $record): string => route('filament.app.resources.campaign.edit', $record->id))
                        ->visible(fn(Campaign $record): bool => $record->user_id == auth()->user()->getUser()->id || auth()->user()->isAdmin() || auth()->user()->isSupport()),

                    Tables\Actions\DeleteAction::make()
                        ->visible(fn(Campaign $record): bool => $record->user_id == auth()->user()->getUser()->id || auth()->user()->isAdmin() || auth()->user()->isSupport()),
                ])->iconButton(),
            ])
            ->modifyQueryUsing(function ($query) {
                $user = auth()->user();
                if ($user->isAdmin() || $user->isSuperAdmin() || $user->isSupport()) {
                    return $query;
                }
                return $query->where('user_id', $user->getUser()->id);
            })
            ->defaultSort('campaigns.id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                                                   ->visible(fn(Campaign $record): bool => $record->user_id == auth()->user()->getUser()->id || auth()->user()->isAdmin() || auth()->user()->isSupport()),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ChaptersRelationManager::class,
            SectionsRelationManager::class,
            ReviewsRelationManager::class,
            DownloadsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaigns::route('/'),
            'create' => Pages\CreateCampaign::route('/create'),
            'view' => Pages\ViewCampaign::route('/{record}'),
            'edit' => Pages\EditCampaign::route('/{record}/edit'),
        ];
    }

    /**
     * Get chapter audio download actions based on available models
     */
    public static function getChapterAudioDownloadActions(): array
    {
        return [
            Tables\Actions\Action::make('download_chapter_audio')
                ->label('Download Chapter Audio')
                ->icon('heroicon-o-archive-box-arrow-down')
                ->url(function ($record) {
                    $downloadUrls = $record->getMeta('chapter_audio_download_urls', []);

                    // If only one model, return direct URL
                    if (count($downloadUrls) === 1) {
                        $urlInfo = array_values($downloadUrls)[0];
                        return $urlInfo['url'];
                    }

                    // Multiple models - return null to use action instead
                    return null;
                })
                ->openUrlInNewTab()
                ->form(function ($record) {
                    $downloadUrls = $record->getMeta('chapter_audio_download_urls', []);

                    // If only one model, no form needed (will use URL)
                    if (count($downloadUrls) <= 1) {
                        return [];
                    }

                    // Multiple models - show selection
                    $options = [];
                    foreach ($downloadUrls as $modelKey => $urlInfo) {
                        $modelName = $urlInfo['model'] ?: 'Default';
                        $options[$modelKey] = ucfirst($modelName) . ' Voice';
                    }

                    return [
                        Forms\Components\Select::make('selected_model')
                            ->label('Select Voice Model')
                            ->options($options)
                            ->required()
                            ->default(array_key_first($downloadUrls))
                            ->helperText('Choose which voice model to download.')
                    ];
                })
                ->action(function ($record, array $data) {
                    $downloadUrls = $record->getMeta('chapter_audio_download_urls', []);

                    if (empty($downloadUrls)) {
                        \Filament\Notifications\Notification::make()
                            ->title('No Audio Available')
                            ->body('No chapter audio files are available for download.')
                            ->warning()
                            ->send();
                        return;
                    }

                    // This action only runs for multiple models
                    $selectedModel = $data['selected_model'] ?? array_key_first($downloadUrls);
                    $urlInfo = $downloadUrls[$selectedModel];

                    return redirect()->to($urlInfo['url']);
                })
                ->modalHeading('Download Chapter Audio')
                ->modalSubmitActionLabel('Download')
                ->color('success')
                ->visible(function ($record) {
                    $downloadUrls = $record->getMeta('chapter_audio_download_urls', []);
                    return !empty($downloadUrls);
                }),
        ];
    }
}
