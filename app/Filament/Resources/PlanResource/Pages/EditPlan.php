<?php

namespace App\Filament\Resources\PlanResource\Pages;

use App\Filament\Resources\PlanResource;
use Filament\Pages\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditPlan extends EditRecord
{
    protected static string $resource = PlanResource::class;

    /**
     * @throws \Exception
     */
    protected function getActions(): array
    {
        return [
            DeleteAction::make()
                ->visible(fn() => auth()->user()->isSuperAdmin()),
        ];
    }
}
