<?php

namespace App\Filament\Resources\PlanResource\Pages;

use App\Filament\Resources\PlanResource;
use Filament\Pages\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListPlans extends ListRecords
{
    protected static string $resource = PlanResource::class;

    /**
     * @throws \Exception
     */
    protected function getActions(): array
    {
        return [
            CreateAction::make()
                ->visible(fn() => auth()->user()->isSuperAdmin()),
        ];
    }
}
