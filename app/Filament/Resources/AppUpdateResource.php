<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AppUpdateResource\Pages;
use App\Models\AppUpdate;
use App\Service\UpdateNotificationService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Filament\Support\Enums\FontWeight;

class AppUpdateResource extends Resource
{
    protected static ?string $model = AppUpdate::class;

    protected static ?string $navigationIcon = 'heroicon-o-megaphone';

    protected static ?string $navigationLabel = 'App Updates';

    protected static ?string $modelLabel = 'App Update';

    protected static ?string $pluralModelLabel = 'App Updates';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 111;
    
    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Update Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('version')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->placeholder('e.g., 2.1.0')
                                    ->helperText('Semantic version number'),

                                Forms\Components\Select::make('type')
                                    ->required()
                                    ->options([
                                        'major' => 'Major',
                                        'minor' => 'Minor',
                                        'patch' => 'Patch',
                                    ])
                                    ->default('minor')
                                    ->helperText('Update type affects email notifications'),
                            ]),

                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., New Dashboard Features')
                            ->columnSpanFull(),

                        Forms\Components\Select::make('category')
                            ->required()
                            ->options([
                                'features' => 'Features',
                                'fixes' => 'Bug Fixes',
                                'improvements' => 'Improvements',
                                'security' => 'Security',
                            ])
                            ->default('features'),
                    ]),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\Textarea::make('summary')
                            ->required()
                            ->rows(3)
                            ->maxLength(500)
                            ->helperText('Brief summary for email notifications (max 500 characters)')
                            ->columnSpanFull(),

                        Forms\Components\MarkdownEditor::make('content')
                            ->required()
                            ->helperText('Full changelog content (Markdown supported)')
                            ->fileAttachmentsDirectory('changelog/images/' . date('Y/m/d'))
                            ->fileAttachmentsDisk(app()->environment('local') ? 'public' : 's3')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Publishing & Notifications')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_published')
                                    ->label('Published')
                                    ->helperText('Make this update visible to users'),

                                Forms\Components\Toggle::make('send_email_notification')
                                    ->label('Send Email Notifications')
                                    ->helperText('Send emails to subscribed users when published'),
                            ]),

                        Forms\Components\DateTimePicker::make('released_at')
                            ->label('Release Date')
                            ->default(now())
                            ->required()
                            ->helperText('When this update was/will be released'),
                    ]),

                // Forms\Components\Section::make('Metadata')
                //     ->schema([
                //         Forms\Components\KeyValue::make('metadata')
                //             ->label('Additional Data')
                //             ->helperText('Optional metadata like links, images, etc.')
                //             ->columnSpanFull(),
                //     ])
                //     ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('version')
                    ->label('Version')
                    ->formatStateUsing(fn (string $state): string => "v{$state}")
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Title')
                    ->searchable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'major' => 'success',
                        'minor' => 'info',
                        'patch' => 'warning',
                        default => 'secondary'
                    }),

                Tables\Columns\TextColumn::make('category')
                    ->label('Category')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'features' => 'primary',
                        'fixes' => 'danger',
                        'improvements' => 'info',
                        'security' => 'warning',
                        default => 'secondary'
                    }),

                Tables\Columns\IconColumn::make('is_published')
                    ->label('Published')
                    ->boolean(),

                Tables\Columns\IconColumn::make('send_email_notification')
                    ->label('Email')
                    ->boolean()
                    ->tooltip('Send email notifications'),

                Tables\Columns\TextColumn::make('email_sent_at')
                    ->label('Email Sent')
                    ->date()
                    ->placeholder('Not sent')
                    ->sortable(),

                Tables\Columns\TextColumn::make('released_at')
                    ->label('Released')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'major' => 'Major',
                        'minor' => 'Minor',
                        'patch' => 'Patch',
                    ]),

                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'features' => 'Features',
                        'fixes' => 'Bug Fixes',
                        'improvements' => 'Improvements',
                        'security' => 'Security',
                    ]),

                Tables\Filters\TernaryFilter::make('is_published')
                    ->label('Published'),

                Tables\Filters\TernaryFilter::make('send_email_notification')
                    ->label('Email Notifications'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('preview_email')
                        ->label('Preview Email')
                        ->icon('heroicon-o-eye')
                        ->color('info')
                        ->url(fn (AppUpdate $record): string => route('admin.update.preview-email', $record))
                        ->openUrlInNewTab(),

                    Tables\Actions\Action::make('send_test_email')
                        ->label('Send Test Email')
                        ->icon('heroicon-o-envelope')
                        ->color('warning')
                        ->form([
                            \Filament\Forms\Components\TextInput::make('test_email')
                                ->label('Test Email Address')
                                ->email()
                                ->required()
                                ->default(fn(): string => auth()->user()->email)
                                ->helperText('Send a test email to this address'),
                        ])
                        ->action(function (AppUpdate $record, array $data) {
                            try {
                                // Validate the update has required fields
                                if (!$record->title || !$record->version) {
                                    throw new \InvalidArgumentException('Update must have title and version');
                                }

                                // Use the non-queued test mailable to avoid serialization issues
                                // Force synchronous sending to avoid queue serialization problems
                                \Illuminate\Support\Facades\Mail::to($data['test_email'])
                                    ->send(new \App\Mail\TestUpdateNotificationMail($record, $data['test_email'], 'Test User'));

                                Notification::make()
                                    ->title('Test email sent!')
                                    ->body("Test email sent to {$data['test_email']}")
                                    ->success()
                                    ->send();

                            } catch (\Exception $e) {
                                // Log the error for debugging
                                \Illuminate\Support\Facades\Log::error('Test email failed: ' . $e->getMessage(), [
                                    'update_id' => $record->id,
                                    'test_email' => $data['test_email'],
                                    'trace' => $e->getTraceAsString()
                                ]);

                                Notification::make()
                                    ->title('Failed to send test email')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }),

                    Tables\Actions\Action::make('send_notifications')
                        ->label('Send Notifications')
                        ->icon('heroicon-o-paper-airplane')
                        ->color('success')
                        ->visible(fn (AppUpdate $record): bool => $record->shouldSendEmailNotification())
                        ->requiresConfirmation()
                        ->modalHeading('Send Email Notifications')
                        ->modalDescription('This will send email notifications to all subscribed users. Are you sure?')
                        ->action(function (AppUpdate $record) {
                            app(UpdateNotificationService::class)->sendNotificationsForUpdate($record);

                            Notification::make()
                                ->title('Email notifications sent!')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ])
                    ->label('Actions')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm')
                    ->color('gray')
                    ->button()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('released_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAppUpdates::route('/'),
            'create' => Pages\CreateAppUpdate::route('/create'),
            'edit' => Pages\EditAppUpdate::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return auth()->user()?->isAdmin() ?? false;
    }
}
