<?php

namespace App\Filament\Resources\BackgroundImageResource\Pages;

use App\Filament\Resources\BackgroundImageResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBackgroundImage extends EditRecord
{
    protected static string $resource = BackgroundImageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    // protected function mutateFormDataBeforeSave(array $data): array
    // {
    //     $data['is_admin'] = auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
    //     return $data;
    // }
}
