<?php

namespace App\Filament\Resources\BackgroundImageResource\Pages;

use App\Filament\Resources\BackgroundImageResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateBackgroundImage extends CreateRecord
{
    protected static string $resource = BackgroundImageResource::class;
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $user = auth()->user()->getUser();
        $data['user_id'] = $user->id;
        $data['is_admin'] = $user->isAdmin() || $user->isSuperAdmin();
        return $data;
    }
}
