<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BackgroundImageResource\Pages;
use App\Models\BackgroundImage;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Hidden;


class BackgroundImageResource extends Resource
{
    protected static ?string $model = BackgroundImage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Templates';

    public static function shouldRegisterNavigation(): bool
    {
        return true;
        // return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
    }

    /**
     * Check if the current user is an admin or super admin
     */
    private static function isUserAdmin(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        $user = auth()->user();
        return $user->isAdmin() || $user->isSuperAdmin();
    }

    
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('src')
                    ->label("Image")
                    ->image()
                    ->rules(['image', 'mimes:jpg,jpeg,png,gif,webp,bmp', 'max:5120'])
                    ->disk('s3')
                    ->directory('templates')
                    ->visibility('private'),
                TextInput::make('opacity')
                    ->label('Opacity')
                    ->numeric()
                    ->minValue(0.1)
                    ->maxValue(1.0)
                    ->step(0.1)
                    ->default(1.0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                if (auth()->check()) {
                    $user = auth()->user()->getUser();

                    if (!$user->isAdmin() && !$user->isSuperAdmin()) {
                        // Regular users can only see their own images and admin images
                        $query->where(function ($q) use ($user) {
                            $q->where('user_id', $user->id);
                        });
                    }
                    // Admins can see all images
                }
            })
            ->columns([
                ImageColumn::make('src')
                    ->label('Image')
                    ->getStateUsing(function (BackgroundImage $record): ?string {
                        if ($record->src) {
                            try {
                                // Try to generate a temporary URL
                                $disk = Storage::disk('s3');
                                if (method_exists($disk, 'temporaryUrl')) {
                                    return $disk->temporaryUrl($record->src, now()->addMinutes(15));
                                } else {
                                    return $disk->url($record->src);
                                }
                            } catch (\Exception) {
                                // Fallback to regular URL if temporary URL fails
                                return Storage::disk('s3')->url($record->src);
                            }
                        }
                        return null;
                    })
                    ->width(150)
                    ->height(150),
                Tables\Columns\TextColumn::make('opacity')->label('Opacity')->width(100),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Owner')
                    ->sortable()
                    ->visible(fn() => self::isUserAdmin()),
                ToggleColumn::make('is_admin')->label('Admin Image')
                    ->visible(fn() => self::isUserAdmin())
                    ->width(100),
                ToggleColumn::make('visible')->width(100),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_admin')
                    ->label('Image Type')
                    ->options([
                        '1' => 'Admin Images',
                        '0' => 'User Images',
                    ])
                    ->visible(fn() => self::isUserAdmin()),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBackgroundImages::route('/'),
            'create' => Pages\CreateBackgroundImage::route('/create'),
            'edit' => Pages\EditBackgroundImage::route('/{record}/edit'),
        ];
    }
}
