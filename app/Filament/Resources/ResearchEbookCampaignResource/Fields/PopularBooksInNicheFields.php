<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\HtmlString;

class PopularBooksInNicheFields
{
    public static function make()
    {
        return Section::make('Identify popular books in a niche')->schema([
            Select::make('form.niche')
                  ->required()
                  ->label('Niche')->default("productivity")
                  ->options([
                      'artificial_intelligence' => 'Artificial Intelligence',
                      'business_entrepreneurship' => 'Business & Entrepreneurship',
                      'career_development' => 'Career Development',
                      'climate_sustainability' => 'Climate & Sustainability',
                      'cybersecurity' => 'Cybersecurity',
                      'digital_marketing' => 'Digital Marketing',
                      'e_commerce' => 'E-commerce',
                      'education_learning' => 'Education & Learning',
                      'fantasy_mythology' => 'Fantasy & Mythology',
                      'finance_investing' => 'Finance & Investing',
                      'fitness_nutrition' => 'Fitness & Nutrition',
                      'health_wellness' => 'Health & Wellness',
                      'leadership_management' => 'Leadership & Management',
                      'luxury_lifestyle' => 'Luxury & Lifestyle',
                      'mental_health_mindfulness' => 'Mental Health & Mindfulness',
                      'music_entertainment' => 'Music & Entertainment',
                      'parenting_family' => 'Parenting & Family',
                      'personal_development' => 'Personal Development',
                      'photography_videography' => 'Photography & Videography',
                      'productivity' => 'Productivity',
                      'psychology_behavior' => 'Psychology & Behavior',
                      'relationships_dating' => 'Relationships & Dating',
                      'science_innovation' => 'Science & Innovation',
                      'self_help_motivation' => 'Self-Help & Motivation',
                      'spirituality_religion' => 'Spirituality & Religion',
                      'sports_fitness' => 'Sports & Fitness',
                      'startup_tech' => 'Startup & Tech Industry',
                      'storytelling_creative_writing' => 'Storytelling & Creative Writing',
                      'sustainability_green_living' => 'Sustainability & Green Living',
                      'tech_gadgets_reviews' => 'Tech Gadgets & Reviews',
                      'travel_adventure' => 'Travel & Adventure',
                      'video_games_esports' => 'Video Games & Esports',
                      'web_development_programming' => 'Web Development & Programming',
                      'work_life_balance' => 'Work-Life Balance',
                      'writing_publishing' => 'Writing & Publishing',
                      'space_opera' => 'Space Opera',
                  ])->searchable()
                  ->placeholder('Select the niche for your campaign'),


            Select::make('form.publishing_year_range')
                  ->required()
                  ->label('Publishing Year Range')
                  ->default("12")
                  ->options([
                      '6'  => 'Last 6 Months',
                      '12' => 'Last 12 Months',
                      '24' => 'Past 2 Years',
                      '36' => 'Past 3 Years',
                      '48' => 'Past 4 Years',
                      '60' => 'Past 5 Years',
                  ])->native(false)
                  ->placeholder('Select the publishing year range'),

            Select::make('form.platform')
                  ->required()
                  ->label('Platform')
                  ->default("amazon")
                  ->options([
                      'amazon' => 'Amazon',
                      'goodreads' => 'Goodreads',
                      'new_york_times' => 'New York Times',
                  ])->native(false)
                  ->placeholder('Select the platform'),


            Select::make('form.target_market')
                  ->required()
                  ->label('Target Market')
                  ->default("global")
                  ->options([
                      'global' => 'Global',
                      'usa' => 'USA',
                      'uk' => 'UK',
                      'asia' => 'Asia',
                  ])->native(false)
                  ->placeholder('Select the target market'),


            Select::make('form.success_metrics')
                  ->required()
                  ->label('Success Metrics')->default("number_of_reviews")
                  ->options([
                      'number_of_reviews' => 'Number of Reviews',
                      'ratings' => 'Ratings',
                      'bestseller_status' => 'Bestseller Status',
                      'sales_volume' => 'Sales Volume',
                      'social_media_mentions' => 'Social Media Mentions',
                      'reader_engagement' => 'Reader Engagement',
                      'press_coverage' => 'Press Coverage',
                      'author_following_growth' => 'Author Following Growth',
                      'awards_nominations' => 'Awards & Nominations',
                      'return_on_investment' => 'Return on Investment (ROI)',
                  ])->native(false)
                  ->placeholder('Select the success metric'),

            Select::make('form.limit')
                  ->required()
                  ->label('Max results')->default('10')
                  ->options([
                      '5' => '5',
                      '10' => '10',
                      '15' => '15',
                      '20' => '20',
                      '25' => '25',
                      '30' => '30',
                  ])->native(false),

        ]);
    }
}
