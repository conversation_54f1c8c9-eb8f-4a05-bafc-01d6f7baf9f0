<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\HtmlString;

class BookTitleGenerationFields
{
    public static function make()
    {
        return Section::make('Book title generation')->schema([
            TextInput::make('form.topic')
                     ->label('Topic')
                     ->required()
                     ->placeholder('e.g., Personal Finance, Sci-Fi, Self-Help')
                     ->helperText(new HtmlString("Specify the topic of your campaign")),

            Select::make('form.audience')
                  ->required()
                  ->label('Targeted Audience')->default("general_public")
                  ->options([
                      'students' => 'Students',
                      'professionals' => 'Professionals',
                      'general_public' => 'General Public',
                      'beginners' => 'Beginners',
                      'experts' => 'Experts',
                      'teachers' => 'Teachers',
                      'entrepreneurs' => 'Entrepreneurs',
                      'hobbyists' => 'Hobbyists',
                  ])->native(false)
                  ->placeholder('Select the audience for your campaign'),

            Select::make('form.genre')
                  ->required()
                  ->label('Genre')->default("motivational")
                  ->options([
                      'adventure' => 'Adventure',
                      'biography' => 'Biography',
                      'business' => 'Business',
                      'comedy' => 'Comedy',
                      'crime' => 'Crime',
                      'drama' => 'Drama',
                      'fantasy' => 'Fantasy',
                      'historical_fiction' => 'Historical Fiction',
                      'horror' => 'Horror',
                      'literary_fiction' => 'Literary Fiction',
                      'memoir' => 'Memoir',
                      'motivational' => 'Motivational',
                      'mystery' => 'Mystery',
                      'personal_development' => 'Personal Development',
                      'philosophy' => 'Philosophy',
                      'poetry' => 'Poetry',
                      'psychology' => 'Psychology',
                      'romance' => 'Romance',
                      'science_fiction' => 'Science Fiction',
                      'self_help' => 'Self-Help',
                      'spirituality' => 'Spirituality',
                      'thriller' => 'Thriller',
                      'travel' => 'Travel',
                  ])->searchable()
                  ->placeholder('Select the genre for your campaign'),

            Select::make('form.tone')
                  ->required()
                  ->label('Tone')->default('professional')
                  ->options([
                      'formal' => 'Formal',
                      'casual' => 'Casual',
                      'technical' => 'Technical',
                      'conversational' => 'Conversational',
                      'professional' => 'Professional',
                      'humorous' => 'Humorous',
                      'inspirational' => 'Inspirational',
                      'neutral' => 'Neutral',
                  ])->native(false)
                  ->placeholder('Choose the tone of your campaign'),

            Select::make('form.key_aspect')
                  ->label('Key Aspect')->default('')
                  ->options([
                      'artificial_intelligence' => 'Artificial Intelligence',
                      'career_growth' => 'Career Growth',
                      'creativity_innovation' => 'Creativity & Innovation',
                      'cultural_studies' => 'Cultural Studies',
                      'decision_making' => 'Decision Making',
                      'emotional_intelligence' => 'Emotional Intelligence',
                      'entrepreneurship' => 'Entrepreneurship',
                      'ethics_morality' => 'Ethics & Morality',
                      'finance_investment' => 'Finance & Investment',
                      'health_wellness' => 'Health & Wellness',
                      'leadership_strategies' => 'Leadership Strategies',
                      'marketing_branding' => 'Marketing & Branding',
                      'mental_health' => 'Mental Health',
                      'negotiation_tactics' => 'Negotiation Tactics',
                      'personal_growth' => 'Personal Growth',
                      'productivity_hacks' => 'Productivity Hacks',
                      'psychological_thrillers' => 'Psychological Thrillers',
                      'relationship_advice' => 'Relationship Advice',
                      'self_discipline' => 'Self-Discipline',
                      'social_dynamics' => 'Social Dynamics',
                      'spiritual_growth' => 'Spiritual Growth',
                      'startup_strategies' => 'Startup Strategies',
                      'storytelling_techniques' => 'Storytelling Techniques',
                      'technology_trends' => 'Technology Trends',
                      'thriller_elements' => 'Thriller Elements',
                      'time_management' => 'Time Management',
                      'wealth_building' => 'Wealth Building',
                      'work_life_balance' => 'Work-Life Balance',
                  ])->searchable()
                  ->placeholder('Select the key aspect for your campaign'),

            Select::make('form.limit')
                  ->required()
                  ->label('Max results')->default('10')
                  ->options([
                      '5' => '5',
                      '10' => '10',
                      '15' => '15',
                      '20' => '20',
                      '25' => '25',
                      '30' => '30',
                  ])->native(false),


        ]);
    }
}
