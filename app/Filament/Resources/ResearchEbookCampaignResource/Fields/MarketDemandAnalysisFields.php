<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\HtmlString;

class MarketDemandAnalysisFields
{
    public static function make()
    {
        return Section::make('Market demand analysis')->schema([
            TextInput::make('form.topic')
                     ->label('Topic')
                     ->required()
                     ->placeholder('e.g., Personal Finance, Sci-Fi, Self-Help')
                     ->helperText(new HtmlString("Specify the topic of your campaign")),

            Select::make('form.audience')
                  ->required()
                  ->label('Targeted Audience')->default("general_public")
                  ->options([
                      'students' => 'Students',
                      'professionals' => 'Professionals',
                      'general_public' => 'General Public',
                      'beginners' => 'Beginners',
                      'experts' => 'Experts',
                      'teachers' => 'Teachers',
                      'entrepreneurs' => 'Entrepreneurs',
                      'hobbyists' => 'Hobbyists',
                  ])->native(false)
                  ->placeholder('Select the audience for your campaign'),

            Select::make('form.publishing_platform')
                  ->required()
                  ->label('Publishing Platform')
                  ->default("google_trend")
                  ->options([
                      'amazon' => 'Amazon',
                      'goodreads' => 'Goodreads',
                      'google_trend' => 'Google Trend',
                      'new_york_times' => 'New York Times',
                  ])->multiple()
                  ->placeholder('Select the platform'),

            Select::make('form.competitor_benchmarking')
                  ->required()
                  ->default("comparison_with_bestsellers")
                  ->label('Competitor Benchmarking')
                  ->options([
                      'comparison_with_bestsellers' => 'Comparison with Bestsellers',
                      'industry_average_performance' => 'Industry Average Performance',
                      'market_share_analysis' => 'Market Share Analysis',
                      'reader_preference_trends' => 'Reader Preference Trends',
                      'pricing_strategy_comparison' => 'Pricing Strategy Comparison',
                      'advertising_effectiveness' => 'Advertising Effectiveness',
                      'social_media_presence' => 'Social Media Presence',
                      'author_branding_strength' => 'Author Branding Strength',
                  ])
                  ->placeholder('Select competitor benchmarking criteria'),


            Select::make('form.trend_timeline')
                  ->required()->default("12")
                  ->label('Trend Timeline')
                  ->options([
                      '6'  => 'Last 6 Months',
                      '12' => 'Last 12 Months',
                      '24' => 'Past 2 Years',
                      '36' => 'Past 3 Years',
                      '48' => 'Past 4 Years',
                      '60' => 'Past 5 Years',
                  ])->native(false)
                  ->placeholder('Select the trend timeline'),


        ]);
    }
}
