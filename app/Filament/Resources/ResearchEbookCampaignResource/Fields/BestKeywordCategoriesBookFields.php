<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\HtmlString;

class BestKeywordCategoriesBookFields
{
    public static function make()
    {
        return Section::make('Best Keywords & Categories for Book Publishing')->schema([
            TextInput::make('form.topic')
                     ->label('Topic')
                     ->required()
                     ->placeholder('e.g., Personal Finance, Sci-Fi, Self-Help')
                     ->helperText(new HtmlString("Specify the topic of your campaign")),

            Select::make('form.audience')
                  ->required()
                  ->label('Targeted Audience')->default("general_public")
                  ->options([
                      'students' => 'Students',
                      'professionals' => 'Professionals',
                      'general_public' => 'General Public',
                      'beginners' => 'Beginners',
                      'experts' => 'Experts',
                      'teachers' => 'Teachers',
                      'entrepreneurs' => 'Entrepreneurs',
                      'hobbyists' => 'Hobbyists',
                  ])->native(false)
                  ->placeholder('Select the audience for your campaign'),

            Select::make('form.ebook_publishing_platform')
                  ->label('Publishing Platform')
                  ->required()
                  ->default("amazon_kdp")
                  ->options([
                      'amazon_kdp' => 'Amazon KDP',
                      'apple_books' => 'Apple Books',
                      'google_play_books' => 'Google Play Books',
                      'barnes_noble_press' => 'Barnes & Noble Press',
                      'kobo_writing_life' => 'Kobo Writing Life',
                      'smashwords' => 'Smashwords',
                      'draft2digital' => 'Draft2Digital',
                      'ingramspark' => 'IngramSpark',
                      'lulu' => 'Lulu',
                      'scribd' => 'Scribd',
                  ])->native(false)
                  ->placeholder('Select the publishing platform'),

            Select::make('form.limit')
                  ->required()
                  ->label('Max results')->default('10')
                  ->options([
                      '5' => '5',
                      '10' => '10',
                      '15' => '15',
                      '20' => '20',
                      '25' => '25',
                      '30' => '30',
                  ])->native(false),



        ]);
    }
}
