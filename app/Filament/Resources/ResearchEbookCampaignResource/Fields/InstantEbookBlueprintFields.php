<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Service\AIModel\Contracts\AIClientInterface;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\HtmlString;

class InstantEbookBlueprintFields
{
    public static function make()
    {
        return Section::make('Instant eBook Blueprint')->schema([
            TextInput::make('form.topic')
                ->label('I want to write a book about')
                ->placeholder('e.g., surviving an EMP attack, personal finance, healthy cooking')
                ->helperText(new HtmlString("Enter the topic you want to write about"))
                ->required(),
        ]);
    }
}
