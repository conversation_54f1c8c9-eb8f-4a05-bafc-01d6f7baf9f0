<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\HtmlString;

class AuthorBioFields
{
    public static function make()
    {
        return Section::make('Generate author bio')->schema([
            TextInput::make('form.author_name')
                     ->label('Author name')
                     ->required()
                     ->placeholder('e.g., <PERSON>'),

            TextInput::make('form.speciality')
                     ->label('Speciality')
                     ->required()
                     ->placeholder('e.g., Programmer, Software Developer, Novel writer, Blogger'),

            TextInput::make('form.notable_works')
                     ->label('Notable Works')
                     ->required()
                     ->placeholder('e.g., Published 10+ ebook in amazon, Awarded best seller award.'),

            TextInput::make('form.professional_background')
                     ->label('Professional Background')
                     ->required()
                     ->placeholder('e.g., Novel writer, Motivational speaker'),


            Select::make('form.tone')
                  ->required()
                  ->label('Tone')->default('professional')
                  ->options([
                      'formal' => 'Formal',
                      'casual' => 'Casual',
                      'technical' => 'Technical',
                      'conversational' => 'Conversational',
                      'professional' => 'Professional',
                      'humorous' => 'Humorous',
                      'inspirational' => 'Inspirational',
                      'neutral' => 'Neutral',
                  ])->native(false)
                  ->placeholder('Choose the tone of your campaign'),

            Select::make('form.limit')
                  ->required()
                  ->label('Max results')->default('3')
                  ->options([
                      '1' => '1',
                      '2' => '2',
                      '3' => '3',
                      '4' => '4',
                      '5' => '5',
                      '6' => '6',
                      '7' => '7',
                      '8' => '8',
                      '9' => '9',
                      '10' => '10',
                  ])->native(false),



        ]);
    }
}
