<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Service\AIModel\Contracts\AIClientInterface;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\HtmlString;

class RandomEbookGeneratorFields
{
    public static function make()
    {
        return Section::make('Random eBook Generator')->schema([
            TextInput::make('form.topic')
                ->label('Topic (Optional)')
                ->placeholder('e.g., Personal Finance, Sci-Fi, Self-Help')
                ->helperText(new HtmlString("Specify a topic to generate titles for that specific area, or leave blank for completely random titles")),

            Hidden::make('form.generated_titles')
                ->default([]),

            Actions::make([
                Action::make('generate_titles')
                    ->label(function (Get $get) {
                        $existingTitles = $get('form.generated_titles') ?? [];
                        return empty($existingTitles) ? 'Generate titles' : 'Generate 10 more titles';
                    })
                    ->icon('heroicon-o-sparkles')
                    ->color('primary')
                    ->action(function (Get $get, Set $set) {
                        if ($get("type") != ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR->value) {
                            return;
                        }

                        $aiClient = app(AIClientInterface::class, ['aiModel' => $get("ai_model")]);
                        $apiKey = auth()->user()->getAiApiKey($get("ai_model"));

                        // Build prompt with optional topic
                        $topic = $get('form.topic');
                        $topicInstruction = '';
                        if (!empty($topic)) {
                            $topicInstruction = " focused on the topic: {$topic}";
                        } else {
                            $topicInstruction = " for random niche and topic";
                        }

                        $promptData = ['topic_instruction' => $topicInstruction];
                        $prompt = promptBuilder(prompts()['random_ebook_title'], $promptData);
                        $prompt .= prompts()['ignore_terms'];

                        try {
                            $eBookTitles = $aiClient->callAIModel($prompt, $apiKey, $get("ai_model"));
                            $titleData = formatAIResponse($eBookTitles);

                            if (isset($titleData["book_titles"]) && count($titleData["book_titles"]) > 0) {
                                // Get existing titles
                                $existingTitles = $get('form.generated_titles') ?? [];

                                // Append new titles to existing ones
                                $newTitles = array_merge($existingTitles, $titleData["book_titles"]);

                                // Store all titles
                                $set('form.generated_titles', $newTitles);

                                // Clear the current selection to force refresh
                                $set('form.research_ebook_titles', null);

                                // Send success notification
                                \Filament\Notifications\Notification::make()
                                    ->title('Titles Generated Successfully')
                                    ->body(count($titleData["book_titles"]) . ' new titles have been generated.')
                                    ->success()
                                    ->send();
                            } else {
                                \Filament\Notifications\Notification::make()
                                    ->title('Generation Failed')
                                    ->body('Unable to generate titles. Please try again.')
                                    ->danger()
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Error')
                                ->body('An error occurred while generating titles: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(function (Get $get) {
                        return $get("type") == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR->value
                            && !empty($get("ai_model"));
                    }),
            ]),

            Placeholder::make('')
                ->content(new HtmlString('<div class="fi-fo-field-wrp-helper-text break-words text-sm text-gray-500">
Click "Generate titles" to Generate random eBook titles based on a specific topic or completely random. Select a title to proceed with the campaign.
</div>')),

            Radio::make('form.research_ebook_titles')
                ->label("Choose your title to generate random ebook")
                ->options(function (Get $get) {
                    $generatedTitles = $get('form.generated_titles') ?? [];
                    if (!empty($generatedTitles)) {
                        return collect($generatedTitles)->mapWithKeys(function ($title) {
                            return [$title => $title];
                        })->toArray();
                    }
                    return [];
                })
                ->visible(function (Get $get) {
                    $generatedTitles = $get('form.generated_titles') ?? [];
                    return !empty($generatedTitles);
                })
                ->reactive()
                ->required()

        ]);
    }
}