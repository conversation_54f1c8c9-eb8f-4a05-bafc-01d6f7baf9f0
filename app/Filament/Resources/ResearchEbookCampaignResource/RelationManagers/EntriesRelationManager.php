<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\RelationManagers;


use App\Actions\CopyBulkAction;
use App\Actions\ExportBulkAction;
use App\Actions\RecordFavoriteAction;

use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Models\ResearchEbookCampaign;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Livewire\Component as LivewireComponent;

class EntriesRelationManager extends RelationManager
{
    protected static string $relationship = 'entries';

    //protected static ?string $title = "Generated book titles";
    protected static bool $isLazy = false;


    public function form(Form $form): Form
    {
        return $form
            ->schema([
//                Forms\Components\TextInput::make('entry')->label("Book title")
//                    ->required()
//                    ->maxLength(255),
            ]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make($this->sectionTitle())->schema([
                    TextEntry::make('entry')->label("")->columnSpanFull(),
                ])->visible(function (){
                    return $this->ownerRecord->type != ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR
                        && $this->ownerRecord->type != ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT;
                }),

                Section::make($this->sectionTitle())->schema([
                    KeyValueEntry::make("entry_content")
                                 ->label("")
                                 ->keyLabel("Random details")
                                 ->valueLabel("Random details value")
                                 ->columnSpanFull(),
                ])->visible(function (){
                    return $this->ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR;
                }),
            ]);
    }

    public function sectionTitle(): string
    {
        if($this->ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::BEST_KEYWORD_CATEGORIES_FOR_EBOOK){
            return "Best keyword or category for ebook";
        }elseif ($this->ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::GENERATE_AUTHOR_BIO){
            return "Generated author bio";
        }elseif ($this->ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR){
            return "Generated random eBook details";
        }elseif ($this->ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT){
            return "Instant eBook Blueprint";
        }
        return "Book title";
    }

    public function table(Table $table): Table
    {
        $title = $this->sectionTitle();
        return $table
            ->recordTitle(function($record){
                return $this->sectionTitle();
            })
            ->emptyStateHeading(self::emptyStateHeading())
            ->columns([
                Tables\Columns\TextColumn::make('entry')
                                         ->label($title)->wrap()->limit(150),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('copy')
                                     ->label('Copy')
                                     ->action(function (?LivewireComponent $livewire) {

                                         $keywords = $livewire->ownerRecord->entries();
                                         $keywords = $this->getExportParamsBind($keywords);
                                         $livewire->js(view('scripts.copy-to-clipboard', ['text' => $keywords->get()->pluck('entry')->join(PHP_EOL)])->render());

                                         Notification::make()->success()->title('Copied')->send();
                                     }),
            ])
            ->actions([
                RecordFavoriteAction::make()->iconButton(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([

                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make('csv_export')->label('Export CSV'),
                    ExportBulkAction::make('txt')->label('Export TXT'),

                ])->label('Export'),

                CopyBulkAction::make('csv')->label('Copy Selected')->icon('heroicon-o-clipboard'),

                Tables\Actions\DeleteBulkAction::make(),


            ]);
    }


    protected function getExportParamsBind($query)
    {
        /** @var ResearchEbookCampaign $campaign */
        $campaign = $this->ownerRecord;

        $activeTab = request()->query('activeRelationManager');
        $wasExport = false;

        if (is_null($campaign) || is_null($activeTab)) {
            $campaign_url = parse_url(request()->server()['HTTP_REFERER'] ?? '') ?? '';
            $campaign_id = explode('/', $campaign_url['path'] ?? '')[2] ?? 0;

            if(is_null($campaign)){
                $campaign = ResearchEbookCampaign::find($campaign_id);
            }

            if (!empty($campaign_url['query'])) {
                parse_str($campaign_url['query'], $queryParams);
            } else {
                $queryParams = [];
            }

            if(is_null($activeTab)){
                $activeTab = $queryParams['activeRelationManager'] ?? null;
            }
            $wasExport = true;
        }

        if (!empty($campaign)) {
            $tab = $campaign->entries()
                            ->get()
                            ->pluck('id');

            if ($activeTab == null) {
                $activeTab = 0;
            }
        }

        if (!empty($campaign)) {
            return $query;
        }

        return $query;
    }

    // In your relation manager class
    public function isReadOnly(): bool
    {
        return false;
    }

    static function emptyStateHeading()
    {
        $campaign = ResearchEbookCampaign::find(request()->segment(2));
        $heading = "No data found for this campaign.";
        return $heading;
    }

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        if($ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::BEST_KEYWORD_CATEGORIES_FOR_EBOOK){
            return "Best keyword or category for ebook";
        }elseif ($ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::GENERATE_AUTHOR_BIO){
            return "Generated author bio";
        }elseif ($ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR){
            return "Generated random eBook details";
        }elseif ($ownerRecord->type == ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT){
            return "Instant eBook Blueprint";
        }
        return "Book title";
    }
}
