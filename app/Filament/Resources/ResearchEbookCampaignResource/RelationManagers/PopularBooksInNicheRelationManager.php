<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\RelationManagers;


use App\Actions\ExportBulkAction;
use App\Actions\RecordFavoriteAction;

use App\Models\ResearchEbookCampaign;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Livewire\Component as LivewireComponent;

class PopularBooksInNicheRelationManager extends RelationManager
{
    protected static string $relationship = 'popularBooksInNicheEntries';

    protected static ?string $title = "Popular books in niche";
    protected static bool $isLazy = false;


    public function form(Form $form): Form
    {
        return $form
            ->schema([
//                Forms\Components\TextInput::make('entry')->label("Book title")
//                    ->required()
//                    ->maxLength(255),
            ]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Book name')->schema([
                    TextEntry::make('book_name')->label("")->columnSpanFull(),
                ]),

                Section::make('Author name')->schema([
                    TextEntry::make('author')->label("")->columnSpanFull(),
                ]),

                Section::make('Book summary')->schema([

                    TextEntry::make('summary')->label("")->columnSpanFull(),

                ]),

                Section::make('Key takeaways')->schema([
                    TextEntry::make('key_takeways')->label("")
                             ->formatStateUsing(function ($record) {
                                 if (is_array($record->key_takeways) && !empty($record->key_takeways)) {
                                     $listItems = array_map(fn($index, $item) => "<li>" . ($index + 1) . ". {$item}</li>", array_keys($record->key_takeways), $record->key_takeways);
                                     return '<ol>' . implode('', $listItems) . '</ol>'; // Using <ol> for ordered list
                                 }
                                 return 'No key takeaways available.';
                             })->html(),
                ]),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('author')
            ->emptyStateHeading(self::emptyStateHeading())
            ->columns([
                Tables\Columns\TextColumn::make('book_name')
                                         ->label("Book name"),
                Tables\Columns\TextColumn::make('author')
                                         ->label("Author name")->wrap(),
            ])
            ->filters([
                //
            ])
            ->headerActions([

            ])
            ->actions([
                RecordFavoriteAction::make()->iconButton(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([

                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make('csv_export')->label('Export CSV'),
                    ExportBulkAction::make('txt')->label('Export TXT'),

                ])->label('Export'),
                Tables\Actions\DeleteBulkAction::make(),


            ]);
    }


    protected function getExportParamsBind($query)
    {
        /** @var ResearchEbookCampaign $campaign */
        $campaign = $this->ownerRecord;

        $activeTab = request()->query('activeRelationManager');
        $wasExport = false;

        if (is_null($campaign) || is_null($activeTab)) {
            $campaign_url = parse_url(request()->server()['HTTP_REFERER'] ?? '') ?? '';
            $campaign_id = explode('/', $campaign_url['path'] ?? '')[2] ?? 0;

            if(is_null($campaign)){
                $campaign = ResearchEbookCampaign::find($campaign_id);
            }

            if (!empty($campaign_url['query'])) {
                parse_str($campaign_url['query'], $queryParams);
            } else {
                $queryParams = [];
            }

            if(is_null($activeTab)){
                $activeTab = $queryParams['activeRelationManager'] ?? null;
            }
            $wasExport = true;
        }

        if (!empty($campaign)) {
            $tab = $campaign->entries()
                            ->get()
                            ->pluck('id');

            if ($activeTab == null) {
                $activeTab = 0;
            }
        }

        if (!empty($campaign)) {
            return $query;
        }

        return $query;
    }

    // In your relation manager class
    public function isReadOnly(): bool
    {
        return false;
    }

    static function emptyStateHeading()
    {
        $campaign = ResearchEbookCampaign::find(request()->segment(2));
        $heading = "No data found for this campaign.";
        return $heading;
    }
}
