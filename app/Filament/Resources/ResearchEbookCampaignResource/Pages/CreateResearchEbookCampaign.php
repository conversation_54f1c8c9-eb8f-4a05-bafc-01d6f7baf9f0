<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Pages;

use App\Enum\CampaignStatusEnum;
use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Filament\Resources\ResearchEbookCampaignResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateResearchEbookCampaign extends CreateRecord
{
    protected static string $resource = ResearchEbookCampaignResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $userId = auth()->user()->id;
        $data['user_id'] = $userId;
        $data['status'] = CampaignStatusEnum::PENDING->value;
        return $data;
    }

    public function mount(): void
    {
        if (!auth()->user()->getOpenAiApiKey()) {
            Notification::make()->title('You must set your OpenAI API key before you can create a campaign.')->danger()->send();
            redirect()->away('/settings');
            return;
        }

        parent::mount();
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction()
                ->label('Start Campaign')
                ->disabled(function () {
                    $data = $this->form->getRawState();

                    // Disable if type is RANDOM_EBOOK_GENERATOR and no title is selected
                    if (isset($data['type']) &&
                        $data['type'] === ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR->value &&
                        (!isset($data['form']['research_ebook_titles']) ||
                         empty($data['form']['research_ebook_titles']))) {
                        return true;
                    }

                    // For other campaign types, never disable
                    return false;
                }),
            $this->getCancelFormAction(),
        ];
    }
}