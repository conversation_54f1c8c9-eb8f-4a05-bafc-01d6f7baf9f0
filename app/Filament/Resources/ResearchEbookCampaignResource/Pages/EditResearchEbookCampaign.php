<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Pages;

use App\Filament\Resources\ResearchEbookCampaignResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditResearchEbookCampaign extends EditRecord
{
    protected static string $resource = ResearchEbookCampaignResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
