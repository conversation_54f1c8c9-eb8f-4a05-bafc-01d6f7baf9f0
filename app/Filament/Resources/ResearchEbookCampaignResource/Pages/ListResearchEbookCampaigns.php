<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Pages;

use App\Filament\Pages\Addons;
use App\Filament\Resources\CampaignResource;
use App\Filament\Resources\ResearchEbookCampaignResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListResearchEbookCampaigns extends ListRecords
{
    protected static string $resource = ResearchEbookCampaignResource::class;

    public function mount(): void
    {
        // Check if user has research tool permissions
        if (!auth()->user()->getUser()->hasResearchTool()) {
            Notification::make()
                ->title('Upgrade Required')
                ->body('You need to upgrade your plan to access research tools. Please visit our sales page to purchase a plan that includes research tools.')
                ->danger()
                ->persistent()
                ->send();

            // Redirect based on user type
            $legacyService = new \App\Services\LegacyUserService();
            if ($legacyService->isLegacyUser(auth()->user())) {
                $this->redirect(Addons::getUrl(['addon' => 'research-tools']), navigate: false);
            } else {
                $this->redirect(\App\Filament\Pages\Pricing::getUrl(), navigate: false);
            }
            return;
        }

        parent::mount();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
