<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Pages;

use App\Actions\RunResearchEbookCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Filament\Resources\ResearchEbookCampaignResource;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\AuthorBioFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\BestKeywordCategoriesBookFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\BookTitleGenerationFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\InstantEbookBlueprintFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\MarketDemandAnalysisFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\PopularBooksInNicheFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\RandomEbookGeneratorFields;
use App\Models\ResearchEbookCampaign;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\HtmlString;
use Filament\Infolists\Components;
use Filament\Infolists\Infolist;
use Filament\Forms\Form;

class ViewResearchEbookCampaign extends ViewRecord
{
    protected static string $resource = ResearchEbookCampaignResource::class;

    public function getRelationManagers(): array
    {
        /** @var ResearchEbookCampaign $campaign */
        $campaign = $this->record;

        if ($campaign &&
            $campaign->type == ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE
        ) {
            return [ResearchEbookCampaignResource\RelationManagers\PopularBooksInNicheRelationManager::class];
        }

        // Don't show relation manager for instant ebook blueprint and random ebook generator - we'll display data directly on the page
        if ($campaign && in_array($campaign->type, [
            ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT,
            ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR
        ])) {
            return [];
        }

        return [ResearchEbookCampaignResource\RelationManagers\EntriesRelationManager::class];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        /** @var ResearchEbookCampaign $campaign */
        $campaign = $this->record;

        $schema = [
            Components\Section::make(' Campaign information')
                ->schema([
                    Components\Split::make([
                        Components\Grid::make(2)
                            ->schema([
                                Components\Group::make([
                                    Components\TextEntry::make('name')->label('Campaign Name'),
                                    Components\TextEntry::make('ai_model'),
                                    Components\TextEntry::make('form.topic')->label('Topic')->visible(function (ResearchEbookCampaign $record) {
                                        return $record->type == ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT || $record->type == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR;
                                    }),
                                    Components\TextEntry::make('status')
                                        ->badge()
                                ]),
                                Components\Group::make([
                                    Components\TextEntry::make('type'),
                                    Components\TextEntry::make('created_at')->date(),
                                ]),
                            ]),
                    ])->from('lg'),
                ])
                ->collapsible(),
        ];

        // Add instant ebook blueprint data section if applicable
        if ($campaign && $campaign->type == ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT) {
            $entry = $campaign->entries()->first();
            if ($entry && $entry->entry_content) {
                $schema[] = Components\Section::make('Instant eBook Blueprint Results')
                    ->schema([
                        Components\TextEntry::make('blueprint_titles')
                            ->label('📚 Book Titles')
                            ->state(fn() => $entry->entry_content['titles'] ?? [])
                            ->listWithLineBreaks()
                            ->bulleted(),
                        Components\TextEntry::make('blueprint_context')
                            ->label('📖 eBook Context')
                            ->state(fn() => $entry->entry_content['ebook_context'] ?? '')
                            ->prose(),
                        Components\TextEntry::make('blueprint_pen_names')
                            ->label('✍️ Pen Names')
                            ->state(fn() => $entry->entry_content['pen_names'] ?? [])
                            ->listWithLineBreaks()
                            ->bulleted(),
                        Components\TextEntry::make('blueprint_description')
                            ->label('📝 eBook Description (for Kindle/Platforms)')
                            ->state(fn() => $entry->entry_content['ebook_description'] ?? '')
                            ->prose(),
                        Components\TextEntry::make('blueprint_keywords')
                            ->label('🔍 eBook Keywords')
                            ->state(fn() => $entry->entry_content['keywords'] ?? [])
                            ->listWithLineBreaks()
                            ->bulleted(),
                        Components\TextEntry::make('blueprint_categories')
                            ->label('📂 Categories for Platforms')
                            ->state(fn() => $entry->entry_content['categories'] ?? [])
                            ->listWithLineBreaks()
                            ->bulleted(),
                    ])
                    ->columns(1);
            }
        }

        // Add random ebook generator data section if applicable
        if ($campaign && $campaign->type == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR) {
            $entry = $campaign->entries()->first();
            if ($entry && $entry->entry_content) {
                // Handle both array and object formats
                $content = is_array($entry->entry_content) && isset($entry->entry_content[0])
                    ? $entry->entry_content[0]
                    : $entry->entry_content;

                $schema[] = Components\Section::make('Generated Random eBook Details')
                    ->schema([
                        Components\TextEntry::make('random_title')
                            ->label('📚 Book Title')
                            ->state(fn() => $content['title'] ?? ''),
                        Components\TextEntry::make('random_author')
                            ->label('✍️ Author Name')
                            ->state(fn() => $content['author_name'] ?? ''),
                        Components\TextEntry::make('random_context')
                            ->label('📖 eBook Context')
                            ->state(fn() => $content['context'] ?? '')
                            ->prose(),
                        Components\TextEntry::make('random_description')
                            ->label('📝 KDP Description')
                            ->state(fn() => $content['description'] ?? '')
                            ->prose(),
                        Components\TextEntry::make('random_keywords')
                            ->label('🔍 Keywords')
                            ->state(function() use ($content) {
                                $keywords = $content['keyword'] ?? '';
                                if (is_string($keywords)) {
                                    // Split comma-separated keywords into array
                                    return array_map('trim', explode(',', $keywords));
                                }
                                return $keywords;
                            })
                            ->listWithLineBreaks()
                            ->bulleted(),
                    ])
                    ->columns(1);
            }
        }

        return $infolist->schema($schema);
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Tables\Actions\BulkActionGroup::make([
                Action::make('Logs')
                      ->icon('heroicon-o-clipboard-document-list')
                      ->url(fn(ResearchEbookCampaign $record): string => config('logging.channels.papertrail.query_log') . 'ResearchEbookCampaign:' . $record->id . ':')
                      ->openUrlInNewTab()
                      ->color('gray')
                      ->visible(fn(): bool => auth()->user()->isAdmin()),

                Action::make('Retry')
                      ->label("Re-try")
                      ->action(function (ResearchEbookCampaign $record, RunResearchEbookCampaignAction $runCampaign) {
                          $record->log("Campaign retry starting for campaign: {$record->id}");
                          $runCampaign->execute($record, true);
                          return Notification::make()->success()->title('Campaign retrying')->send();
                      })->icon('heroicon-m-play')->visible(function (ResearchEbookCampaign $record) {
                        return $record->status == CampaignStatusEnum::FAILED;
                    }),

                Action::make('Start')->action(function (ResearchEbookCampaign $record, RunResearchEbookCampaignAction $runCampaign) {
                    $runCampaign->execute($record, true);
                    return Notification::make()->success()->title('Campaign starting!')->send();
                })->icon('heroicon-m-play')->visible(function (ResearchEbookCampaign $record) {
                    return $record->status == CampaignStatusEnum::DRAFT;
                }),

                Actions\ReplicateAction::make()->label('Clone')
                                                        ->beforeReplicaSaved(function (ResearchEbookCampaign $replica, array $data) {
                                                            $replica->status = CampaignStatusEnum::DRAFT->value;
                                                        })->form([
                        Section::make("Campaign goal")->schema([
                            Group::make()->schema([
                                TextInput::make('name')
                                         ->label('Campaign name')
                                         ->required()
                                         ->placeholder('Enter the name of your campaign')
                                         ->helperText(new HtmlString("Specify the name of your campaign"))
                                         ->disabledOn("edit"),
                                Radio::make('type')
                                     ->label(new HtmlString('Choose research eBook campaign type'))
                                     ->options(ResearchEbookCampaignIdeaTypeEnum::getAllowedTypes())
                                     ->hiddenOn('view')
                                     ->required()
                                     ->live()
                                     ->disabledOn("edit"),

                                Select::make('ai_model')
                                      ->label('AI Model')
                                      ->default('gpt-4o-mini')
                                      ->required()
                                      ->options(function (?ResearchEbookCampaign $record) {
                                          return ($record?->user ?: auth()->user()->getUser())?->getAvailableAIModels();
                                      })
                                      ->visibleOn(['create', 'view']),
                            ])
                        ]),

                        BookTitleGenerationFields::make()->visible(function(Get $get){
                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::BOOK_TITLE_GENERATION->value;
                        }),

                        PopularBooksInNicheFields::make()->visible(function(Get $get){
                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE->value;
                        }),

                        MarketDemandAnalysisFields::make()->visible(function(Get $get){
                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::MARKET_DEMAND_ANALYSIS->value;
                        }),

                        BestKeywordCategoriesBookFields::make()->visible(function(Get $get){
                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::BEST_KEYWORD_CATEGORIES_FOR_EBOOK->value;
                        }),

                        AuthorBioFields::make()->visible(function(Get $get){
                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::GENERATE_AUTHOR_BIO->value;
                        }),

                        RandomEbookGeneratorFields::make()->visible(function(Get $get){
                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR->value;
                        }),

                        InstantEbookBlueprintFields::make()->visible(function(Get $get){
                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT->value;
                        }),
                    ])



            ])->button()->label("Action")->color("warning"),

            EditAction::make()->icon("heroicon-m-pencil"),
        ];
    }
}
