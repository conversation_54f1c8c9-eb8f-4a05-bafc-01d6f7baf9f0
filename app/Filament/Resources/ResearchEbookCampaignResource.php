<?php

namespace App\Filament\Resources;

use App\Actions\RunResearchEbookCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\FeatureEnum;
use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\AuthorBioFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\BestKeywordCategoriesBookFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\BookTitleGenerationFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\InstantEbookBlueprintFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\MarketDemandAnalysisFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\PopularBooksInNicheFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Fields\RandomEbookGeneratorFields;
use App\Filament\Resources\ResearchEbookCampaignResource\Pages;
use App\Filament\Resources\ResearchEbookCampaignResource\RelationManagers;
use App\Models\Campaign;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

class ResearchEbookCampaignResource extends Resource
{
    protected static ?string $model = ResearchEbookCampaign::class;

    protected static ?string $navigationIcon = 'heroicon-o-light-bulb';
    protected static ?string $navigationLabel = 'Research';
    protected static ?int $navigationSort = 2;

    public static function canCreate(): bool
    {
        //return true;
        return auth()->user()->getUser()->hasResearchTool() && isCampaignTypeEnabled( FeatureEnum::RESEARCH_TOOLS->value);
    }
    public static function shouldRegisterNavigation(): bool
    {
        //return true;
        return isCampaignTypeEnabled( FeatureEnum::RESEARCH_TOOLS->value);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Section::make("Campaign goal")->schema([
                    Forms\Components\Group::make()->schema([
                        TextInput::make('name')
                                 ->label('Campaign name')
                                 ->required()
                                 ->placeholder('Enter the name of your campaign')
                                 ->helperText(new HtmlString("Specify the name of your campaign"))
                                 ->disabledOn("edit"),
                        Radio::make('type')
                             ->label(new HtmlString('Choose research eBook campaign type'))
                             ->options(ResearchEbookCampaignIdeaTypeEnum::getAllowedTypes())
                             ->hiddenOn('view')
                             ->required()
                             ->live()
                             ->disabledOn("edit"),

                        Select::make('ai_model')
                              ->label('AI Model')
                              ->default('gpt-4o-mini')
                              ->required()
                              ->options(function (?ResearchEbookCampaign $record) {
                                  return ($record?->user ?: auth()->user()->getUser())?->getAvailableAIModels();
                              })
                              ->visibleOn(['create', 'view']),
                    ])
                ]),

                BookTitleGenerationFields::make()->visible(function(Forms\Get $get){
                    return $get("type") == ResearchEbookCampaignIdeaTypeEnum::BOOK_TITLE_GENERATION->value;
                }),

                PopularBooksInNicheFields::make()->visible(function(Forms\Get $get){
                    return $get("type") == ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE->value;
                }),

                MarketDemandAnalysisFields::make()->visible(function(Forms\Get $get){
                    return $get("type") == ResearchEbookCampaignIdeaTypeEnum::MARKET_DEMAND_ANALYSIS->value;
                }),

                BestKeywordCategoriesBookFields::make()->visible(function(Forms\Get $get){
                    return $get("type") == ResearchEbookCampaignIdeaTypeEnum::BEST_KEYWORD_CATEGORIES_FOR_EBOOK->value;
                }),

                AuthorBioFields::make()->visible(function(Forms\Get $get){
                    return $get("type") == ResearchEbookCampaignIdeaTypeEnum::GENERATE_AUTHOR_BIO->value;
                }),

                RandomEbookGeneratorFields::make()->visible(function(Forms\Get $get, Forms\Set $set){
                    if (
                        $get("type") == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR->value
                    ){
                        return true;
                    }

                    return false;
                }),

                InstantEbookBlueprintFields::make()->visible(function(Forms\Get $get){
                    return $get("type") == ResearchEbookCampaignIdeaTypeEnum::INSTANT_EBOOK_BLUEPRINT->value;
                }),

                Forms\Components\Section::make()->schema([
                    Placeholder::make('status')
                               ->visibleOn('view')
                               ->content(fn($record): string => ucfirst($record?->status?->value) ?? '-'),

                    Placeholder::make('type')
                               ->visibleOn('view')
                               ->content(fn($record): string => ucwords(str_replace('_', ' ', $record?->type?->value)) ?? '-'),

                    Placeholder::make('created_at')
                               ->visibleOn('view')
                               ->label('Created Date')
                               ->content(fn($record): string => $record?->created_at?->diffForHumans() ?? '-'),
                ])->visibleOn('view')->columns(3),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make("name")->label("Campaign name"),
                TextColumn::make("status")
                          ->label("Status")
                            ->badge()
                            ->sortable()
                            ->color(fn(CampaignStatusEnum $state): string => $state->getColor()),

                TextColumn::make("type")
                          ->label("Type")
                          ->badge()
                          ->sortable()
                          ->color(fn(ResearchEbookCampaignIdeaTypeEnum $state): string => $state->getLabel()),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                \Filament\Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\Action::make('Logs')
                                         ->icon('heroicon-o-clipboard-document-list')
                                         ->url(fn(ResearchEbookCampaign $record): string => config('logging.channels.papertrail.query_log') . 'ResearchEbookCampaign:' . $record->id . ':')
                                         ->openUrlInNewTab()
                                         ->color('gray')
                                         ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\Action::make('Retry')
                                         ->label("Re-try")
                                         ->action(function (ResearchEbookCampaign $record, RunResearchEbookCampaignAction $runCampaign) {
                                             $record->log("Campaign retry starting for campaign: {$record->id}");
                                             $runCampaign->execute($record, true);
                                             return Notification::make()->success()->title('Campaign retrying')->send();
                                         })->icon('heroicon-m-play')
                                           ->visible(function (ResearchEbookCampaign $record) {
                                                return $record->status == CampaignStatusEnum::FAILED;
                                            }),

                    Tables\Actions\Action::make('Start')->action(function (ResearchEbookCampaign $record, RunResearchEbookCampaignAction $runCampaign) {
                        $runCampaign->execute($record, true);
                        return Notification::make()->success()->title('Campaign starting now!')->send();
                    })->icon('heroicon-m-play')->visible(function (ResearchEbookCampaign $record) {
                        return $record->status == CampaignStatusEnum::DRAFT;
                    }),

                    \Filament\Tables\Actions\ReplicateAction::make()->label('Clone')
                                                            ->beforeReplicaSaved(function (ResearchEbookCampaign $replica, array $data) {
                                                                $replica->status = CampaignStatusEnum::DRAFT->value;
                                                            })->form([
                                                                        Forms\Components\Section::make("Campaign goal")->schema([
                                                                            Forms\Components\Group::make()->schema([
                                                                                TextInput::make('name')
                                                                                         ->label('Campaign name')
                                                                                         ->required()
                                                                                         ->placeholder('Enter the name of your campaign')
                                                                                         ->helperText(new HtmlString("Specify the name of your campaign"))
                                                                                         ->disabledOn("edit"),
                                                                                Radio::make('type')
                                                                                     ->label(new HtmlString('Choose research eBook campaign type'))
                                                                                     ->options(ResearchEbookCampaignIdeaTypeEnum::getAllowedTypes())
                                                                                     ->hiddenOn('view')
                                                                                     ->required()
                                                                                     ->live()
                                                                                     ->disabledOn("edit"),

                                                                                Select::make('ai_model')
                                                                                      ->label('AI Model')
                                                                                      ->default('gpt-4o-mini')
                                                                                      ->required()
                                                                                      ->options(function (?ResearchEbookCampaign $record) {
                                                                                          return ($record?->user ?: auth()->user()->getUser())?->getAvailableAIModels();
                                                                                      })
                                                                                      ->visibleOn(['create', 'view']),
                                                                            ])
                                                                        ]),

                                                                        BookTitleGenerationFields::make()->visible(function(Forms\Get $get){
                                                                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::BOOK_TITLE_GENERATION->value;
                                                                        }),

                                                                        PopularBooksInNicheFields::make()->visible(function(Forms\Get $get){
                                                                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE->value;
                                                                        }),

                                                                        MarketDemandAnalysisFields::make()->visible(function(Forms\Get $get){
                                                                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::MARKET_DEMAND_ANALYSIS->value;
                                                                        }),

                                                                        BestKeywordCategoriesBookFields::make()->visible(function(Forms\Get $get){
                                                                            return $get("type") == ResearchEbookCampaignIdeaTypeEnum::BEST_KEYWORD_CATEGORIES_FOR_EBOOK->value;
                                                                        }),
                                                            ])
                ])->iconButton(),
            ])
            ->modifyQueryUsing(function ($query) {
                $user = auth()->user();
                if ($user->isAdmin() || $user->isSuperAdmin() || $user->isSupport()) {
                    return $query;
                }
                return $query->where('user_id', $user->getUser()->id);
            })
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort("created_at", "desc");
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListResearchEbookCampaigns::route('/'),
            'create' => Pages\CreateResearchEbookCampaign::route('/create'),
            'view' => Pages\ViewResearchEbookCampaign::route('/{record}/'),
            //'edit' => Pages\EditResearchEbookCampaign::route('/{record}/edit'),
        ];
    }
}
