<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WebhookResource\Pages;
use App\Models\Webhook;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class WebhookResource extends Resource
{
    protected static ?string $model = Webhook::class;

    protected static ?string $slug = 'webhooks';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?string $recordTitleAttribute = 'id';

    protected static ?int $navigationSort = 103;

    protected static ?string $navigationIcon = 'heroicon-o-queue-list';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSupport();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                TextInput::make('id')
                    ->disabled(),

                TextArea::make('payload')
                    ->label('Payload')
                    ->formatStateUsing(fn(?Webhook $record): string => collect($record?->payload)?->toJson(JSON_PRETTY_PRINT) ?? '-')->disabled(),

                TextInput::make('amount')
                    ->label('Amount')
                    ->disabled(),
                TextInput::make('status')
                    ->disabled(),
                Placeholder::make('created_at')
                    ->label('Created Date')
                    ->content(fn(?Webhook $record): string => $record?->created_at?->diffForHumans() ?? '-'),

                Placeholder::make('updated_at')
                    ->label('Last Modified Date')
                    ->content(fn(?Webhook $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('user.name')->searchable(),
                TextColumn::make('status')->badge()->searchable(),
                TextColumn::make('amount'),
                TextColumn::make('payload')->searchable()->visible(false),
                TextColumn::make('updated_at')->label('Updated At')->sortable()->since()->alignCenter(),
            ])->actions([
                ViewAction::make(),
            ])
            ->defaultSort('webhooks.id', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWebhooks::route('/'),
            // 'create' => Pages\CreateWebhook::route('/create'),
            // 'edit' => Pages\EditWebhook::route('/{record}/edit'),
            // 'view' => Pages\ViewWebhook::route('/{record}'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
