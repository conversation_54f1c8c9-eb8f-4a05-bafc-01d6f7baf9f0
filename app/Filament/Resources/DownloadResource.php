<?php

namespace App\Filament\Resources;

use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use App\Filament\Resources\DownloadResource\Pages;
use App\Filament\Resources\DownloadResource\RelationManagers;
use App\Models\Download;
use App\Traits\StorageHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Storage;

class DownloadResource extends Resource
{
    use StorageHelper;
    
    protected static ?string $model = Download::class;

    protected static ?string $navigationIcon = 'heroicon-o-folder-arrow-down';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('campaign.topic')
                    ->label("File version")
                    ->formatStateUsing(function ($state, $record) {
                        $state = strlen($state) > 50 ? substr($state, 0, 50) . '...' : $state;
                        return str_replace(" ", "_", $state) . "_v" . $record->file_version;
                    })->url(function($record){
                        return "/campaign/{$record->campaign_id}";
                    }),
                TextColumn::make('type')
                          ->label("Type")->badge()
                          ->formatStateUsing(fn ($state) => strtoupper($state)),
                TextColumn::make('user.name')
                          ->label("User name")
                          ->visible(function() {
                              return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
                          }),

                Tables\Columns\TextColumn::make('created_at')
                                         ->label('Created')
                                         ->searchable()
                                         ->sortable()
                                         ->alignCenter()
                                         ->formatStateUsing(fn(Download $record) => $record->created_at->diffForHumans()),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\BulkActionGroup::make([
                    // PDF Download Action
                    Tables\Actions\Action::make('download_pdf')
                        ->label('Download PDF')
                        ->icon('heroicon-o-document-arrow-down')
                        ->visible(fn (Download $record) => $record->type === 'pdf' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return Storage::disk('s3')->temporaryUrl($record->path, now()->addHours(2));
                        })
                        ->openUrlInNewTab(),
    
                    // EPUB Download Action
                    Tables\Actions\Action::make('download_epub')
                        ->label('Download EPUB')
                        ->icon('heroicon-o-book-open')
                        ->visible(fn (Download $record) => $record->type === 'epub' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return Storage::disk('s3')->temporaryUrl($record->path, now()->addHours(2));
                        })
                        ->openUrlInNewTab(),
    
                    // Audio Download Action
                    Tables\Actions\Action::make('download_audio')
                        ->label('Download Audio Book')
                        ->icon('heroicon-o-musical-note')
                        ->visible(fn (Download $record) => $record->type === 'mp3' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return Storage::disk('s3')->temporaryUrl($record->path, now()->addHours(2));
                        })
                        ->openUrlInNewTab(),
    
                    // Text Download Action
                    Tables\Actions\Action::make('download_text')
                        ->label('Download Text File')
                        ->icon('heroicon-o-document-text')
                        ->visible(fn (Download $record) => $record->type === 'txt' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return Storage::disk('s3')->temporaryUrl($record->path, now()->addHours(2));
                        })
                        ->openUrlInNewTab(),
    
                    // Share Flipbook Action
                    Tables\Actions\Action::make('share_flipbook')
                        ->label("Share flipbook")
                        ->icon('heroicon-o-globe-americas')
                        ->form(function (Download $record) {
                            return PublicShareForm::getForm($record);
                        })
                        ->action(function (Download $record, array $data) {
                            PublicShareForm::action($record, $data);
                        })
                        ->visible(function (Download $record) {
                            return $record->type == 'pdf' && isCampaignTypeEnabled(FeatureEnum::LINK_SHARING->value);
                        })
                        ->disabled(fn() => ! auth()->user()->getUser()->hasLinkSharing()),
                    Tables\Actions\Action::make('cover_image')
                            ->label("Download cover image")
                            ->icon('heroicon-o-arrow-down-tray')
                            ->action(function ($record) {
                                // Use the trait's methods through the bound $this
                                $url = self::getValidCoverImageUrl($record);

                                if (!$url) {
                                    Notification::make()
                                        ->title("Ebook cover file not found.")
                                        ->danger()
                                        ->send();
                                    return;
                                }

                                $path = self::parseStoragePathFromUrl($url);
                                return Storage::disk('s3')->download($path);
                            })
                            ->color('gray')
                            ->visible(function ($record) {
                                return self::getValidCoverImageUrl($record) !== null 
                                    && in_array($record->type, ['pdf', 'epub']);
                            }),
                ])->iconButton(),
            ])
            ->defaultSort('downloads.id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDownloads::route('/'),
            //'create' => Pages\CreateDownload::route('/create'),
            //'edit' => Pages\EditDownload::route('/{record}/edit'),
        ];
    }

    protected function getTemporaryUrl(Download $record): string
    {
        return Storage::disk('s3')->temporaryUrl($record->path,
            now()->addHours(2) // Increased from 30 minutes to 2 hours
        );
    }
}
