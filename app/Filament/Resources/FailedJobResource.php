<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FailedJobResource\Pages;
use App\Filament\Resources\FailedJobResource\RelationManagers;
use App\Models\FailedJob;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Artisan;

class FailedJobResource extends Resource
{
    protected static ?string $model = FailedJob::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Admin';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSupport();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make("connection"),
                Forms\Components\TextInput::make("queue"),
                Forms\Components\KeyValue::make("payload"),
                Forms\Components\KeyValue::make("payload.data"),
                Forms\Components\Textarea::make("exception"),
                Forms\Components\TextInput::make("failed_at"),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make("queue"),
                Tables\Columns\TextColumn::make("exception")->wrap()->limit(220),
                Tables\Columns\TextColumn::make("failed_at")->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                //Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Action::make('retry')
                      ->label('Retry')
                      ->action(function ($record) {
                          // Retry the failed job
                          Artisan::call('queue:retry', ['id' => $record->uuid]);
                          return Notification::make()->success()->title('Failed job retrying')->send();
                      })
                      ->icon('heroicon-o-arrow-path')
                      ->color('success'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFailedJobs::route('/'),
//            'create' => Pages\CreateFailedJob::route('/create'),
//            'edit' => Pages\EditFailedJob::route('/{record}/edit'),
        ];
    }
}
