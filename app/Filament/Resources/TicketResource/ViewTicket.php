<?php

namespace App\Filament\Resources\TicketResource;

use App\Filament\Resources\TicketResource;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewTicket extends ViewRecord
{
    protected static string $resource = TicketResource::class;

    protected $queryString = [
        'activeRelationManager' => ['except' => 0]
    ];

    public function getActions(): array
    {
        return [
            Action::make('Close Ticket')
                ->label('Close Ticket')
                ->color('success')
                ->visible(fn(): bool => $this->record->status < 3)
                ->icon('heroicon-o-check-circle')
                ->requiresConfirmation()
                ->action(function () {
                    $this->record->update(['status' => 4]);
                    Notification::make()->title('Ticket Closed')->success()->send();
                    $this->redirect('/tickets');
                }),

            Action::make('Reopen Ticket')
                ->label('Reopen Ticket')
                ->color('warning')
                ->visible(fn(): bool => $this->record->status > 2)
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->record->update(['status' => 1]);
                    Notification::make()->title('Ticket Reopened')->success()->send();
                    $this->redirect('/ticket/'.$this->record->id);
                }),
        ];
    }
}
