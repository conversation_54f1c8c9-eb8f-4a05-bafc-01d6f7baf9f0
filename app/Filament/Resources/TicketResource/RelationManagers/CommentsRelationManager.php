<?php

namespace App\Filament\Resources\TicketResource\RelationManagers;

use App\Filament\Fields\TicketCommentField;
use App\Models\User;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Actions\Action as ActionsAction;
use Filament\Notifications\Notification;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Livewire\Component as LivewireComponent;
use Sgcomptech\FilamentTicketing\Events\NewComment;
use Sgcomptech\FilamentTicketing\Events\NewResponse;
use App\Models\Comment;
use App\Models\Ticket;

class CommentsRelationManager extends RelationManager
{
    protected static string $relationship = 'comments';

    protected static ?string $recordTitleAttribute = 'user.name';

    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                TicketCommentField::make(),
            ]);
    }

    public function table(Table $table): Table
    {
        $user = auth()->user();

        return $table
            ->columns([
                Stack::make([
                    Split::make([
                        TextColumn::make('user.name')
                            ->translateLabel()
                            ->weight('bold')
                            ->color(fn(LivewireComponent $livewire, Model $record) => $livewire->ownerRecord->user_id == $record->user_id ? 'primary' : 'success')
                            ->grow(false),
                        TextColumn::make('created_at')
                            ->translateLabel()
                            ->dateTime()
                            ->since()
                            ->color('gray'),
                    ]),

                    TextColumn::make('content')->wrap()->formatStateUsing(function ($state) {

                        $state = preg_replace('/(?<=[\s>])(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[A-Z0-9+&@#\/%=~_|]/i', '<a href="$0" target="_blank">$0</a>', $state);

                        return new HtmlString(("<div class='prose dark:prose-invert aiwisemind-ticket-comment max-w-full'>$state</div>"));
                    })
                ]),
            ])
            ->headerActions([
                Action::make('addComment')
                    ->visible(function (LivewireComponent $livewire) {
                        $ticket = $livewire->ownerRecord;
                        return $ticket->status < 3;
                    })
                    ->label(__('Add Comment'))
                    ->closeModalByClickingAway(false)
                    ->form([
                        TicketCommentField::make(),

                        // FileUpload::make('attachment')
                        //     ->disk('public')
                        //     ->directory('form-attachments')
                        //     ->visibility('private'),
                    ])
                    ->action(function (array $data, LivewireComponent $livewire) use ($user): void {
                        $ticket = $livewire->ownerRecord;
                        abort_unless(
                            config('filament-ticketing.use_authorization') == false ||
                            $ticket->user_id == $user->id ||
                            $ticket->assigned_to_id == $user->id ||
                            $user->can('manageAllTickets', Ticket::class),
                            403
                        );
                        $comment = Comment::create([
                            'content' => $data['content'],
                            'user_id' => $user->id,
                            'ticket_id' => $livewire->ownerRecord->id,
                        ]);

                        if ($livewire->ownerRecord->user_id == $user->id) {
                            $agent = User::find($livewire->ownerRecord->assigned_to_id);
                            if($agent){
                                // self::sendNotification($agent, $livewire->ownerRecord->id, 'owner');
                            }
                            NewComment::dispatch($comment);
                        } else {
                            // self::sendNotification($livewire->ownerRecord->user, $livewire->ownerRecord->id, 'agent');
                            NewResponse::dispatch($comment);
                        }

                        $livewire->ownerRecord->update([
                            'last_comment_user_id' => $user->id,
                            'last_comment_user_name' => $user->name,
                        ]);
                    }),
            ])
            ->defaultSort('id', 'desc')
            ->actions([
                EditAction::make()
                    ->visible(fn(Comment $record) => (auth()->user()->canUpdateTicket() && $record->user_id == auth()->id()))
                    ->iconButton(),
                DeleteAction::make()->visible(fn(Comment $record) => auth()->user()?->isAdmin())->iconButton(),
            ]);
    }

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Comments');
    }

    public static function sendNotification(User $user, int $ownerRecord, string $type = 'owner'): void
    {
        $body = ($type == 'agent' ? 'A representative has responded to your support ticket.' : 'The ticket user has posted a response on this ticket.');
        Notification::make()
            ->title('New Response in Ticket!')
            ->body($body)
            ->icon('heroicon-o-ticket')
            ->iconColor('success')
            ->actions([
                ActionsAction::make('view')
                    ->color('success')
                    ->url(route('ticket.show', $ownerRecord))
                    ->button(),
            ])->sendToDatabase($user);
    }
}
