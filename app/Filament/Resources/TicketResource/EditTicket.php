<?php

namespace App\Filament\Resources\TicketResource;


use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Sgcomptech\FilamentTicketing\Events\NewAssignment;
use App\Models\Ticket;
use Illuminate\Contracts\Support\Htmlable;
use STS\FilamentImpersonate\Pages\Actions\Impersonate;

class EditTicket extends EditRecord
{
    public $prev_assigned_to_id;

    protected $queryString = [
        'activeRelationManager' => ['except' => 0]
    ];

    public static function getResource(): string
    {
        return config('filament-ticketing.ticket-resource');
    }

    protected function getActions(): array
    {
        return [
            Action::make('View User')
                ->label('View User')
                ->color('gray')
                ->visible(fn(): bool => auth()->user()->can('manageAllTickets', Ticket::class))
                ->icon('heroicon-o-user')
                ->url(fn(): string => "/users/{$this->record->user_id}/edit"),

            Action::make('Close Ticket')
                ->label('Close Ticket')
                ->color('success')
                ->visible(fn(): bool => $this->record->status < 3)
                ->icon('heroicon-o-check-circle')
                ->requiresConfirmation()
                ->action(function () {
                    $this->record->update(['status' => 4]);
                    Notification::make()->title('Ticket Closed')->success()->send();
                    $this->redirect('/tickets');
                }),

            Action::make('Reopen Ticket')
                ->label('Reopen Ticket')
                ->color('warning')
                ->visible(fn(): bool => $this->record->status > 2)
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->record->update(['status' => 1]);
                    Notification::make()->title('Ticket Reopened')->success()->send();
                    $this->redirect('/ticket/'.$this->record->id);
                }),

            DeleteAction::make()
        ];
    }

    public function getTitle(): string | Htmlable
    {
        $interacted = $this->record?->ticketable;

        return __('Ticket').($interacted ? ' ['.$interacted?->{$interacted?->model_name()}.']' : '');
    }

    protected function afterFill(): void
    {
        $this->prev_assigned_to_id = $this->record->assigned_to_id;
    }

    protected function afterSave(): void
    {
        if ($this->record->assigned_to_id != $this->prev_assigned_to_id) {
            NewAssignment::dispatch($this->record);
        }
    }
}
