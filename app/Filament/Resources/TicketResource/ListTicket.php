<?php

namespace App\Filament\Resources\TicketResource;

use App\Filament\Resources\TicketResource;
use Filament\Resources\Pages\ViewRecord;
use  Sgcomptech\FilamentTicketing\Filament\Resources\TicketResource\Pages\ListTicket as BaseListTicket;

class ListTicket extends BaseListTicket
{
    protected static string $resource = TicketResource::class;

    protected function getDefaultTableSortColumn(): ?string
    {
        return 'updated_at';
    }

    protected function getDefaultTableSortDirection(): ?string
    {
        return 'desc';
    }
}
