<?php

namespace App\Filament\Resources\ChapterResource\RelationManagers;

use App\Enum\CampaignStatusEnum;
use App\Models\Chapter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SectionsRelationManager extends RelationManager
{
    protected static string $relationship = 'sections';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('title')
                                           ->label("Section title")
                                           ->fileAttachmentsDisk('s3')
                                           ->fileAttachmentsDirectory("campaign/".date('Y/m/d'))
                                           ->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
                                           ->required()->columnSpan("full"),

                Forms\Components\RichEditor::make('intro')
                                           ->fileAttachmentsDisk('s3')
                                           ->fileAttachmentsDirectory("campaign/".date('Y/m/d'))
                                           ->label("Section intro")
                                            ->fileAttachmentsDirectory('campaign-image/'.date('Y-m-d'))
                                            ->fileAttachmentsDisk(app()->environment('local') ? 'public' : 's3')
                                           ->required()->columnSpan("full"),
                Forms\Components\RichEditor::make('body')
                                           ->fileAttachmentsDisk('s3')
                                           ->fileAttachmentsDirectory("campaign/".date('Y/m/d'))
                                           ->label("Section body")
                                           ->fileAttachmentsDirectory('campaign-image/'.date('Y-m-d'))
                                           ->fileAttachmentsDisk(app()->environment('local') ? 'public' : 's3')
                                           ->required()->columnSpan("full"),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('plain_title')
            ->columns([
                Tables\Columns\TextColumn::make('title')->html(),
            ])->defaultSort('order', "asc")
              ->reorderable('order')
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->mutateFormDataUsing(function($data){
                    $data['status'] = CampaignStatusEnum::DONE;
                    $data["order"] = $this->ownerRecord->sections->count() + 1;
                    $data['section_total_words'] = str_word_count(strip_tags($data['body']));
                    return $data;
                }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label("Quick Edit"),
                Tables\Actions\Action::make('chapter')->label("Open Editor")
                    ->icon('heroicon-o-pencil')
                    ->url(function ($record){
                        return "/standalone/edit-book/{$this->ownerRecord->campaign_id}?section={$record->id}";
                    })
                    ->openUrlInNewTab()
                    ->color('blue'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
