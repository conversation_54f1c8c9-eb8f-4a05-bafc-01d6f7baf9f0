<?php

namespace App\Filament\Resources\UserResource;

use App\Enum\CreditLogActionEnum;
use App\Models\Plan;
use LemonSqueezy\Laravel\Subscription;
use App\Models\User;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;

class CreditRefundForm
{
    static function getForm(): array
    {
        $fields = [
            TextInput::make('number_of_credit')
                ->default(0)
                ->numeric()
                ->required(),
            Textarea::make('description'),
        ];

        return $fields;
    }

    /**
     * @param  User
     * @param  array  $data
     */
    static function action(User $record, array $data)
    {
        $record->logCreditActivity(
            user: $record,
            action: CreditLogActionEnum::REFUND,
            credit: $data['number_of_credit'],
            description:$data['description']??""
        );

        $record->credits += $data['number_of_credit'];
        $record->save();

        Notification::make()
            ->title('Credit is refunded.')
            ->success()
            ->send();

        redirect()->away(url()->previous());
        return;
    }
}
