<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Models\Campaign;
// use App\Models\PaaEntry;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Illuminate\Database\Eloquent\Model;

class UserOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    public ?Model $record = null;

    public static function canView(): bool
    {
        return 1;
    }

    protected function getColumns(): int
    {
        return 3;
    }

    protected function getCards(): array
    {
        // $contentChart = Trend::query(PaaEntry::query()->whereHas('campaign', fn($q) => $q->where('user_id', $this->record->id)))
        //     ->between(start: now()->subMonth(), end: now())
        //     ->perDay()
        //     ->count();

        $campaignChart = Trend::query(Campaign::query()->where('user_id', $this->record->id))
            ->between(start: now()->subMonth(), end: now())
            ->perDay()
            ->count();

        $cards = [
            Stat::make('Campaigns', Campaign::query()->where('user_id', $this->record->id)->count())
                ->chart($campaignChart->map(fn(TrendValue $value) => $value->aggregate)->toArray()),

            // Stat::make('Contents', PaaEntry::query()->whereHas('campaign', fn($q) => $q->where('user_id', $this->record->id))->count())
            //     ->chart($contentChart->map(fn(TrendValue $value) => $value->aggregate)->toArray()),

        ];
        if (auth()->user()->isAdmin()) {
            $cards[] = Stat::make('Billing Information', function () {
                return $this->record->credits_used.'/'.$this->record->credits;
            });
        }
        return $cards;
    }
}
