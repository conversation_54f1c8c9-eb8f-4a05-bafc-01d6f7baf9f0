<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Enum\UserRoleEnum;
use App\Filament\Resources\UserResource;
use App\Filament\Resources\UserResource\CreditRefundForm;
use App\Filament\Resources\UserResource\Widgets\UserOverview;
use App\Models\User;
use App\Service\Logger;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use STS\FilamentImpersonate\Pages\Actions\Impersonate;

class EditUser extends EditRecord
{
    protected $queryString = [
        'activeRelationManager' => ['except' => 0]
    ];

    protected static string $resource = UserResource::class;

    /**
     * @throws \Exception
     */
    protected function getActions(): array
    {
        return [
            ActionGroup::make([
                \Filament\Actions\Action::make('Logs')
                    ->label('Logs')
                    ->url(fn(User $record): string => config('logging.channels.papertrail.query_log')."User:".$record->id)
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-clipboard-document-list')
                    ->visible(fn(): bool => auth()->user()->isAdmin()),

                DeleteAction::make()
                    ->after(function () {
                        (new Logger(static::class, $this->record))->log('User has been deleted by '.auth()->user()->name);
                    })->visible(fn(): bool => auth()->user()->isAdmin()),

                \Filament\Actions\Action::make('User Activity')
                    ->label('User Activity')
                    ->icon('heroicon-o-clipboard-document-list')
                    ->color('gray')
                    ->url(fn(): string => config('logging.channels.papertrail.query_log').'User:'.$this->record->id.': User activity')
                    ->openUrlInNewTab()
                    ->visible(fn(): bool => auth()->user()->isAdmin()),

                \Filament\Actions\Action::make('Credit Refund')
                    ->label('Credit Refund')
                    ->color('success')
                    ->form(function () {
                        return CreditRefundForm::getForm();
                    })
                    ->action(function (User $record, array $data) {
                        return CreditRefundForm::action($record, $data);
                    })
                    ->icon('heroicon-o-receipt-refund')
                    ->visible(fn(): bool => auth()->user()->isAdmin()),

                // Action::make('billing_dashboard')
                //     ->label('Billing dashboard')
                //     ->url(fn (User $record): string => route('user.billing.dashboard', ['user' => $record]))
                //     ->openUrlInNewTab()
                //     ->icon('heroicon-o-briefcase')
                //     ->visible(fn(): bool => auth()->user()->isAdmin() || auth()->user()->isSupport()),

                    Impersonate::make()->record($this->getRecord())->color('warning'),
            ])
            ->button()
            ->label('Actions')
        ];
    }

    function getHeaderWidgets(): array
    {
        return [
            UserOverview::class,
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {


        $previousName = $record->name;
        $previousEmail = $record->email;

        $record->update($data);

        if ($previousName !== $record->name) {
            (new Logger(static::class, $record))->log('Your name is updated from '.$previousName.' to '.$record->name);
        }

        if ($previousEmail !== $record->email) {
            (new Logger(static::class, $record))->log('Your email is updated from '.$previousEmail.' to '.$record->email);
        }

        return $record;
    }

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }

}
