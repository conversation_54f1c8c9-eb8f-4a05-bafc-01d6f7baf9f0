<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\Plan;
use App\Models\User;
use App\Service\Logger;
use App\Services\SubscriptionPlan;
use Filament\Pages\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // $data['user_id'] = auth()->id();

        return $data;
    }

    protected function handleRecordCreation(array $data): User
    {
        $planId = $data['plan'] ?? null;

        unset($data['plan']);

        /** @var User $data */
        $user = static::getModel()::create($data);

        if ($user->wasRecentlyCreated) {
            (new Logger(static::class, $user))->log($user->name.' is created by '.auth()->user()->name);
        }

        return $user;
    }

    protected function getRedirectUrl(): string
    {
        // $user = User::where('email', $this->data['email'])->first();
        // $plan = Plan::where('id', $this->data['plan'])->first();

        // if ($user && $plan) {
        //     return route('subscription.buy-plan',[
        //         'id' => $this->data['plan'],
        //         'user_id' => $user->id
        //     ]);
        // }

        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
