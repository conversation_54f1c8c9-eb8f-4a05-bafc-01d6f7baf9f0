<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Filament\Resources\CreditLogResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class CreditLogRelationManager extends RelationManager
{
    protected static string $relationship = 'creditlogs';

    public function form(Form $form): Form
    {
        return CreditLogResource::form($form);
    }

    public function table(Table $table): Table
    {
        return CreditLogResource::table($table);
    }
}
