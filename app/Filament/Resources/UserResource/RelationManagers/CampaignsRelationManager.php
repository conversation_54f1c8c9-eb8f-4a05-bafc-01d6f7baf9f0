<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Filament\Resources\CampaignResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Infolist;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CampaignsRelationManager extends RelationManager
{
    protected static string $relationship = 'Campaigns';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Form $form): Form
    {
        return CampaignResource::form($form);
    }

    public function table(Table $table): Table
    {
        return CampaignResource::table($table);
    }
    
    public function infolist(Infolist $infolist): Infolist
    {
        return CampaignResource::infolist($infolist);
    }

    protected function getDefaultTableSortColumn(): ?string
    {
        return 'campaigns.created_at';
    }

    protected function getDefaultTableSortDirection(): ?string
    {
        return 'desc';
    }
}
