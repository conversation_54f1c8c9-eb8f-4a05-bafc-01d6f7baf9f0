<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoadmapItemResource\Pages;
use App\Models\RoadmapItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\KeyValue;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;

class RoadmapItemResource extends Resource
{
    protected static ?string $model = RoadmapItem::class;

    protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static ?string $navigationLabel = 'Roadmap Items';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 113;
    
    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Textarea::make('summary')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),

                        MarkdownEditor::make('content')
                            ->label('Detailed Description')
                            ->columnSpanFull()
                            ->toolbarButtons([
                                'attachFiles',
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'heading',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'table',
                                'undo',
                            ]),
                    ]),

                Forms\Components\Section::make('Status & Priority')
                    ->schema([
                        Select::make('status')
                            ->required()
                            ->options([
                                'planned' => 'Planned',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('planned')
                            ->reactive(),

                        Select::make('priority')
                            ->required()
                            ->options([
                                'low' => 'Low',
                                'medium' => 'Medium',
                                'high' => 'High',
                                'critical' => 'Critical',
                            ])
                            ->default('medium'),

                        Select::make('category')
                            ->required()
                            ->options([
                                'features' => 'New Features',
                                'improvements' => 'Improvements',
                                'fixes' => 'Bug Fixes',
                                'infrastructure' => 'Infrastructure',
                            ])
                            ->default('features'),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Timeline')
                    ->schema([
                        DatePicker::make('estimated_date')
                            ->label('Estimated Completion Date')
                            ->displayFormat('M j, Y'),

                        DatePicker::make('completed_date')
                            ->label('Actual Completion Date')
                            ->displayFormat('M j, Y')
                            ->visible(fn (Forms\Get $get) => $get('status') === 'completed'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Toggle::make('is_published')
                            ->label('Published')
                            ->helperText('Only published items will be visible on the roadmap page')
                            ->default(false),
                    ]),

                Forms\Components\Section::make('Additional Data')
                    ->schema([
                        KeyValue::make('metadata')
                            ->label('Metadata')
                            ->helperText('Additional data like progress percentage, links, etc.')
                            ->keyLabel('Key')
                            ->valueLabel('Value')
                            ->addActionLabel('Add metadata'),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                TextColumn::make('status')
                    ->colors([
                        'secondary' => 'planned',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'danger' => 'cancelled',
                    ])
                    ->sortable()
                    ->badge()
                    ->icons([
                        'heroicon-o-clock' => 'planned',
                        'heroicon-o-arrow-path' => 'in_progress',
                        'heroicon-o-check-circle' => 'completed',
                        'heroicon-o-x-circle' => 'cancelled',
                    ]),

                TextColumn::make('priority')
                    ->badge()
                    ->colors([
                        'secondary' => 'low',
                        'primary' => 'medium',
                        'warning' => 'high',
                        'danger' => 'critical',
                    ]),

                TextColumn::make('category')
                    ->badge()
                    ->colors([
                        'info' => 'features',
                        'success' => 'improvements',
                        'warning' => 'fixes',
                        'secondary' => 'infrastructure',
                    ]),

                TextColumn::make('estimated_date')
                    ->date('M j, Y')
                    ->sortable()
                    ->color(fn (RoadmapItem $record) => $record->is_overdue ? 'danger' : null),

                TextColumn::make('likes_count')
                    ->label('Likes')
                    ->getStateUsing(fn (RoadmapItem $record) => $record->likes()->count())
                    ->badge()
                    ->color('success')
                    ->icon('heroicon-o-heart')
                    ->sortable(query: function ($query, $direction) {
                        return $query->withCount('likes')->orderBy('likes_count', $direction);
                    }),

                BooleanColumn::make('is_published')
                    ->label('Published'),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'planned' => 'Planned',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),

                SelectFilter::make('priority')
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                        'critical' => 'Critical',
                    ]),

                SelectFilter::make('category')
                    ->options([
                        'features' => 'New Features',
                        'improvements' => 'Improvements',
                        'fixes' => 'Bug Fixes',
                        'infrastructure' => 'Infrastructure',
                    ]),

                Tables\Filters\TernaryFilter::make('is_published')
                    ->label('Published'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoadmapItems::route('/'),
            'create' => Pages\CreateRoadmapItem::route('/create'),
            'edit' => Pages\EditRoadmapItem::route('/{record}/edit'),
        ];
    }
}
