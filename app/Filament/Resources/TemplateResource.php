<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TemplateResource\Pages;
use App\Models\Template;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Filament\Forms\Components\FileUpload;
class TemplateResource extends Resource
{
    protected static ?string $model = Template::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Admin';

    // public static function canCreate(): bool
    // {
    //     return auth()->user()->isSuperAdmin();
    // }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->default(null),
                Select::make('font')
                    ->label('Font')
                    ->options([
                        'arial' => 'Arial',
                        'times_new_roman' => 'Times New Roman',
                        'calibri' => 'Calibri',
                    ])
                    ->required()
                    ->placeholder('Choose the font style'),

                Select::make('font_size')
                    ->label('Font Size')
                    ->options([
                        '12' => '12 pt',
                        '14' => '14 pt',
                        '16' => '16 pt',
                        '18' => '18 pt',
                    ])
                    ->required()
                    ->placeholder('Select font size'),

                Select::make('heading')
                    ->label('Heading Style')
                    ->options([
                        '24' => '24 pt',
                        '26' => '26 pt',
                        '28' => '28 pt',
                    ])
                    ->required()
                    ->placeholder('Select heading style'),

                Select::make('sub_heading')
                    ->label('Subheading Style')
                    ->options([
                        '18' => '18 pt',
                        '20' => '20 pt',
                        '22' => '22 pt',
                        '24' => '24 pt',
                    ])
                    ->required()
                    ->placeholder('Select subheading style'),

                Select::make('line_space')
                    ->label('Line Spacing')
                    ->options([
                        '1.0' => '1.0',
                        '1.5' => '1.5',
                        '2.0' => '2.0',
                    ])
                    ->required()
                    ->placeholder('Select line spacing'),

                Select::make('paragraph_space')
                    ->label('Paragraph Spacing')
                    ->options([
                        '12' => '12',
                        '14' => '14',
                        '16' => '16',
                        '18' => '18',
                    ])
                    ->required()
                    ->placeholder('Select paragraph spacing'),

                Select::make('page_size')
                    ->label('Page Size')
                    ->options([
                        'a4' => 'A4',
                        'letter' => 'Letter',
                        'legal' => 'Legal',
                    ])
                    ->required()
                    ->placeholder('Select page size'),

                Forms\Components\Fieldset::make('Margin Settings')
                    ->schema([
                        Select::make('margin_top')
                            ->label('Top Margin')
                            ->options([
                                '5' => 'Ultra Narrow (5px)',
                                '10' => 'Very Narrow (10px)',
                                '15' => 'Narrow (15px)',
                                '20' => 'Slightly Narrow (20px)',
                                '25' => 'Moderate (25px)',
                                '30' => 'Slightly Wide (30px)',
                                '40' => 'Wide (40px)',
                                '50' => 'Very Wide (50px)',
                                '60' => 'Extra Wide (60px)',
                                '80' => 'Ultra Wide (80px)',
                            ])
                            ->default('20')
                            ->required()
                            ->placeholder('Select top margin size'),

                        Select::make('margin_bottom')
                            ->label('Bottom Margin')
                            ->options([
                                '5' => 'Ultra Narrow (5px)',
                                '10' => 'Very Narrow (10px)',
                                '15' => 'Narrow (15px)',
                                '20' => 'Slightly Narrow (20px)',
                                '25' => 'Moderate (25px)',
                                '30' => 'Slightly Wide (30px)',
                                '40' => 'Wide (40px)',
                                '50' => 'Very Wide (50px)',
                                '60' => 'Extra Wide (60px)',
                                '80' => 'Ultra Wide (80px)',
                            ])
                            ->default('20')
                            ->required()
                            ->placeholder('Select bottom margin size'),

                        Select::make('margin_left')
                            ->label('Left Margin')
                            ->options([
                                '5' => 'Ultra Narrow (5px)',
                                '10' => 'Very Narrow (10px)',
                                '15' => 'Narrow (15px)',
                                '20' => 'Slightly Narrow (20px)',
                                '25' => 'Moderate (25px)',
                                '30' => 'Slightly Wide (30px)',
                                '40' => 'Wide (40px)',
                                '50' => 'Very Wide (50px)',
                                '60' => 'Extra Wide (60px)',
                                '80' => 'Ultra Wide (80px)',
                            ])
                            ->default('20')
                            ->required()
                            ->placeholder('Select left margin size'),

                        Select::make('margin_right')
                            ->label('Right Margin')
                            ->options([
                                '5' => 'Ultra Narrow (5px)',
                                '10' => 'Very Narrow (10px)',
                                '15' => 'Narrow (15px)',
                                '20' => 'Slightly Narrow (20px)',
                                '25' => 'Moderate (25px)',
                                '30' => 'Slightly Wide (30px)',
                                '40' => 'Wide (40px)',
                                '50' => 'Very Wide (50px)',
                                '60' => 'Extra Wide (60px)',
                                '80' => 'Ultra Wide (80px)',
                            ])
                            ->default('20')
                            ->required()
                            ->placeholder('Select right margin size'),

                        Forms\Components\Hidden::make('margins')
                            ->default('custom')
                            ->dehydrateStateUsing(function ($state, $get) {
                                return json_encode([
                                    'top' => (int)$get('margin_top'),
                                    'bottom' => (int)$get('margin_bottom'),
                                    'left' => (int)$get('margin_left'),
                                    'right' => (int)$get('margin_right'),
                                ]);
                            }),
                    ]),

                Select::make('text_align')
                    ->label('Text Alignment')
                    ->options([
                        'left' => 'Left',
                        'center' => 'Center',
                        'right' => 'Right',
                    ])
                    ->required()
                    ->placeholder('Choose text alignment'),

                FileUpload::make('image')
                    ->disk('s3')
                    ->directory('templates')
                    ->visibility('private'),

                Toggle::make('is_featured'),
                Toggle::make('is_default'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('font')
                    ->searchable(),
                Tables\Columns\TextColumn::make('font_size')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('heading')
                    ->searchable(),
                Tables\Columns\TextColumn::make('sub_heading')
                    ->searchable(),
                Tables\Columns\TextColumn::make('line_space')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('text_align')
                    ->searchable(),
                ToggleColumn::make('is_featured'),
                ToggleColumn::make('is_default'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTemplates::route('/'),
            'create' => Pages\CreateTemplate::route('/create'),
            'edit' => Pages\EditTemplate::route('/{record}/edit'),
        ];
    }
}
