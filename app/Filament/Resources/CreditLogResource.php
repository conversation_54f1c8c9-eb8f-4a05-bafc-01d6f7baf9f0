<?php

namespace App\Filament\Resources;

use App\Enum\CreditLogActionEnum;
use App\Enum\CreditLogActionTypeEnum;
use App\Filament\Resources\CreditLogResource\Pages;
use App\Filament\Resources\CreditLogResource\RelationManagers;
use App\Http\Middleware\ApplyTenantPrefix;
use App\Models\CreditLog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CreditLogResource extends Resource
{
    protected static ?string $model = CreditLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 14;

    // protected static string|array $routeMiddleware = [ApplyTenantPrefix::class];

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin() || auth()->user()->isSupport();
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->rowIndex(),
                TextColumn::make('credit')
                    ->badge()
                    ->color(fn(CreditLog $record) => $record->action_type === CreditLogActionTypeEnum::CREDIT_IN ? 'success' : 'danger')
                    ->formatStateUsing(fn(CreditLog $record) => ($record->action_type === CreditLogActionTypeEnum::CREDIT_IN ? '+' : '-').$record->credit),
                TextColumn::make('previous_credit')
                    ->label('Previous')
                    ->badge()
                    ->color('gray')
                    ->formatStateUsing(fn(CreditLog $record) => number_format($record->previous_credit, 2)),
                TextColumn::make('current_credit')
                    ->label('Current')
                    ->badge()
                    ->color('gray')
                    ->formatStateUsing(fn(CreditLog $record) => number_format($record->current_credit, 2)),
                TextColumn::make('description'),
                TextColumn::make('action')->label('Type'),
                TextColumn::make('user_id')
                    ->label('User')
                    ->formatStateUsing(fn(CreditLog $record) => $record->user->name)
                    ->url(fn(CreditLog $record): string => "/users/{$record->user_id}")
                    ->visible(auth()->user()->isAdmin() || auth()->user()->isSupport())
                    ->color('primary')
                    ->words(1, ''),

                TextColumn::make('created_at')->label('Created'),
            ])
            ->modifyQueryUsing(function ($query) {
                $user = auth()->user();
                if ($user->isAdmin() || $user->isSuperAdmin() || $user->isSupport()) {
                    return $query;
                }
                return $query->where('user_id', $user->getUser()->id);
            })
            ->filters([
                SelectFilter::make('action')->options(CreditLogActionEnum::class)->label('Action')->placeholder('Select Action')->multiple(),
                SelectFilter::make('action_type')->options(CreditLogActionTypeEnum::class)->label('Action type')->placeholder('Select Action type')->multiple(),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated(config('filament-config.pagination_option'));
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreditLogs::route('/'),
        ];
    }
}
