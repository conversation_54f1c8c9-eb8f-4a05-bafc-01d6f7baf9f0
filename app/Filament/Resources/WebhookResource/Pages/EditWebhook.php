<?php

namespace App\Filament\Resources\WebhookResource\Pages;

use App\Filament\Resources\WebhookResource;
use Filament\Pages\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditWebhook extends EditRecord
{
    protected static string $resource = WebhookResource::class;

    /**
     * @throws \Exception
     */
    protected function getActions(): array
    {
        return [
            DeleteAction::make()
                ->visible(fn() => auth()->user()->isSuperAdmin()),
        ];
    }
}
