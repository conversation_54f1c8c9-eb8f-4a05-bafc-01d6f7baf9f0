<?php

namespace App\Filament\Resources\SubscriptionResource\Pages;

use App\Filament\Resources\SubscriptionResource;
use App\Service\Logger as ServiceLogger;
use App\Services\Logger;
use Filament\Pages\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditSubscription extends EditRecord
{
    protected static string $resource = SubscriptionResource::class;

    /**
     * @throws \Exception
     */
    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make()
                ->visible(fn() => auth()->user()->isSuperAdmin())
                ->after(function () {
                    (new ServiceLogger(static::class, $this->record->user))->log($this->record->plan->name.' subscription is detached by '.auth()->user()->name);
                }),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $previousSubscription = $record->plan->name;

        $record->update($data);

        $logMessage = '';

        if ($previousSubscription !== $record->plan->name) {
            $logMessage .= 'Your subscription is changed from '.$previousSubscription.' to '.$record->plan->name;
        }

        (new ServiceLogger(static::class, $record->user))->log($logMessage);

        return $record;
    }
}
