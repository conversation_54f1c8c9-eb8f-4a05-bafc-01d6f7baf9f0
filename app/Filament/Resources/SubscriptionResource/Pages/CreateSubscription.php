<?php

namespace App\Filament\Resources\SubscriptionResource\Pages;

use App\Filament\Resources\SubscriptionResource;
use App\Models\Subscription;
use App\Services\LegacyUserService;
use Filament\Resources\Pages\CreateRecord;

class CreateSubscription extends CreateRecord
{
    protected static string $resource = SubscriptionResource::class;

    public function create(bool $another = false): void
    {
        $subscription = Subscription::create($this->form->getState());

        if ($subscription->wasRecentlyCreated) {
            $legacyService = new LegacyUserService();
            $isLegacyUser = $legacyService->isLegacyUser($subscription->user);
            $userType = $isLegacyUser ? 'Legacy' : 'New';

            $subscription->user->log(
                "CreateSubscription: {$subscription?->plan?->name} subscription is assigned by ".auth()->user()->name." to {$userType} user\n"
                ."Plan credits: {$subscription?->plan?->credits}\n"
                ."Plan includes all addons: ".($subscription?->plan?->includes_all_addons ? 'Yes' : 'No')."\n"
                ."Page limit: ".($subscription?->plan?->page_limit ?? 'Unlimited')."\n"
                ."Plan amount: $".($subscription?->plan?->amount ?? '0')
            );
        }
    }
}
