<?php

namespace App\Filament\Resources;

use App\Enum\FeatureEnum;
use App\Enum\PlanTypeEnum;
use App\Enum\UserRoleEnum;
use App\Filament\Resources\PlanResource\Pages;
use App\Filament\Resources\PlanResource\RelationManagers\UserRelationManager;
use App\Models\Plan;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\Concerns\BelongsToTable;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component as LivewireComponent;

class PlanResource extends Resource
{

    protected static ?string $model = Plan::class;

    protected static ?string $slug = 'plans';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 101;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    public static function canCreate(): bool
    {
        return auth()->user()->isSuperAdmin();
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->withCount([
            'users',
            // 'subscriptions',
            // 'subscriptions as active_subscriptions_count' => fn($query) => $query->where('status', 'active')->where('payment_method', '!=', 'free'),
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Section::make('Type')->schema([
                    TextInput::make('name')
                        ->autofocus()
                        ->required()
                        ->placeholder('Enter plan name'),

                    TextInput::make('slug')
                        ->autofocus()
                        ->required()
                        ->placeholder('Enter plan name'),

                    Select::make('type')
                        ->options(PlanTypeEnum::class)
                        ->required(),

                    TextInput::make('wplus_code'),
                    TextInput::make('amount')->required(),
                    TextInput::make('credits')->required(),

                    Textarea::make('page_limit'),
                    Textarea::make('purchase_url'),
                    Textarea::make('description'),
                    Checkbox::make('show')
                        ->label('Show in price table')
                        ->reactive(),
                    TextArea::make('features')->helperText('use pipe ( | ) to separate each feature'),

                    Toggle::make('early_bird')
                        ->required(),
                        
                    Checkbox::make('includes_all_addons')
                        ->label('Include all addons'),
                        
                    Checkbox::make('permissions.has_everything')
                        ->label('Has Access Everything')
                        ->reactive()
                        ->helperText('Check this if the plan has everything. This will override all other permissions.'),

                    CheckboxList::make('permissions.features')
                        ->hidden(fn($get) => $get('permissions.has_everything'))
                        ->options(FeatureEnum::getOptions()),
                ])
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {

        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                IconColumn::make('show')->boolean()->sortable(),
                ToggleColumn::make('early_bird')->sortable(),

                TextColumn::make('amount')->label('Price')->formatStateUsing(function ($state) {
                    return '$'.number_format($state);
                })->alignCenter()->sortable()->toggleable(),

                TextColumn::make('credits')->sortable(),
                TextColumn::make('description')->sortable(),
            ])->actions([
                ViewAction::make(),
                EditAction::make()->visible(auth()->user()->role === UserRoleEnum::SUPER_ADMIN),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->paginated(config('filament-config.pagination_option'));
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
            'view' => Pages\ViewPlan::route('/{record}'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }

    public static function getRelations(): array
    {
        return [
            // UserRelationManager::class,
            // SubscriptionRelationManager::class,
            // WebhookRelationManager::class,
        ];
    }
}
