<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\UserRelationManager;
use App\Filament\RelationManagers\WebhookRelationManager;
use App\Filament\Resources\SubscriptionResource\Pages;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Webhook;
use App\Services\LegacyUserService;
use Filament\Forms\Components\Textarea;
use Filament\Tables;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $slug = 'subscriptions';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 102;

    protected static ?string $recordTitleAttribute = 'id';

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    public static function canCreate(): bool
    {
        return auth()->user()->isSuperAdmin();
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Fieldset::make('Subscription')->columns(2)->schema([
                    Select::make('user_id')
                        ->searchable()
                        ->relationship('user', 'name')
                        ->placeholder('Select User')
                        ->required()
                        ->reactive()
                        ->afterStateUpdated(fn (callable $set) => $set('plan_id', null)),

                    Select::make('plan_id')
                        ->placeholder('Select Plan')
                        ->required()
                        ->options(function (callable $get) {
                            $userId = $get('user_id');
                            if (!$userId) {
                                return Plan::pluck('name', 'id')->toArray();
                            }

                            $user = User::find($userId);
                            if (!$user) {
                                return Plan::pluck('name', 'id')->toArray();
                            }

                            $legacyService = new LegacyUserService();
                            $isLegacyUser = $legacyService->isLegacyUser($user);

                            if ($isLegacyUser) {
                                // Show legacy plans (effective_date <= 2024-07-31)
                                return Plan::where('effective_date', '<=', '2024-07-31')
                                          ->orWhereNull('effective_date')
                                          ->pluck('name', 'id')
                                          ->toArray();
                            } else {
                                // Show new plans (effective_date > 2024-07-31)
                                return Plan::where('effective_date', '>', '2024-07-31')
                                          ->pluck('name', 'id')
                                          ->toArray();
                            }
                        })
                        ->helperText(function (callable $get) {
                            $userId = $get('user_id');
                            if (!$userId) {
                                return 'Select a user first to see appropriate plans';
                            }

                            $user = User::find($userId);
                            if (!$user) {
                                return 'User not found';
                            }

                            $legacyService = new LegacyUserService();
                            $isLegacyUser = $legacyService->isLegacyUser($user);

                            return $isLegacyUser
                                ? 'Showing legacy plans (includes all addons, unlimited pages)'
                                : 'Showing new pricing plans (addon-based pricing)';
                        }),
                ]),

                Fieldset::make('Subscription')->columns(3)->schema([

                    Select::make('payment_method')->required()->options([
                        'free' => 'Free',
                        'paypal' => 'Paypal',
                        'stripe' => 'Stripe',
                        'paytm' => 'Paytm',
                        'razorpay' => 'Razorpay',
                        'instamojo' => 'Instamojo',
                        'mollie' => 'Mollie',
                        'payu' => 'PayU',
                        'ppm' => 'Ppm',
                        'wallet' => 'Wallet',
                        'bank_transfer' => 'Bank Transfer',
                        'manual' => 'Manual',
                    ])->default('free'),

                    Select::make('status')->required()->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'cancelled' => 'Cancelled',
                        "refund" => "Refund",
                        'expired' => 'Expired',
                        "subscr_cancelled" => "Subscr Cancelled",
                        "subscr_failed_declined" => "Subscr Failed Declined",
                        "subscr_refunded" => "Subscr Refunded",
                        "subscr_suspended" => "Subscr Suspended",
                    ])->default('active'),

                    TextInput::make('created_content_count')->default(0)->numeric()->hiddenOn(['create']),
                ]),


                Fieldset::make('W+')->columns(3)->schema([
                    TextInput::make('wplus_txnid'),
                    TextInput::make('wplus_purchase_id'),
                    TextInput::make('wplus_sale_id'),
                ]),

                Fieldset::make('Amount')->columns(2)->schema([
                    TextInput::make('wplus_sale_amount'),
                    TextInput::make('wplus_sale_earning'),
                ]),

                Fieldset::make('Refund credit')->columns(1)->schema([
                    TextInput::make('Credit Content Quota')
                        ->formatStateUsing(fn(?Subscription $record) => $record?->getMeta('content_quota')),
                ])->visible(fn(?Subscription $record) => $record?->plan?->name === 'Credit Refund'),

                TextInput::make('subscribed_at')->visibleOn(['view']),

                Textarea::make('notes'),

                DateTimePicker::make('subscribed_at')->visibleOn(['edit']),
                DateTimePicker::make('subscription_updated_at')->visibleOn(['edit']),
                DateTimePicker::make('cancelled_at')->visibleOn(['edit']),

                Placeholder::make('subscribed_at')
                    ->label('Subscribed At')
                    ->visibleOn(['view'])
                    ->content(fn(?Subscription $record): string => $record?->subscribed_at ? ($record?->subscribed_at?->diffForHumans().' ('.$record?->subscribed_at->toDateTimeString().')') : '-'),

                Placeholder::make('cancelled_at')
                    ->label('Cancelled At')
                    ->visibleOn(['view'])
                    ->visible(fn(?Subscription $record): bool => (bool) $record?->cancelled_at)
                    ->content(fn(?Subscription $record): string => $record?->cancelled_at ? ($record?->cancelled_at?->diffForHumans().' ('.$record?->cancelled_at->toDateTimeString().')') : '-'),

                Placeholder::make('subscription_updated_at')
                    ->label('Subscription Updated At')
                    ->visibleOn(['view'])
                    ->content(fn(?Subscription $record
                    ): string => $record?->subscription_updated_at ? ($record?->subscription_updated_at?->diffForHumans().' ('.$record?->subscription_updated_at->toDateTimeString().')') : '-'),

                Placeholder::make('Next Billing')
                    ->label('Next Billing')
                    ->visibleOn(['view'])
                    ->content(fn(?Subscription $record
                    ): string => $record?->subscription_updated_at ? ($record?->subscription_updated_at->addDays(30)?->diffForHumans().' ('.$record?->subscription_updated_at->addDays(30)->toDateTimeString().')') : '-'),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('plan.name')->searchable()->sortable(),
                TextColumn::make('user.name')->searchable()->sortable(),
                TextColumn::make('status')->badge()->color('gray')->searchable()->sortable()->alignCenter(),
                TextColumn::make('payment_method')->badge()->color('gray')->searchable()->sortable()->alignCenter(),
                TextColumn::make('subscription_updated_at')->label('Updated At')->sortable()->since()->alignCenter(),
            ])->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn() => auth()->user()->isSuperAdmin()),
            ])
            ->paginated(config('filament-config.pagination_option'));
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }

    public static function getRelations(): array
    {
        return [
            WebhookRelationManager::class,
        ];
    }
}
