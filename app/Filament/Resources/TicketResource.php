<?php

namespace App\Filament\Resources;

use App\Enum\UserRoleEnum;
use App\Filament\Fields\TicketCommentField;
use App\Filament\Resources\TicketResource\EditTicket;
use App\Filament\Resources\TicketResource\ListTicket;
use App\Filament\Resources\TicketResource\Pages;
use App\Filament\Resources\TicketResource\RelationManagers;
use App\Filament\Resources\TicketResource\ViewTicket;
use App\Models\Campaign;
use App\Models\Comment;
use App\Models\Content;
use App\Models\KeywordResearch;
use App\Models\Ticket;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Sgcomptech\FilamentTicketing\Filament\Resources\TicketResource\Pages\CreateTicket;

class TicketResource extends Resource
{
    protected static ?string $model = Ticket::class;

    protected static ?string $navigationLabel = "Tickets";
    protected static ?string $modelLabel = "Tickets";
    protected static ?string $navigationIcon = 'heroicon-o-ticket';
    protected static ?int $navigationSort = 4;
    public static function getEloquentQuery(): Builder
    {
        if (request()->is('tickets')) {
            $subQuery = [
                'latest_comment_body' => Comment::select('content')
                    ->whereColumn('ticket_id', 'tickets.id')
                    ->latest()
                    ->take(1),
            ];
        } else {
            $subQuery = [];
        }

        return parent::getEloquentQuery()->when(auth()->user()->cannot('manageAllTickets', Ticket::class), function ($query) {
            return $query->where('user_id', auth()->user()->id);
        })->withCount('comments')->addSelect($subQuery);
    }

    public static function form(Form $form): Form
    {
        $user = auth()->user();
        if (config('filament-ticketing.use_authorization')) {
            $cannotManageAllTickets = $user->cannot('manageAllTickets', Ticket::class);
            $cannotManageAssignedTickets = $user->cannot('manageAssignedTickets', Ticket::class);
            $cannotAssignTickets = $user->cannot('assignTickets', Ticket::class);
        } else {
            $cannotManageAllTickets = false;
            $cannotManageAssignedTickets = false;
            $cannotAssignTickets = false;
        }

        $statuses = array_map(fn($e) => __($e), config('filament-ticketing.statuses'));
        $priorities = array_map(fn($e) => __($e), config('filament-ticketing.priorities'));

        $colspan = [
            'default' => 3,
            'sm' => 1,
            'md' => 1,
        ];

        return $form
            ->schema([
                // Fieldset::make(__('Ticket Guide'))->schema([
                //     Placeholder::make('')
                //         ->columnSpan('full')
                //         ->content(new HtmlString(view('ticket-guide')->render()))
                //         ->visibleOn('create'),
                // ])->visibleOn('create'),

                Section::make([

                    Placeholder::make('User Name')
                        ->columnSpan($colspan)
                        ->label(__('User Name'))
                        ->content(fn($record) => $record?->user->name)
                        ->hiddenOn('create'),

                    Placeholder::make('User Email')
                        ->columnSpan($colspan)
                        ->label(__('User Email'))
                        ->content(fn($record) => $record?->user->email)
                        ->hiddenOn('create'),

                    Placeholder::make('created_at')
                        ->columnSpan($colspan)
                        ->label("Created")
                        ->content(fn($record) => $record?->created_at?->diffForHumans())
                        ->hiddenOn('create'),

                    TextInput::make('title')
                        ->translateLabel()
                        ->required()
                        ->maxLength(255)
                        ->columnSpan(3)
                        ->placeholder(__('Enter a title for your ticket'))
                        ->disabledOn('edit')
                        ->hiddenOn(['edit', 'view']),

                    TicketCommentField::make()
                        ->columnSpan(3)
                        ->disabledOn(['edit', 'view'])
                        ->hiddenOn(['edit', 'view'])
                        ->placeholder(__('Type your message here...'))
                        ->formatStateUsing(function ($state) {
                            $state = preg_replace('/(?<=[\s>])(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[A-Z0-9+&@#\/%=~_|]/i', '<a href="$0" target="_blank">$0</a>', $state);

                            return ("<div class='prose dark:prose-invert aiwisemind-ticket-comment max-w-full'>$state</div>");
                        }),

                    Placeholder::make('content')->label('')->content(function ($record) {

                        $data = "<h4>$record->title</h4>".$record->content;
                        $state = preg_replace('/(?<=[\s>])(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[A-Z0-9+&@#\/%=~_|]/i', '<a href="$0" target="_blank">$0</a>', $data);

                        return new HtmlString(("<div class='prose dark:prose-invert aiwisemind-ticket-comment max-w-full'>$state</div>"));
                    })->visibleOn(['view', 'edit'])->columnSpan('full'),

                    Select::make('status')
                        ->translateLabel()
                        ->columnSpan($colspan)
                        ->options($statuses)
                        ->required()
                        // ->disabled(fn($record) => (
                        //     $cannotManageAllTickets &&
                        //     ($cannotManageAssignedTickets || $record?->assigned_to_id != $user->id)
                        // ))
                        ->hiddenOn('create'),

                    Select::make('priority')
                        ->translateLabel()
                        ->columnSpan($colspan)
                        ->default('1')
                        ->options($priorities)
                        ->disabledOn('edit')
                        ->required(),

                    Select::make('assigned_to_id')
                        ->label(__('Assign Ticket To'))
                        ->columnSpan($colspan)
                        ->searchable()
                        ->options(function () {
                            return config('filament-ticketing.user-model')::query()
                                ->whereIn('role', UserRoleEnum::ticketAccessibleRoles())
                                ->limit(50)
                                ->get()
                                ->filter(fn($user) => $user->can('manageAssignedTickets', Ticket::class))
                                ->pluck('name', 'id');
                        })
                        ->getOptionLabelUsing(fn($value): ?string => config('filament-ticketing.user-model')::find($value)?->name)
                        // ->disabled($cannotAssignTickets)
                        ->hiddenOn('create'),

                    Select::make('campaign_id')
                        ->label(__('Attach Campaign'))
                        ->translateLabel()
                        ->columnSpan($colspan)
                        ->searchable()
                        ->live()
                        ->options(function () {
                            return Campaign::query()
                                ->where('user_id', auth()->user()?->id)
                                ->limit(50)
                                ->get()
                                ->pluck('topic', 'id')
                                ->toArray();
                        })
                        ->afterStateUpdated(fn (Set $set) => $set('content_id', null))
                        ->hiddenOn(['edit', 'view']),

                    // Select::make('keyword_research_id')
                    //     ->label(__('Attach Keyword Research'))
                    //     ->translateLabel()
                    //     ->columnSpan($colspan)
                    //     ->searchable()
                    //     ->options(function (Get $get) {
                    //         return KeywordResearch::query()
                    //             ->where('user_id', auth()->user()->id)
                    //             ->limit(50)
                    //             ->get()
                    //             ->pluck('keyword_research', 'id')
                    //             ->toArray();
                    //     })
                    //     ->hiddenOn(['edit', 'view']),

                    Placeholder::make('campaign_name')
                        ->columnSpan($colspan)
                        ->label(__('Attach Campaign'))
                        ->content(function ($record) {
                            $name = Campaign::find($record->campaign_id)?->getAttribute('name');
//                            $route = route('filament.admin.resources.campaign.view', $record->campaign_id ?? '');
                            $route = url('/campaigns/' . $record->campaign_id ?? '');

                            $data = "<a class='text-primary-600 transition hover:text-primary-500 hover:underline focus:text-primary-500 focus:underline' href='$route' target='_blank'>$name</a>";
                            $state = preg_replace('/(?<=[\s>])(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[A-Z0-9+&@#\/%=~_|]/i', '<a href="$0" target="_blank">$0</a>', $data);

                            return new HtmlString(("<div class='prose dark:prose-invert aiwisemind-ticket-comment max-w-full'>$state</div>"));

//                            return new HtmlString(html_url_to_anchor_replacements("<a class='text-primary-600 transition hover:text-primary-500 hover:underline focus:text-primary-500 focus:underline' href='$route' target='_blank'>$name</a>"));
                        })
                        ->visible(function ($record) {
                            return ($record?->campaign_id);
                        })
                        ->hiddenOn('create'),
                ])->columns(3),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        $user = auth()->user();

        if (config('filament-ticketing.use_authorization')) {
            $canManageAllTickets = $user->can('manageAllTickets', Ticket::class);
            $canManageAssignedTickets = $user->can('manageAssignedTickets', Ticket::class);
        } else {
            $canManageAllTickets = true;
            $canManageAssignedTickets = true;
        }

        $statuses = array_map(fn($e) => __($e), config('filament-ticketing.statuses'));
        $priorities = array_map(fn($e) => __($e), config('filament-ticketing.priorities'));

        return $table
            ->columns([
                TextColumn::make('id')->sortable()->visible($canManageAllTickets),

                TextColumn::make('identifier')
                    ->translateLabel()
                    ->toggleable()
                    ->toggledHiddenByDefault($canManageAllTickets)
                    ->searchable(),

                TextColumn::make('user.name')
                    ->translateLabel()
                    ->sortable()
                    ->searchable()
                    ->words(1)
                    ->visible($canManageAllTickets)
                    ->url(fn(Ticket $record): string => "/users/{$record->user_id}")
                    ->openUrlInNewTab(),

                TextColumn::make('title')
                    ->translateLabel()
                    ->searchable()
                    ->toggleable()
                    ->formatStateUsing(function (Ticket $record) {
                        $title = str($record->title)->words(5)->limit(20);
                        return new HtmlString("<span class='text-sm'>".$title."</span>");
                    })
                    ->tooltip(fn(Ticket $record) => $record->title),

                TextColumn::make('priority')
                    ->alignCenter()
                    ->toggleable()
                    ->toggledHiddenByDefault()
                    ->translateLabel()
                    ->formatStateUsing(fn($record) => $priorities[$record->priority] ?? '-')
                    ->color(fn($record) => $record->priorityColor()),

                TextColumn::make('content')
                    ->label(__('Last Message'))
                    ->toggleable()
                    ->visible($canManageAllTickets)
                    ->tooltip(function (Ticket $record) {
                        // $user = $record->last_comment_user_name ?: \get_agent_name($record->last_comment_user_name);
                        $user = $record->last_comment_user_name;

                        $state = $record->latest_comment_body ?: $record->content;

                        $state = preg_replace("/<style\\b[^>]*>(.*?)<\\/style>/s", "", $state);

                        return html_entity_decode(str($state)->stripTags()->sanitizeHtml()->prepend($user.': '));
                    })
                    ->formatStateUsing(function (Ticket $record) {
                        // $user = $record->last_comment_user_name ?: \get_agent_name($record->last_comment_user_name);
                        $user = $record->last_comment_user_name;

                        $state = $record->latest_comment_body ?: $record->content;

                        $state = preg_replace("/<style\\b[^>]*>(.*?)<\\/style>/s", "", $state);

                        $state = html_entity_decode(str($state)->stripTags()->sanitizeHtml()->prepend($user.': ')->words(6)->limit(25));

                        return new HtmlString("<span class='text-sm'>$state</span>");
                    })
                    ->translateLabel(),


                TextColumn::make('status')
                    ->alignCenter()
                    ->toggleable()
                    ->badge()
                    ->color(function ($record) {
                        if ($record->status > 2) {
                            return 'success';
                        }

                        if (!$record->comments_count) {
                            return 'danger';
                        }

                        return $record->last_comment_user_id == $record->user_id ? 'warning' : 'secondary';
                    })
                    ->formatStateUsing(function (Ticket $record) use ($statuses) {
                        if ($record->status > 2) {
                            return $statuses[$record->status] ?? '-';
                        }

                        if (!$record->comments_count) {
                            return __('New ');
                        }

                        if ($record->user_id == auth()->user()->id) {
                            return $record->last_comment_user_id == $record->user_id ? __('Waiting for agent') : __('Agent Replied');
                        }

                        return $record->last_comment_user_id == $record->user_id ? __('Waiting for agent') : __('Replied');
                    })
                    ->translateLabel(),

                TextColumn::make('comments_count')
                    ->label(__('Comments'))
                    ->badge()
                    ->color('gray')
                    ->toggleable()
                    ->alignCenter()
                    ->translateLabel(),

                TextColumn::make('assigned_to.name')
                    ->alignCenter()
                    ->toggleable()
                    ->translateLabel(),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->since()
                    ->toggleable()
                    ->toggledHiddenByDefault()
                    ->alignCenter()
                    ->sortable()
                    ->translateLabel(),

                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->since()
                    ->toggleable()
                    ->alignCenter()
                    ->sortable()
                    ->translateLabel(),

                // BadgeColumn::make('Last Reply')
                //     ->label(__('Last Reply'))
                //     ->formatStateUsing(function (Ticket $record) {
                //         return $record->latest_comment ?? null;
                //
                //         // return $record->user?->name;
                //     })
                //     ->alignCenter()
                //     ->translateLabel()
            ])
            ->filters(array(
                SelectFilter::make('status')
                    ->translateLabel()
                    ->multiple()
                    ->default(array('1', '2'))
                    ->options($statuses),

                SelectFilter::make('priority')
                    ->translateLabel()
                    ->multiple()
                    ->options($priorities),

                TernaryFilter::make('hide_replied')
                    ->translateLabel()
                    ->visible($canManageAllTickets)
                    ->default(session()->has('kwestify_ticket_hide_replied'))
                    ->queries(
                        true: function (Builder $query) {
                            $query->where(function ($q) {
                                return $q->whereRaw('last_comment_user_id = user_id')->orWhereNull('last_comment_user_id');
                            });

                            session()->put('kwestify_ticket_hide_replied', true);

                            return $query;
                        },
                        false: function (Builder $query) {
                            session()->forget('kwestify_ticket_hide_replied');
                            return $query;
                        },
                        blank: function (Builder $query) {
                            return $query;
                        },
                    )
                    ->options($priorities),
            ))
            ->actions([
                ViewAction::make()->hidden($canManageAllTickets)->url(fn(Ticket $record): string => "/tickets/{$record->id}"),
                EditAction::make()->visible($canManageAllTickets || $canManageAssignedTickets)->url(fn(Ticket $record): string => "/tickets/{$record->id}/edit"),
            ])
            ->bulkActions([
                BulkAction::make('Close Selected')
                    ->label('Close Selected')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible($canManageAllTickets)
                    ->action(function (Collection $records, array $data) {
                        $records->each(function ($record) {
                            $record->update(['status' => 4]);
                        });
                    })
                    ->icon('heroicon-o-check-circle')
                    ->deselectRecordsAfterCompletion(),

                DeleteBulkAction::make()->visible($canManageAllTickets),
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated(config('filament-config.pagination_option'));
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CommentsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTicket::route('/'),
            'create' => CreateTicket::route('/create'),
            'edit' => EditTicket::route('/{record}/edit'),
            'view' => ViewTicket::route('/{record}'),
        ];
    }
}
