<?php

namespace App\Filament\Resources\CampaignResource\Fields;

use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Select;



class SecondStepFields
{
    public static function make()
    {
        return Wizard\Step::make('Audience & Details')
        ->icon('heroicon-o-users')
        ->schema([
            Select::make('form.targeted_audience')
                ->label('Targeted Audience')
                ->options([
                    'students' => 'Students',
                    'professionals' => 'Professionals',
                    'general_public' => 'General Public',
                ])
                ->placeholder('Select the audience for your ebook'),

            Select::make('form.purpose')
                ->label('Purpose')
                ->options([
                    'informative' => 'Informative',
                    'educational' => 'Educational',
                    'entertainment' => 'Entertainment',
                ])
                ->placeholder('Select the purpose of your ebook'),

            Select::make('form.tone')
                ->label('Tone')
                ->options([
                    'formal' => 'Formal',
                    'casual' => 'Casual',
                    'technical' => 'Technical',
                ])
                ->placeholder('Choose the tone of your ebook'),

            Select::make('form.language')
                ->label('Language')
                ->options([
                    'english' => 'English',
                    'spanish' => 'Spanish',
                    'french' => 'French',
                ])
                ->placeholder('Select the language for your ebook'),
        ]);
    }
}
