<?php

namespace App\Filament\Resources\CampaignResource\Fields;

use App\Models\Template;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Wizard;

class ThirdStepFields
{

    public static function make()
    {
        $templates = Template::orderByDesc('is_featured')->get();
        return Wizard\Step::make('Ebook Format')
            ->icon('heroicon-o-book-open')
            ->schema([
                ViewField::make('sample')
                    ->view('filament.forms.components.modal')
                    ->viewData([
                        "templates" => $templates,
                    ]),
                Select::make('font')
                    ->label('Font')
                    ->options([
                        'arial' => 'Arial',
                        'times_new_roman' => 'Times New Roman',
                        'calibri' => 'Calibri',
                    ])
                    ->placeholder('Choose the font style'),

                Select::make('font_size')
                    ->label('Font Size')
                    ->options([
                        '12' => '12 pt',
                        '14' => '14 pt',
                        '16' => '16 pt',
                    ])
                    ->placeholder('Select font size'),

                Select::make('heading')
                    ->label('Heading Style')
                    ->options([
                        'bold' => 'Bold',
                        'italic' => 'Italic',
                        'underline' => 'Underline',
                    ])
                    ->placeholder('Select heading style'),

                Select::make('sub_heading')
                    ->label('Subheading Style')
                    ->options([
                        'bold' => 'Bold',
                        'italic' => 'Italic',
                        'underline' => 'Underline',
                    ])
                    ->placeholder('Select subheading style'),

                Select::make('line_space')
                    ->label('Line Spacing')
                    ->options([
                        '1.0' => '1.0',
                        '1.5' => '1.5',
                        '2.0' => '2.0',
                    ])
                    ->placeholder('Select line spacing'),

                Select::make('paragraph_space')
                    ->label('Paragraph Spacing')
                    ->options([
                        'single' => 'Single',
                        'double' => 'Double',
                    ])
                    ->placeholder('Select paragraph spacing'),

                Select::make('page_size')
                    ->label('Page Size')
                    ->options([
                        'a4' => 'A4',
                        'letter' => 'Letter',
                        'legal' => 'Legal',
                    ])
                    ->placeholder('Select page size'),

                Select::make('margins')
                    ->label('Margins')
                    ->options([
                        'narrow' => 'Narrow',
                        'moderate' => 'Moderate',
                        'wide' => 'Wide',
                    ])
                    ->placeholder('Select margin size'),

                Select::make('text_align')
                    ->label('Text Alignment')
                    ->options([
                        'left' => 'Left',
                        'center' => 'Center',
                        'right' => 'Right',
                    ])
                    ->placeholder('Choose text alignment'),
            ]);
    }
}
