<?php

namespace App\Filament\Resources\CampaignResource\Fields;

use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;

use Illuminate\Support\HtmlString;

class FirstStepFields
{
    public static function make()
    {
        return Wizard\Step::make('Basic Information')
        ->icon('heroicon-o-document-text')
        ->schema([
            TextInput::make('name')
                ->label('Topic')
                ->required()
                ->placeholder('Enter the title of your ebook')
                ->helperText(new HtmlString("Specify the main topic of the ebook")),
            Textarea::make('form.description')
                ->required()
                ->label('Description')
                ->placeholder('Provide a brief description of the ebook')
                ->helperText(new HtmlString("Enter a concise description")),
            TextInput::make('form.total_page')
                ->numeric()
                ->required()
                ->label('Total Pages')
                ->placeholder('Enter total number of pages')
                ->helperText(new HtmlString("Specify the total page count for the ebook")),
        ]);
    }
}
