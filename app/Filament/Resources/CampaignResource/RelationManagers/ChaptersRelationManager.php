<?php

namespace App\Filament\Resources\CampaignResource\RelationManagers;

use App\Enum\CampaignStatusEnum;
use App\Jobs\GenerateEbookJob;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChaptersRelationManager extends RelationManager
{
    protected static string $relationship = 'chapters';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('title')
                                           ->label("Chapter title")
                                           ->fileAttachmentsDisk('s3')
                                           ->fileAttachmentsDirectory("campaign/".date('Y/m/d'))
                                           ->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
                                           ->required()->columnSpan("full"),

                Forms\Components\RichEditor::make('intro')
                                           ->fileAttachmentsDisk('s3')
                                           ->fileAttachmentsDirectory("campaign/".date('Y/m/d'))
                                           ->label("Chapter intro")
                                           ->fileAttachmentsDirectory('campaign-image/'.date('Y-m-d'))
                                           ->fileAttachmentsDisk(app()->environment('local') ? 'public' : 's3')
                                           ->required()->columnSpan("full"),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('plain_title')
            ->columns([
                Tables\Columns\TextColumn::make('title')->html(),
                Tables\Columns\TextColumn::make('status')
                                         ->badge()
                                         ->sortable()
                                         ->color(fn($state): string => $state->getColor())
                                         ->formatStateUsing(fn(CampaignStatusEnum $state): string => ucfirst($state->value)),
            ])
            ->defaultSort("chapter_number", "asc")
            ->reorderable('chapter_number')
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->mutateFormDataUsing(function (array $data){
                    $data["chapter_number"] = $this->ownerRecord->chapters->count() + 1;
                    $data["status"] = CampaignStatusEnum::DONE;
                    $data["chapter_total_words"] = str_word_count($data["intro"]);
                    return $data;
                }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('chapter')->label("Go to chapter")
                                     ->icon('heroicon-o-arrow-turn-down-right')
                                     ->url(function ($record){
                                         return "/chapters/{$record->id}/edit";
                                     })
                                     ->color('blue'),

                Tables\Actions\Action::make('chapter')->label("Open Editor")
                    ->icon('heroicon-o-pencil')
                    ->url(function ($record){
                        return "/standalone/edit-book/{$record->campaign_id}?chapter={$record->id}&edit=true";
                    })
                    ->openUrlInNewTab()
                    ->color('blue'),

                Tables\Actions\Action::make('download_audio')
                    ->label('Download MP3')
                    ->icon('heroicon-o-speaker-wave')
                    ->url(function ($record) {
                        $availableModels = $record->getAvailableAudioModels();

                        // If only one model, return direct URL
                        if (count($availableModels) === 1) {
                            return route('download.chapter.audio.single', [
                                'chapter' => $record->id,
                                'model' => $availableModels[0]
                            ]);
                        }

                        // Multiple models - return null to use action instead
                        return null;
                    })
                    ->openUrlInNewTab()
                    ->form(function ($record) {
                        $availableModels = $record->getAvailableAudioModels();

                        // If only one model, no form needed (will use URL)
                        if (count($availableModels) <= 1) {
                            return [];
                        }

                        // Multiple models - show selection
                        $options = [];
                        foreach ($availableModels as $model) {
                            $options[$model] = ucfirst($model) . ' Voice';
                        }

                        return [
                            Forms\Components\Select::make('selected_model')
                                ->label('Select Voice Model')
                                ->options($options)
                                ->required()
                                ->default($availableModels[0])
                                ->helperText('Choose which voice model MP3 to download.')
                        ];
                    })
                    ->action(function ($record, array $data) {
                        $availableModels = $record->getAvailableAudioModels();

                        if (empty($availableModels)) {
                            \Filament\Notifications\Notification::make()
                                ->title('No Audio Available')
                                ->body('This chapter does not have any audio files.')
                                ->warning()
                                ->send();
                            return;
                        }

                        // This action only runs for multiple models
                        $model = $data['selected_model'] ?? $availableModels[0];

                        $url = route('download.chapter.audio.single', [
                            'chapter' => $record->id,
                            'model' => $model
                        ]);
                        //redirect on new tab
                        return redirect($url);
                    })
                    ->modalHeading('Download Chapter MP3')
                    ->modalSubmitActionLabel('Download MP3')
                    ->color('green')
                    ->visible(function ($record) {
                        return $record->hasAudio();
                    }),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
