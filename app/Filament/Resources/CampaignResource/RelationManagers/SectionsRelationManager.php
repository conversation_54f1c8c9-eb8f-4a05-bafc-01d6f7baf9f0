<?php

namespace App\Filament\Resources\CampaignResource\RelationManagers;

use App\Enum\CampaignStatusEnum;
use App\Models\Chapter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SectionsRelationManager extends RelationManager
{
    protected static string $relationship = 'sections';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make("chapter_id")
                                       ->required()
                                       ->columnSpan("full")
                                       ->options(function ($livewire) {
                                            return $livewire->ownerRecord->chapters->pluck('title', 'id')->map(function ($title) {
                                                return strip_tags($title);
                                        });
                        }),
                Forms\Components\RichEditor::make('title')
                                           ->fileAttachmentsDisk('s3')
                                           ->fileAttachmentsDirectory("campaign/".date('Y/m/d'))
                                           ->label("Section title")
                                           ->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
                                           ->required()->columnSpan("full"),

                Forms\Components\RichEditor::make('intro')
                                           ->label("Section intro")
                                           ->fileAttachmentsDirectory('campaign-image/'.date('Y-m-d'))
                                           ->fileAttachmentsDisk(app()->environment('local') ? 'public' : 's3')
                                           ->required()->columnSpan("full"),
                Forms\Components\RichEditor::make('body')
                                           ->fileAttachmentsDisk('s3')
                                           ->fileAttachmentsDirectory("campaign/".date('Y/m/d'))
                                           ->label("Section body")
                                           ->fileAttachmentsDirectory('campaign-image/'.date('Y-m-d'))
                                           ->fileAttachmentsDisk(app()->environment('local') ? 'public' : 's3')
                                           ->required()->columnSpan("full"),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('plain_title')
            ->columns([
                Tables\Columns\TextColumn::make('title')->html(),
                Tables\Columns\TextColumn::make('status')
                                         ->badge()
                                         ->sortable()
                                         ->color(fn($state): string => $state->getColor())
                                         ->formatStateUsing(fn(CampaignStatusEnum $state): string => ucfirst($state->value)),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->mutateFormDataUsing(function($data){
                    $data['status'] = CampaignStatusEnum::DONE;
                    $data["order"] = Chapter::find($data["chapter_id"])->sections->count() + 1;
                    $data['section_total_words'] = str_word_count(strip_tags($data['body']));
                    return $data;
                }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('chapter_id', 'asc');
    }

    /**
     * @param  string|null  $title
     */
    public static function set_title(?string $title): void
    {
        self::$title = strip_tags($title);
    }
}
