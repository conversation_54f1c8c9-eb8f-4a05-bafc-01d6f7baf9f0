<?php

namespace App\Filament\Resources\CampaignResource\RelationManagers;

use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use App\Models\Campaign;
use App\Models\Download;
use App\Traits\StorageHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class DownloadsRelationManager extends RelationManager
{
    use StorageHelper;

    protected static string $relationship = 'downloads';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
//                Forms\Components\TextInput::make('path')
//                    ->required()
//                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('path')
            ->columns([
                TextColumn::make('campaign.topic')
                          ->label("File version")
                          ->formatStateUsing(function ($state, $record) {
                                $state = strlen($state) > 50 ? substr($state, 0, 50) . '...' : $state;
                                return str_replace(" ", "_", $state) . "_v" . $record->file_version;
                          }),
                TextColumn::make('type')
                          ->label("Type")->badge()
                          ->formatStateUsing(fn ($state) => strtoupper($state)),

                Tables\Columns\TextColumn::make('created_at')
                                         ->label('Created')
                                         ->searchable()
                                         ->sortable()
                                         ->alignCenter()
                                         ->formatStateUsing(fn(Download $record) => $record->created_at->diffForHumans()),
            ])->defaultSort("created_at", "desc")
            ->filters([
                //
            ])
            ->headerActions([
                //Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\Action::make('download_pdf')
                        ->label('Download PDF')
                        ->icon('heroicon-o-document-arrow-down')
                        ->visible(fn (Download $record) => $record->type === 'pdf' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return $this->getTemporaryUrl($record);
                        })
                        ->openUrlInNewTab(),
    
                    // EPUB Download Action
                    Tables\Actions\Action::make('download_epub')
                        ->label('Download EPUB')
                        ->icon('heroicon-o-book-open')
                        ->visible(fn (Download $record) => $record->type === 'epub' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return $this->getTemporaryUrl($record);
                        })
                        ->openUrlInNewTab(),
    
                    // Audio Download Action
                    Tables\Actions\Action::make('download_audio')
                        ->label('Download Audio Book')
                        ->icon('heroicon-o-musical-note')
                        ->visible(fn (Download $record) => $record->type === 'mp3' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return $this->getTemporaryUrl($record);
                        })
                        ->openUrlInNewTab(),
    
                    // Text Download Action
                    Tables\Actions\Action::make('download_text')
                        ->label('Download Text File')
                        ->icon('heroicon-o-document-text')
                        ->visible(fn (Download $record) => $record->type === 'txt' && isS3FileExist($record->path))
                        ->url(function (Download $record) {
                            return $this->getTemporaryUrl($record);
                        })
                        ->openUrlInNewTab(),
    
                    // Share Flipbook Action
                    Tables\Actions\Action::make('share_flipbook')
                        ->label("Share flipbook")
                        ->icon('heroicon-o-globe-americas')
                        ->form(function (Download $record) {
                            return PublicShareForm::getForm($record);
                        })
                        ->action(function (Download $record, array $data) {
                            PublicShareForm::action($record, $data);
                        })
                        ->visible(function (Download $record) {
                            return $record->type == 'pdf' && isCampaignTypeEnabled(FeatureEnum::LINK_SHARING->value);
                        })
                        ->disabled(fn() => ! auth()->user()->getUser()->hasLinkSharing()),
                        
                    Tables\Actions\Action::make('cover_image')
                            ->label("Download cover image")
                            ->icon('heroicon-o-arrow-down-tray')
                            ->action(function ($record) {
                                $url = self::getValidCoverImageUrl($record);

                                if (! $url) {
                                    Notification::make()
                                                ->title("Ebook cover file not found.")
                                                ->danger()
                                                ->send();

                                    return;
                                }

                                $path = self::parseStoragePathFromUrl($url);
                                return Storage::disk('s3')->download($path);
                            })
                            ->color('gray')
                            ->visible(fn($record) => self::getValidCoverImageUrl($record) !== null && in_array($record->type, ['pdf', 'epub'])),
                ])->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
