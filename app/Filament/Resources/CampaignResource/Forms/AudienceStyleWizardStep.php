<?php

namespace App\Filament\Resources\CampaignResource\Forms;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Wizard\Step;

class AudienceStyleWizardStep
{
    public static function make()
    {
        return Step::make('Audience & Style')
                          ->icon('heroicon-o-users')
                          ->schema([
                              Select::make('form.audience')
                                    ->label('Targeted Audience')->default("general_public")
                                    ->options([
                                        'students' => 'Students',
                                        'professionals' => 'Professionals',
                                        'general_public' => 'General Public',
                                        'beginners' => 'Beginners',
                                        'experts' => 'Experts',
                                        'teachers' => 'Teachers',
                                        'entrepreneurs' => 'Entrepreneurs',
                                        'hobbyists' => 'Hobbyists',
                                    ])
                                    ->placeholder('Select the audience for your ebook'),

                              Select::make('form.purpose')
                                    ->label('Purpose')->default("informative")
                                    ->options([
                                        'informative' => 'Informative',
                                        'educational' => 'Educational',
                                        'entertainment' => 'Entertainment',
                                        'marketing' => 'Marketing',
                                        'inspirational' => 'Inspirational',
                                        'technical' => 'Technical',
                                        'reference' => 'Reference',
                                        'research' => 'Research',
                                    ])
                                    ->placeholder('Select the purpose of your ebook'),

                              Select::make('form.tone')
                                    ->label('Tone')->default('professional')
                                    ->options([
                                        'formal' => 'Formal',
                                        'casual' => 'Casual',
                                        'technical' => 'Technical',
                                        'conversational' => 'Conversational',
                                        'professional' => 'Professional',
                                        'humorous' => 'Humorous',
                                        'inspirational' => 'Inspirational',
                                        'neutral' => 'Neutral',
                                    ])
                                    ->placeholder('Choose the tone of your ebook'),

                              Select::make('form.language')
                                    ->label('Language')
                                    ->default("English")
                                      ->options(function () {
                                          $json = file_get_contents(base_path('resources/json/languages.json'));
                                          $languages = json_decode($json, true);

                                          return collect($languages)->mapWithKeys(function ($language, $key) {
                                              $label = ($language === $key) ? $language : "$language - $key";
                                              return [$key => $label];
                                          })->toArray();
                                      })

                                  ->searchable()
                                    ->required()
                                    ->placeholder('Select the language for your ebook'),
                          ]);
    }
}
