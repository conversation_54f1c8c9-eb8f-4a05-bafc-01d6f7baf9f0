<?php

namespace App\Filament\Resources\CampaignResource\Forms;

use App\Enum\CampaignStatusEnum;
use App\Enum\FeatureEnum;
use App\Jobs\GenerateChapterWiseAudioJob;
use App\Models\Campaign;
use App\Service\Addons;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use App\Services\IntroOutroTemplateService;

class ConfigureChapterWiseAudioForm
{
    /**
     * Get the chapter-wise audio generation form fields
     */
    public static function getForm(Campaign $campaign): array
    {
        // Only show form if user has audio book generation capability
        if (!auth()->user()->getUser()->hasAudioBookGeneration() || !auth()->user()->getUser()->getOpenAiApiKey()) {
            return [];
        }

        if ($campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED && $campaign->getMeta("voice_model")) {
            return [];
        }

        if (!self::canProcessAudio($campaign)) {
            return [];
        }

        return [
            Select::make('voice_model')
                ->label('Select Voice Model')
                ->options([
                    'alloy' => 'Alloy',
                    'ash' => 'Ash',
                    'coral' => 'Coral',
                    'echo' => 'Echo',
                    'fable' => 'Fable',
                    'onyx' => 'Onyx',
                    'nova' => 'Nova',
                    'sage' => 'Sage',
                    'shimmer' => 'Shimmer',
                ])
                ->default($campaign->getMeta('chapter_wise_voice_model', 'alloy'))
                ->reactive(),

            ViewField::make('audio_player')
                ->view('components.audio-player')
                ->statePath('voice_model')
                ->reactive(),

            \Filament\Forms\Components\Checkbox::make('include_chapter_intro')
                ->label('Include Chapter Intro')
                ->helperText('Add polished opening voiceover to each chapter (e.g., "Welcome to Chapter 1...")')
                ->default($campaign->getMeta('include_chapter_intro', false))
                ->reactive(),

            Select::make('intro_template_selection')
                ->label('Intro Template Selection')
                ->options(function () use ($campaign) {
                    $language = $campaign->getForm('language') ?: 'English';
                    return IntroOutroTemplateService::getTemplateOptionsForLanguage($language);
                })
                ->default($campaign->getMeta('intro_template_selection', 'random'))
                ->helperText('Choose "Random" for variety or select a specific template for all chapters')
                ->visible(fn ($get) => $get('include_chapter_intro')),

            Placeholder::make('')
                ->content(new \Illuminate\Support\HtmlString(self::getHelpText())),
        ];
    }

    /**
     * Handle the chapter-wise audio generation action
     */
    public static function action(Campaign $campaign, array $data): void
    {
        // Check if user has audio book generation capability
        if (!auth()->user()->getUser()->hasAudioBookGeneration()) {
            Notification::make()
                ->title('Upgrade Required')
                ->body('You need to upgrade your plan to access audio book generation. Please visit our sales page to purchase a plan that includes audio book generation.')
                ->danger()
                ->send();
            redirect()->away(Addons::getUrl(['addon' => 'audio-generation']));
            return;
        }

        // Check if user has OpenAI API key
        if (!auth()->user()->getUser()->getOpenAiApiKey()) {
            Notification::make()
                ->title('OpenAI API Key Required')
                ->body('You need to add your OpenAI API key to generate audio books. Please add your API key in your profile settings.')
                ->danger()
                ->send();
            return;
        }

        // Check if campaign can be processed
        if (!self::canProcessAudio($campaign)) {
            Notification::make()
                ->title('Cannot Process Audio')
                ->body('This campaign cannot be processed for audio generation at this time.')
                ->danger()
                ->send();
            return;
        }

        // Save voice model and intro configuration
        $campaign->saveMeta("chapter_wise_voice_model", $data['voice_model']);
        $campaign->saveMeta("include_chapter_intro", $data['include_chapter_intro'] ?? false);
        $campaign->saveMeta("intro_template_selection", $data['intro_template_selection'] ?? 'random');
        GenerateChapterWiseAudioJob::dispatch($campaign, $data['voice_model'])->onQueue($campaign->getChapterAudioQueueName());

        // Show success notification
        Notification::make()
            ->title("We're processing your chapter-wise audiobook with {$data['voice_model']}. Please wait.")
            ->success()
            ->send();
    }

    /**
     * Get visibility condition for CampaignResource table
     */
    public static function getVisibility(): \Closure
    {
        return fn($record) => isCampaignTypeEnabled(FeatureEnum::AUDIO_BOOK_GENERATION->value) && self::canProcessAudio($record);
    }

    /**
     * Get the label for the action button
     */
    public static function getLabel(Campaign $campaign): string
    {
        if ($campaign->status == CampaignStatusEnum::AUDIO_PROCESSING) {
            return 'Processing Audio...';
        }

        if ($campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED) {
            return 'Retry Generate Audio';
        }

        // Check if any chapters have audio (using correct meta key)
        $hasChapterAudio = $campaign->chapters()->whereNotNull('meta->audio_s3_path')->exists();

        if ($hasChapterAudio) {
            return 'Re-generate Audio Book';
        }

        return 'Generate Audio Book';
    }

    /**
     * Check if campaign can be processed for audio
     */
    private static function canProcessAudio(Campaign $campaign): bool
    {
        // Campaign must be completed or failed audio processing
        if (!in_array($campaign->status, [CampaignStatusEnum::DONE, CampaignStatusEnum::AUDIO_PROCESSING_FAILED])) {
            return false;
        }

        // Campaign must have chapters
        if ($campaign->chapters()->count() === 0) {
            return false;
        }

        // User must have audio book generation capability
        if (!auth()->user()->getUser()->hasAudioBookGeneration()) {
            return false;
        }

        // User must have OpenAI API key
        if (!auth()->user()->getUser()->getOpenAiApiKey()) {
            return false;
        }

        return true;
    }

    /**
     * Get help text for the form
     */
    private static function getHelpText(): string
    {
        return '
            <div class="text-sm text-gray-600 dark:text-gray-400">
                <h4 class="font-semibold mb-2">Chapter-wise Audio Generation</h4>
                <ul class="list-disc list-inside space-y-1">
                    <li>Each chapter will be generated as a separate MP3 file</li>
                    <li>You can download individual chapter audio files from the chapter management section</li>
                    <li>This process may take several minutes depending on the number and size of chapters</li>
                </ul>
            </div>
        ';
    }
}
