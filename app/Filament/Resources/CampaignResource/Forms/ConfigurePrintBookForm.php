<?php

namespace App\Filament\Resources\CampaignResource\Forms;

use App\Models\Campaign;
use App\Services\LuluPrintSpecService;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Placeholder;
use Filament\Notifications\Notification;

class ConfigurePrintBookForm
{
    public static function getForm(?Campaign $record = null): array
    {
        return [
            Select::make('book_size')
                ->label('Book Size')
                ->options(function () {
                    return LuluPrintSpecService::getAvailableBookSizes();
                })
                ->required()
                ->reactive()
                ->default(function () use ($record) {
                    if ($record) {
                        $luluConfig = $record->getMeta('lulu_print');
                        return $luluConfig['book_size'] ?? null;
                    }
                    return null;
                })
                ->afterStateUpdated(function ($state, callable $set) {
                    // Reset dependent fields when book size changes
                    $set('paper_type', null);
                    $set('binding_type', null);
                    $set('interior_color', null);
                    $set('print_quality', null);
                    $set('cover_finish', null);
                }),

            Select::make('paper_type')
                ->label('Paper Type')
                ->options(function (callable $get) {
                    $bookSize = $get('book_size');
                    if (!$bookSize) {
                        return [];
                    }
                    return LuluPrintSpecService::getAvailablePaperTypes($bookSize);
                })
                ->required()
                ->reactive()
                ->default(function () use ($record) {
                    if ($record) {
                        $luluConfig = $record->getMeta('lulu_print');
                        return $luluConfig['paper_type'] ?? null;
                    }
                    return null;
                })
                ->afterStateUpdated(function ($state, callable $set) {
                    // Reset dependent fields when paper type changes
                    $set('binding_type', null);
                    $set('interior_color', null);
                    $set('print_quality', null);
                    $set('cover_finish', null);
                }),

            Select::make('binding_type')
                ->label('Binding Type')
                ->options(function (callable $get) {
                    $bookSize = $get('book_size');
                    $paperType = $get('paper_type');
                    if (!$bookSize) {
                        return [];
                    }
                    return LuluPrintSpecService::getAvailableBindingTypes($bookSize, $paperType);
                })
                ->required()
                ->reactive()
                ->default(function () use ($record) {
                    if ($record) {
                        $luluConfig = $record->getMeta('lulu_print');
                        return $luluConfig['binding_type'] ?? null;
                    }
                    return null;
                })
                ->afterStateUpdated(function ($state, callable $set) {
                    // Reset dependent fields when binding type changes
                    $set('interior_color', null);
                    $set('print_quality', null);
                    $set('cover_finish', null);
                }),

            Select::make('interior_color')
                ->label('Interior Color')
                ->options(function (callable $get) {
                    $bookSize = $get('book_size');
                    $paperType = $get('paper_type');
                    $bindingType = $get('binding_type');
                    if (!$bookSize) {
                        return [];
                    }
                    return LuluPrintSpecService::getAvailableInteriorColors($bookSize, $paperType, $bindingType);
                })
                ->required()
                ->reactive()
                ->default(function () use ($record) {
                    if ($record) {
                        $luluConfig = $record->getMeta('lulu_print');
                        return $luluConfig['interior_color'] ?? null;
                    }
                    return null;
                })
                ->afterStateUpdated(function ($state, callable $set) {
                    // Reset dependent fields when interior color changes
                    $set('print_quality', null);
                    $set('cover_finish', null);
                }),

            Select::make('print_quality')
                ->label('Print Quality')
                ->options(function (callable $get) {
                    $bookSize = $get('book_size');
                    $paperType = $get('paper_type');
                    $bindingType = $get('binding_type');
                    $interiorColor = $get('interior_color');
                    if (!$bookSize) {
                        return [];
                    }
                    return LuluPrintSpecService::getAvailablePrintQualities($bookSize, $paperType, $bindingType, $interiorColor);
                })
                ->required()
                ->reactive()
                ->default(function () use ($record) {
                    if ($record) {
                        $luluConfig = $record->getMeta('lulu_print');
                        return $luluConfig['print_quality'] ?? null;
                    }
                    return null;
                })
                ->afterStateUpdated(function ($state, callable $set) {
                    // Reset cover finish when print quality changes
                    $set('cover_finish', null);
                }),

            Select::make('cover_finish')
                ->label('Cover Finish')
                ->options(function (callable $get) {
                    $bookSize = $get('book_size');
                    $paperType = $get('paper_type');
                    $bindingType = $get('binding_type');
                    $interiorColor = $get('interior_color');
                    $printQuality = $get('print_quality');
                    if (!$bookSize) {
                        return [];
                    }
                    return LuluPrintSpecService::getAvailableCoverFinishes($bookSize, $paperType, $bindingType, $interiorColor, $printQuality);
                })
                ->required()
                ->default(function () use ($record) {
                    if ($record) {
                        $luluConfig = $record->getMeta('lulu_print');
                        return $luluConfig['cover_finish'] ?? null;
                    }
                    return null;
                }),

            Placeholder::make('validation_status')
                ->label('Configuration Status')
                ->content(function (callable $get) {
                    $bookSize = $get('book_size');
                    $paperType = $get('paper_type');
                    $bindingType = $get('binding_type');
                    $interiorColor = $get('interior_color');
                    $printQuality = $get('print_quality');
                    $coverFinish = $get('cover_finish');

                    if (!$bookSize || !$paperType || !$bindingType || !$interiorColor || !$printQuality || !$coverFinish) {
                        return '⚠️ Please complete all fields to validate configuration';
                    }

                    $podPackageId = $bookSize . $interiorColor . $printQuality . $bindingType . $paperType . $coverFinish . 'XX';

                    if (LuluPrintSpecService::isValidPodPackageId($podPackageId)) {
                        $spec = LuluPrintSpecService::getSpecDetails($podPackageId);
                        $minPages = $spec['Min Page'] ?? 'N/A';
                        $maxPages = $spec['Max Page'] ?? 'N/A';
                        $basePrice = $spec['Base Price-USD'] ?? 'N/A';

                        return "✅ Valid Configuration<br>POD Package ID: <strong>{$podPackageId}</strong><br>Page Range: {$minPages}-{$maxPages}<br>Base Price: \${$basePrice} USD";
                    } else {
                        return "❌ Invalid Configuration<br>POD Package ID: <strong>{$podPackageId}</strong><br>This combination is not available in Lulu Print API";
                    }
                })
                ->reactive(),
        ];
    }

    public static function action(Campaign $record, array $data): void
    {
        // Validate that all required fields are present
        $requiredFields = ['book_size', 'paper_type', 'binding_type', 'print_quality', 'interior_color', 'cover_finish'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                Notification::make()
                    ->title('Validation Error')
                    ->body("Missing required field: {$field}")
                    ->danger()
                    ->send();
                return;
            }
        }

        // Generate POD Package ID
        $podPackageId = self::generatePodPackageId($data);

        // Validate against Lulu Print API specifications
        if (!LuluPrintSpecService::isValidPodPackageId($podPackageId)) {
            Notification::make()
                ->title('Invalid Configuration')
                ->body("The selected combination is not available in Lulu Print API. POD Package ID: {$podPackageId}")
                ->danger()
                ->send();
            return;
        }

        // Get specification details for additional information
        $spec = LuluPrintSpecService::getSpecDetails($podPackageId);
        $minPages = $spec['Min Page'] ?? 'N/A';
        $maxPages = $spec['Max Page'] ?? 'N/A';
        $basePrice = $spec['Base Price-USD'] ?? 'N/A';

        // Store the configuration
        $data['pod_package_id'] = $podPackageId;
        $data['spec_details'] = $spec;
        $record->saveMeta('lulu_print', $data);

        Notification::make()
            ->title('Print book configuration saved successfully!')
            ->body("POD Package ID: {$podPackageId}<br>Page Range: {$minPages}-{$maxPages}<br>Base Price: \${$basePrice} USD")
            ->success()
            ->send();
    }

    /**
     * Generate POD Package ID according to Lulu Print API specifications
     * Format: Trim Size + Color + Print Quality + Bind + Paper + Finish + Linen + Foil
     */
    private static function generatePodPackageId(array $data): string
    {
        return ($data['book_size'] ?? '') .
            ($data['interior_color'] ?? '') .
            ($data['print_quality'] ?? '') .
            ($data['binding_type'] ?? '') .
            ($data['paper_type'] ?? '') .
            ($data['cover_finish'] ?? '') .
            'X' . // Linen (X = no linen)
            'X';  // Foil (X = no foil)
    }
}