<?php

namespace App\Filament\Resources\CampaignResource\Forms;

use App\Enum\CampaignStatusEnum;
use App\Enum\FeatureEnum;
use App\Jobs\GenerateAudioBookJob;
use App\Models\Campaign;
use App\Filament\Pages\Addons;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;

class ConfigureAudioBookForm
{
    /**
     * Get the audio book generation form fields
     */
    public static function getForm(Campaign $campaign): array
    {
        // Only show form if user has audio book generation capability
        if (!auth()->user()->getUser()->hasAudioBookGeneration() || !auth()->user()->getUser()->getOpenAiApiKey()) {
            return [];
        }

        if($campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED && $campaign->getMeta("voice_model")){
            return [];
        }

        if (!self::canProcessAudio($campaign)) {
            return [];
        }

        return [
            Select::make('voice_model')
                ->label('Select Voice Model')
                ->options([
                    'alloy' => 'Alloy',
                    'ash' => 'Ash',
                    'coral' => 'Coral',
                    'echo' => 'Echo',
                    'fable' => 'Fable',
                    'onyx' => 'Onyx',
                    'nova' => 'Nova',
                    'sage' => 'Sage',
                    'shimmer' => 'Shimmer',
                ])
                ->default('alloy')
                ->reactive(),

            ViewField::make('audion_player')
                ->view('components.audio-player')
                ->statePath('voice_model')
                ->reactive(),

            Placeholder::make('')
                ->content(function () {
                    return ConfigureAudioBookForm::getHelpText();
                }),
        ];
    }

    /**
     * Handle the audio book generation action
     */
    public static function action(Campaign $campaign, array $data): void
    {
        // Check if user has audio book generation capability
        if (!auth()->user()->getUser()->hasAudioBookGeneration()) {
            Notification::make()
                ->title('Upgrade Required')
                ->body('You need to upgrade your plan to access audio book generation. Please visit our sales page to purchase a plan that includes audio book generation.')
                ->danger()
                ->send();

            // Redirect based on user type
            $legacyService = new \App\Services\LegacyUserService();
            if ($legacyService->isLegacyUser(auth()->user())) {
                redirect()->away(Addons::getUrl(['addon' => 'audio-generation']));
            } else {
                redirect()->away(\App\Filament\Pages\Pricing::getUrl());
            }
            return;
        }

        // Check if user has OpenAI API key
        if (!auth()->user()->getUser()->getOpenAiApiKey()) {
            Notification::make()
                ->title('OpenAI API Key Required')
                ->body('You need to set your OpenAI API key before you can generate an audio book. Please visit your settings page to add your API key.')
                ->danger()
                ->send();
            redirect()->away('/settings');
            return;
        }

        if (!self::canProcessAudio($campaign)) {
            Notification::make()
                ->title('Campaign is in '.$campaign->status->getLabel().' status')
                ->body('Please wait for the campaign to complete before generating an audio book.')
                ->danger()
                ->send();
            return;
        }

        if($campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED && $campaign->getMeta("voice_model")){
            $data['voice_model'] = $campaign->getMeta("voice_model");
        }

        // Save voice model and dispatch job
        $campaign->saveMeta("voice_model", $data['voice_model']);
        GenerateAudioBookJob::dispatch($campaign)->onQueue($campaign->getAudioBookQueueName());

        // Show success notification
        Notification::make()
            ->title("We're processing your audiobook with {$data['voice_model']}. Please wait.")
            ->success()
            ->send();
    }

    /**
     * Get visibility condition for CampaignResource table
     */
    public static function getVisibility(): \Closure
    {
        return fn($record) => isCampaignTypeEnabled(FeatureEnum::AUDIO_BOOK_GENERATION->value) && self::canProcessAudio($record);
    }
    /**
     * Get visibility condition for CampaignResource table
     */
    public static function getHelpText  (): HtmlString
    {
        return new HtmlString("<span class='text-danger-600'>100 Credits will be deducted for each audio generation. </span>");
    }

    /**
     * Get dynamic label based on campaign status
     */
    public static function getLabel(Campaign $record): string
    {
        return $record->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED
            ? "Regenerate Audio Book"
            : "Generate Audio Book";
    }

    /**
     * @param  Campaign  $campaign
     * @return bool
     */
    private static function canProcessAudio(Campaign $campaign): bool
    {
        return in_array($campaign->status, [CampaignStatusEnum::DONE, CampaignStatusEnum::AUDIO_PROCESSING_FAILED]);
    }
}
