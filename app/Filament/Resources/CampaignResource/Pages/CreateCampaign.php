<?php

namespace App\Filament\Resources\CampaignResource\Pages;

use App\Enum\CampaignStatusEnum;
use App\Filament\Resources\CampaignResource;
use App\Http\Middleware\CheckRemainCredit;
use App\Models\BackgroundImage;
use App\Models\Campaign;
use App\Models\EbookFormat;
use App\Models\Template;
use App\Services\LegacyUserService;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class CreateCampaign extends CreateRecord
{
    protected static string $resource = CampaignResource::class;
    protected static string|array $routeMiddleware = [CheckRemainCredit::class];

    public function setDesign($id): void
    {
        $template = Template::find($id);

        $currentData = $this->form->getState();
        $currentData['ebookFormat']['font'] = $template["font"];
        $currentData['ebookFormat']['font_size'] = $template["font_size"];
        $currentData['ebookFormat']['heading'] = $template["heading"];
        $currentData['ebookFormat']['sub_heading'] = $template["sub_heading"];
        $currentData['ebookFormat']['line_space'] = $template["line_space"];
        $currentData['ebookFormat']['paragraph_space'] = $template["paragraph_space"];
        $currentData['ebookFormat']['page_size'] = $template["page_size"];
        $currentData['ebookFormat']['margins'] = $template["margins"];
        $currentData['ebookFormat']['text_align'] = $template["text_align"];
        $currentData['ebookFormat']['background_image'] = $template->image ?? "";
        $currentData['ebookFormat']['background_opacity'] = $template["background_opacity"] ?? 1;
        $currentData['ebookFormat']['margin_top'] = $template["margin_top"] ?? "";
        $currentData['ebookFormat']['margin_bottom'] = $template["margin_bottom"] ?? "";
        $currentData['ebookFormat']['margin_left'] = $template["margin_left"] ?? "";
        $currentData['ebookFormat']['margin_right'] = $template["margin_right"] ?? "";
        $this->form->fill($currentData);

    }
    // Method to set the background image with opacity
    public function setBackgroundImage($src, $opacity = 1.0): void
    {
        $backgroundImage = BackgroundImage::where('src', $src)->first();
        $backgroundOpacity = $backgroundImage->opacity ?? 1.0;

        // Get current form state and update only specific fields
        $currentData = $this->form->getRawState();
        $currentData['ebookFormat']['background_image'] = $src;
        $currentData['ebookFormat']['background_opacity'] = $backgroundOpacity;

        // Use the data property to update without triggering form reset
        $this->data = $currentData;
    }

    /**
     * @throws \Exception
     */
    protected function getFormActions(): array
    {
        return [
            Action::make('save_as_draft')
                ->label('Save as Draft')
                ->color('gray')
                ->action('saveAsDraft'),
            //            $this->getCreateFormAction()->label("Save & Run Campaign"),
            $this->getCancelFormAction()
        ];
    }


    public function saveAsDraft(): void
    {
        $data = $this->form->getState();
        $ebookFormatData = $this->data['ebookFormat'];
        $eBookFormat = EbookFormat::create($ebookFormatData);

        $data["ebook_format_id"] = $eBookFormat->id;
        $data["status"] = CampaignStatusEnum::DRAFT;

        unset($data['types']);
        unset($data['sample']);
        Campaign::create($data);

        Notification::make()->title('Campaign saved as draft.')->warning()->send();
        $this->redirect($this->getResource()::getUrl('index'));

        //return $campaignData;
    }

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $ebookFormatParams = ["font", "font_size", "heading", "sub_heading", "line_space", "paragraph_space", "page_size", "margins", "text_align"];
        //$ebookFormatData = Arr::only($data, $ebookFormatParams);
        $ebookFormatData = $this->data['ebookFormat'];
        if(!$ebookFormatData["background_opacity"]){
            $ebookFormatData["background_opacity"] = 1;
        }
        $eBookFormat = EbookFormat::create($ebookFormatData);
        $campaignData = Arr::except($data, $ebookFormatParams);
        $campaignData["ebook_format_id"] = $eBookFormat->id;

        unset($campaignData['types']);
        unset($campaignData['sample']);

        return $campaignData;
    }

    public function mount(): void
    {
        $user = auth()->user();

        // Check OpenAI API key
        if (!$user->getOpenAiApiKey()) {
            Notification::make()->title('You must set your OpenAI API key before you can create a campaign.')->danger()->send();
            redirect()->away('/settings');
            return;
        }

        // Check if non-legacy user has active subscription
        $legacyService = app(LegacyUserService::class);
        if (!$legacyService->isLegacyUser($user) && !$user->hasValidSubscription()) {
            Notification::make()
                ->title('Active Subscription Required')
                ->body('You need an active subscription to create campaigns. Please subscribe to a plan to continue.')
                ->danger()
                ->send();
            redirect()->to('/pricing');
            return;
        }

        parent::mount();
    }
}
