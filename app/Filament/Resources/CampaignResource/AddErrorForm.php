<?php

namespace App\Filament\Resources\CampaignResource;

use App\Models\AccountCampaign;
use App\Models\Campaign;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;

class AddErrorForm
{
    static function getForm(): array
    {
        $fields = [
            Textarea::make('message')->required(),
        ];

        return $fields;
    }

    /**
     * @param  Campaign | AccountCampaign
     * @param  array  $data
     */
    static function action(Campaign|AccountCampaign $record, array $data)
    {
        $record->saveMeta("error_message",$data["message"]);

        Notification::make()
            ->title('Error message added.')
            ->success()
            ->send();

        redirect()->away(url()->previous());
        return;
    }
}
