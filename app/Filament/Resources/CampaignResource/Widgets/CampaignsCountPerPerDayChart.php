<?php

namespace App\Filament\Resources\CampaignResource\Widgets;

use App\Models\Campaign;
use Filament\Widgets\BarChartWidget;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class CampaignsCountPerPerDayChart extends ChartWidget
{
    protected static ?string $pollingInterval = null;

    protected static ?string $heading = 'Chart';

    protected static ?int $sort = 2;

    public function getHeading(): string
    {
        return 'Campaigns';
    }

    protected function getData(): array
    {
        $query = Campaign::select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->where('created_at', '>', now()->subDays(30))
            ->groupBy('date')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Campaigns',
                    'data' => $query->pluck('count')->toArray(),
                    'backgroundColor' => random_colors()
                ],
            ],
            'labels' => $query->pluck('date')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
