<?php

namespace App\Filament\Resources\CampaignResource\Widgets;

use App\Filament\Resources\CampaignResource\Pages\ListCampaigns;
use App\Models\Campaign;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

class CampaignOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected static ?string $pollingInterval = null;

    public static function canView(): bool
    {
        return 1;
    }

    protected function getColumns(): int
    {
        return 3;
    }

    protected function getTablePage(): string
    {
        return ListCampaigns::class;
    }

    protected function getCards(): array
    {
        $campaignData = cache()->remember('stats:campaign-overview:'.auth()->id(), now()->addMinutes(15), function () {
            return Trend::query(Campaign::query()->when(!auth()->user()->isAdmin(), fn($q) => $q->where('user_id', auth()->id())))
                ->between(start: now()->subMonth(), end: now())
                ->perDay()
                ->count();
        });

        $campaignCount = $this->getPageTableQuery()->count();

        return [
            Stat::make('Campaigns', $campaignCount)->chart($campaignData->map(fn(TrendValue $value) => $value->aggregate)->toArray()),

            // Stat::make('Getting Started', null)
            //     ->url('https://aiwisemind.com/docs/quick-start-tutorial/')
            //     ->openUrlInNewTab()
            //     ->description('Click to read our quick start tutorial')
            //     ->icon('heroicon-o-light-bulb'),

                // Stat::make('MedPoster Guide', null)
                // ->url('https://aiwisemind.com/docs')
                // ->openUrlInNewTab()
                // ->description('Click to read our documentations')
                // ->icon('heroicon-o-book-open'),

        ];
    }
}
