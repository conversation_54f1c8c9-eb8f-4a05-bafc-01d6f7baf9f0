<?php

namespace App\Filament\Resources\CampaignResource\Widgets;

use App\Models\Campaign;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class CampaignCountPerStatusChart extends ChartWidget
{
    protected static ?string $pollingInterval = null;

    protected static ?string $heading = 'Chart';

    protected static ?int $sort = 2;

    public function getHeading(): string
    {
        return 'Campaign Status';
    }

    protected function getData(): array
    {
        $query = Campaign::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->orderBy('count', 'DESC')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Campaigns',
                    'data' => $query->pluck('count')->toArray(),
                    'backgroundColor' => random_colors()
                ],
            ],
            'labels' => $query->pluck('status')->map(fn($type) => title($type->value))->toArray(),
        ];
    }

    protected function getType(): string
    {
       return 'pie';
    }
}
