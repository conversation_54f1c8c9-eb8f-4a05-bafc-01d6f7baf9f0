<?php

namespace App\Filament\Pages;

use App\Service\ChangelogJsonService;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;

class ChangelogJsonManager extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-document-duplicate';

    protected static ?string $navigationLabel = 'JSON Changelog';

    protected static ?string $title = 'JSON Changelog Manager';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 112;

    protected static string $view = 'filament.pages.changelog-json-manager';

    public $jsonContent = '';
    public $updates = [];

    public function mount(): void
    {
        $this->loadJsonData();
    }

    public function loadJsonData(): void
    {
        $service = app(ChangelogJsonService::class);
        $this->jsonContent = $service->exportToJson();
        $this->updates = $service->getAllUpdates();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('sync_from_database')
                ->label('Sync from Database')
                ->icon('heroicon-o-arrow-path')
                ->color('info')
                ->requiresConfirmation()
                ->modalHeading('Sync from Database')
                ->modalDescription('This will replace the current JSON changelog with data from the database. Are you sure?')
                ->action(function () {
                    $service = app(ChangelogJsonService::class);
                    $service->syncFromDatabase();
                    $this->loadJsonData();

                    Notification::make()
                        ->title('Synced successfully!')
                        ->success()
                        ->send();
                }),

            Action::make('sync_to_database')
                ->label('Sync to Database')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Sync to Database')
                ->modalDescription('This will create/update database records from the JSON changelog. Existing records with the same version will be updated. Are you sure?')
                ->action(function () {
                    $service = app(ChangelogJsonService::class);
                    $service->syncToDatabase();

                    Notification::make()
                        ->title('JSON changes synced to database successfully!')
                        ->success()
                        ->send();
                }),

            Action::make('export_json')
                ->label('Export JSON')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () {
                    $service = app(ChangelogJsonService::class);
                    $json = $service->exportToJson();

                    return response()->streamDownload(function () use ($json) {
                        echo $json;
                    }, 'changelog-' . now()->format('Y-m-d') . '.json', [
                        'Content-Type' => 'application/json',
                    ]);
                }),

            Action::make('import_json')
                ->label('Import JSON')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('warning')
                ->form([
                    \Filament\Forms\Components\Textarea::make('json_data')
                        ->label('JSON Data')
                        ->required()
                        ->rows(10)
                        ->helperText('Paste your JSON changelog data here'),
                ])
                ->requiresConfirmation()
                ->modalHeading('Import JSON Changelog')
                ->modalDescription('This will replace the current changelog. Make sure to backup first!')
                ->action(function (array $data) {
                    try {
                        $service = app(ChangelogJsonService::class);
                        $service->importFromJson($data['json_data']);
                        $this->loadJsonData();

                        Notification::make()
                            ->title('Imported successfully!')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Import failed!')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }

    public static function canAccess(): bool
    {
        return auth()->user()?->isAdmin() ?? false;
    }
}
