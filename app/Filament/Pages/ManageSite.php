<?php

namespace App\Filament\Pages;

use App\Enum\CampaignTypeEnum;
use App\Enum\FeatureEnum;
use App\Enum\SiteColorsEnum;
use App\Enum\SiteFeaturesEnum;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Wallo\FilamentSelectify\Components\ToggleButton;

class ManageSite extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';
    protected static string $view = 'filament.pages.site-settings';
    protected static ?string $navigationGroup = 'Admin';
    protected static ?int $navigationSort = 16;

    /**
     * @var array<string, mixed> | null
     */
    public ?array $data = [];

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isSuperAdmin();
    }

    public function mount(): void
    {
        abort_unless(auth()->user()->isAdmin(), 403);

        $this->fillForm();
    }

    protected function fillForm(): void
    {
        $setting = SiteSetting::latest()
            ->get()
            ->pluck('payload', 'name')
            ->toArray();

        $this->form->fill($setting);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Site')
                    ->label('Site')
                    ->description('Manage basic settings here.')
                    ->schema([
                        Forms\Components\Grid::make()->schema([
                            Forms\Components\TextInput::make('name')
                                ->label('Site Name')
                                ->placeholder('Your Site Name')
                                ->required(),
//                                                Forms\Components\Textarea::make('documentations')
//                                                                         ->label('Documentations')
//                                                                         ->placeholder('icon|title|description|link')
//                                                                         ->helperText('Use | to separate icon, title, description, and link')
//                                                                         ->required(),
                        ])
                            ->columns(1),
                        
                        Forms\Components\Grid::make()->schema([
                            ToggleButton::make('has_top_navigation')
                                ->label('Top Navigation')
                                ->required()
                                ->onColor('success')
                                ->onLabel('Enabled')
                                ->offLabel('Disabled')
                                ->default(true),

                            ToggleButton::make('is_sidebar_collapse')
                                ->label('Sidebar Collapse')
                                ->required()
                                ->onColor('success')
                                ->onLabel('Enabled')
                                ->offLabel('Disabled')
                                ->default(true),
                        ])
                            ->columns(4),
                    ]),
            Forms\Components\Section::make('Features')
                ->schema([
                    Forms\Components\Grid::make()->schema(
                        [
                        Forms\Components\Grid::make()->schema(
                            $this->getFeatureTypeRepater(),
                        )->columns(3),
                            
                        ]
                    )->columns(4),
                ]),

                Forms\Components\Section::make('Theme Settings')
                    ->description('Manage theme settings here.')
                    ->headerActions([
                        Action::make('reset')
                            ->modalHeading('Are you sure?')
                            ->modalDescription('All existing items will be removed from the order.')
                            ->requiresConfirmation()
                            ->color('danger')
                            ->action(fn(Forms\Set $set) => $set('theme_colors', defaultThemeColors())),
                    ])
                    ->schema([
                        Forms\Components\Grid::make()
                            ->schema($this->getSelectColorSection())
                            ->columns(3),
                    ]),

            ])
            ->columns(3)
            ->statePath('data');
    }

    public function save()
    {
        try {
            DB::beginTransaction();
            $data = $this->form->getState();

            foreach ($data as $key => $value) {
                SiteSetting::updateOrCreate([
                    'name' => $key,
                ], [
                    'payload' => $value,
                ]);
            }

            Cache::forget('site-settings');
            Cache::forget('site-features');

            DB::commit();

            Notification::make()
                ->title('Settings updated.')
                ->success()
                ->send();

            return redirect(route('filament.app.pages.manage-site'));
        } catch (\Throwable $th) {
            Notification::make()
                ->title('Failed to update settings.')
                ->danger()
                ->send();
        }
    }


    protected function getSelectColorSection()
    {
        $colorLabels = [
            'primary' => 'Primary',
            'secondary' => 'Secondary',
            'gray' => 'Gray',
            'success' => 'Success',
            'danger' => 'Danger',
            'info' => 'Info',
            'warning' => 'Warning',
        ];

        $colorFields = [];

        foreach ($colorLabels as $key => $label) {
            $colorFields[] = Select::make("theme_colors.$key")
                ->label($label)
                ->required()
                ->allowHtml()
                ->native(false)
                ->searchable()
                ->options($this->showColorOptions());
        }

        return $colorFields;
    }

    protected function showColorOptions()
    {
        return collect(SiteColorsEnum::cases())
            ->mapWithKeys(static fn($case) => [
                $case->value => "<span class='flex items-center gap-x-4'>
                    <span class='rounded-full w-4 h-4' style='background:rgb(" . $case->getColor()[600] . ")'></span>
                    <span>" . $case->getLabel() . '</span>
                    </span>',
            ]);
    }

        protected function getFeatureTypeRepater()
    {
        $items = FeatureEnum::getOptions();

        foreach ($items as $key => $value) {
            $toggle[] = ToggleButton::make('campaign_types.' . $key)
                ->label($value)
                ->required()
                ->onColor('success')
                ->offColor('danger')
                ->onLabel('Enabled')
                ->offLabel('Disabled')
                ->default(true);
        };
        return $toggle;
    }
}
