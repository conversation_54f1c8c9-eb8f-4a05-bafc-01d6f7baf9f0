<?php

namespace App\Filament\Pages;

use App\Livewire\UpdatePasswordComponent;
use App\Livewire\UserProfileComponent;
use Filament\Pages\Page;
use App\Livewire\SubscriptionProfileComponent;
class MyProfile extends Page
{
    protected static string $view = 'filament.pages.my-profile';

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public function getRegisteredMyProfileComponents()
    {
        return [
            'subscription_info' => SubscriptionProfileComponent::class,
            'user_profile' => UserProfileComponent::class,
            'update_password' => UpdatePasswordComponent::class,
        ];
    }

}
