<?php

namespace App\Filament\Pages;

use App\Services\LegacyUserService;
use Filament\Pages\Page;

class Addons extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-puzzle-piece';

    protected static ?string $navigationLabel = 'Addons';

    protected static ?int $navigationSort = 5;

    protected static string $view = 'filament.pages.addons';

    protected static ?string $title = 'Available Addons';
    
    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || self::isLegacyUser();
    }
    
    public static function isLegacyUser(): bool
    {
        $legacyService = new LegacyUserService();
        return $legacyService->isLegacyUser(auth()->user());
    }

    public function getAddons(): array
    {
        $user = auth()->user();
        $selectedAddon = request()->query('addon');

        return [
            [
                'name' => 'Audio Generation',
                'description' => 'Convert your eBooks to high-quality audiobooks with AI-powered voice generation.',
                'icon' => 'heroicon-o-speaker-wave',
                'url' => 'https://ebookwriter.ai/audio-ebook/',
                'purchased' => $user->hasAudioBookGeneration(),
                'selected' => $selectedAddon === 'audio-generation',
                'features' => [
                    "Realistic AI voices powered by OpenAI",
                    "One-click ebook to audio conversion",
                    "No recording or editing required",
                    "Sell and bundle audio versions easily",
                ]
            ],
            [
                'name' => 'Research Tools',
                'description' => 'Advanced research capabilities to enhance your eBook content with reliable sources.',
                'icon' => 'heroicon-o-magnifying-glass',
                'url' => 'https://ebookwriter.ai/research-tools/',
                'purchased' => $user->hasResearchTool(),
                'selected' => $selectedAddon === 'research-tools',
                'features' => [
                    "AI-powered title generation",
                    "Bestseller market analysis",
                    "Keyword optimization for visibility",
                    "Automated author bio creation",
                ]
            ],
            [
                'name' => 'Flipbook & Lead Collection',
                'description' => 'Create interactive flipbooks and collect leads from your eBook readers.',
                'icon' => 'heroicon-o-book-open',
                'url' => 'https://ebookwriter.ai/flipbook-and-lead-collection/',
                'purchased' => $user->hasLeadCollection(),
                'selected' => $selectedAddon === 'flipbook-lead-collection',
                'features' => [
                    "Interactive flipbook creation",
                    "Automatic email collection from readers",
                    "Active affiliate links on every page",
                    "Export lead data from dashboard",
                ]
            ],
            [
                'name' => 'YouTube & URL to eBook',
                'description' => 'Convert YouTube videos and web content directly into comprehensive eBooks.',
                'icon' => 'heroicon-o-video-camera',
                'url' => 'https://ebookwriter.ai/youtube-and-url-conversion/',
                'purchased' => $user->hasVideoToEbook(),
                'selected' => $selectedAddon === 'youtube-url-conversion',
                'features' => [
                    "YouTube video to ebook conversion",
                    "Auto-expand content into chapters",
                    "Insert affiliate links and personal tips",
                    "Create marketable ebooks in minutes"
                ]
            ]
        ];
    }
}
