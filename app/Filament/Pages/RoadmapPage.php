<?php

namespace App\Filament\Pages;

use App\Models\RoadmapItem;
use App\Models\FeatureRequest;
use App\Models\RoadmapLike;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;

use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Log;

class RoadmapPage extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static ?string $navigationLabel = 'Roadmap';

    protected static ?string $title = 'Product Roadmap';

    protected static bool $shouldRegisterNavigation = false;

    protected static string $view = 'filament.pages.roadmap-page';

    public $roadmapItems = [];
    public $filters = [
        'search' => '',
        'status' => '',
        'category' => '',
        'priority' => '',
    ];

    public $requestForm = [
        'title' => '',
        'content' => '',
        'type' => '',
    ];

    public function mount(): void
    {
        $this->loadRoadmapItems();
    }

    public function getTitle(): string|Htmlable
    {
        return false;
    }

    public function loadRoadmapItems(): void
    {
        try {
            $query = RoadmapItem::published()->orderByRaw("
                CASE status
                    WHEN 'in_progress' THEN 1
                    WHEN 'planned' THEN 2
                    WHEN 'completed' THEN 3
                    WHEN 'cancelled' THEN 4
                END
            ")->orderBy('estimated_date', 'asc');

            // Apply filters
            if (!empty($this->filters['status'])) {
                $query->where('status', $this->filters['status']);
            }

            if (!empty($this->filters['category'])) {
                $query->where('category', $this->filters['category']);
            }

            if (!empty($this->filters['priority'])) {
                $query->where('priority', $this->filters['priority']);
            }

            if (!empty($this->filters['search'])) {
                $search = $this->filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('summary', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
                });
            }

            $this->roadmapItems = $query->with(['likes' => function ($query) {
                if (auth()->check()) {
                    $query->where('user_id', auth()->id());
                }
            }])->withCount('likes')->limit(50)->get();

        } catch (\Exception $e) {
            // Handle database connection issues gracefully
            $this->roadmapItems = collect([]);

            // Log the error for debugging
            Log::error('Failed to load roadmap items in RoadmapPage: ' . $e->getMessage());
        }
    }

    public function applyFilters(): void
    {
        $this->loadRoadmapItems();
    }

    public function clearFilters(): void
    {
        $this->filters = [
            'search' => '',
            'status' => '',
            'category' => '',
            'priority' => '',
        ];
        $this->loadRoadmapItems();
    }

    public function updatedFilters(): void
    {
        $this->loadRoadmapItems();
    }

    public function getStatusCounts(): array
    {
        try {
            return [
                'planned' => RoadmapItem::published()->byStatus('planned')->count(),
                'in_progress' => RoadmapItem::published()->byStatus('in_progress')->count(),
                'completed' => RoadmapItem::published()->byStatus('completed')->count(),
                'cancelled' => RoadmapItem::published()->byStatus('cancelled')->count(),
            ];
        } catch (\Exception $e) {
            return [
                'planned' => 0,
                'in_progress' => 0,
                'completed' => 0,
                'cancelled' => 0,
            ];
        }
    }

    public function getUpcomingCount(): int
    {
        try {
            return RoadmapItem::published()->upcoming()->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    public function getCompletedCount(): int
    {
        try {
            return RoadmapItem::published()->completed()->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(function () {
                    $this->loadRoadmapItems();
                }),
        ];
    }

    public function submitRequest(): void
    {
        // Validate the form data
        $this->validate([
            'requestForm.title' => 'required|string|max:255',
            'requestForm.content' => 'required|string|min:10',
            'requestForm.type' => 'required|in:feature,improvement,bug',
        ], [
            'requestForm.title.required' => 'Title is required.',
            'requestForm.title.max' => 'Title cannot exceed 255 characters.',
            'requestForm.content.required' => 'Description is required.',
            'requestForm.content.min' => 'Description must be at least 10 characters.',
            'requestForm.type.required' => 'Please select a type.',
            'requestForm.type.in' => 'Please select a valid type.',
        ]);

        try {
            FeatureRequest::create([
                'user_id' => auth()->id(),
                'title' => $this->requestForm['title'],
                'content' => $this->requestForm['content'],
                'type' => $this->requestForm['type'],
                'status' => 'pending',
            ]);

            // Reset the form
            $this->requestForm = [
                'title' => '',
                'content' => '',
                'type' => '',
            ];

            Notification::make()
                ->title('Request Submitted Successfully')
                ->body('Thank you for your request! Our team will review it and get back to you.')
                ->success()
                ->send();

            // Close the modal by dispatching an event
            $this->dispatch('close-modal');

        } catch (\Exception $e) {
            Log::error('Failed to create feature request: ' . $e->getMessage());

            Notification::make()
                ->title('Error')
                ->body('There was an error submitting your request. Please try again.')
                ->danger()
                ->send();
        }
    }

    public function toggleLike($roadmapItemId): void
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            Notification::make()
                ->title('Authentication Required')
                ->body('Please log in to like roadmap items.')
                ->warning()
                ->send();
            return;
        }

        try {
            // Verify the roadmap item exists and is published
            RoadmapItem::published()->findOrFail($roadmapItemId);
            $userId = auth()->id();

            // Check if user already liked this item
            $existingLike = RoadmapLike::where('user_id', $userId)
                ->where('roadmap_item_id', $roadmapItemId)
                ->first();

            if ($existingLike) {
                // Unlike - remove the like
                $existingLike->delete();
                $action = 'unliked';
            } else {
                // Like - create new like
                RoadmapLike::create([
                    'user_id' => $userId,
                    'roadmap_item_id' => $roadmapItemId,
                ]);
                $action = 'liked';
            }

            // Refresh the roadmap items to update like counts
            $this->loadRoadmapItems();

            // Show success notification
            Notification::make()
                ->title(ucfirst($action))
                ->body("You have {$action} this roadmap item.")
                ->success()
                ->duration(2000)
                ->send();

        } catch (\Exception $e) {
            Log::error('Failed to toggle like: ' . $e->getMessage());

            Notification::make()
                ->title('Error')
                ->body('There was an error processing your request. Please try again.')
                ->danger()
                ->send();
        }
    }
}
