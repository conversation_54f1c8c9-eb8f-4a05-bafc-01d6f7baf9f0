<?php

namespace App\Filament\Pages;

use App\Models\AppUpdate;
use App\Models\UserUpdatePreference;
use App\Service\UpdateNotificationService;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Log;

class ChangelogPage extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;
    protected static ?string $navigationIcon = 'heroicon-o-megaphone';

    protected static ?string $navigationLabel = 'What\'s New';

    protected static ?string $title = 'What\'s New';

    protected static bool $shouldRegisterNavigation = false;

    protected static string $view = 'filament.pages.changelog-page';

    public static function getRoutePath(): string
    {
        return '/changelog';
    }

    public $updates = [];
    public $unreadUpdates = [];
    public $filters = [
        'search' => '',
        'type' => '',
        'category' => '',
    ];

    // Preferences modal data
    public $email_major_updates = false;
    public $email_minor_updates = false;
    public $email_patch_updates = false;
    public $email_security_updates = false;
    public $categories = [];

    public function mount(): void
    {
        $this->loadUpdates();
        $this->loadUserPreferences();
        // Don't auto-mark as read on mount - let user see the updates first
        // $this->markAllAsRead();
    }

    public function getTitle(): string|Htmlable
    {
        return false;
    }

    public function loadUpdates(): void
    {
        try {
            $query = AppUpdate::published()->orderBy('released_at', 'desc');

            // Apply filters
            if (!empty($this->filters['type'])) {
                $query->where('type', $this->filters['type']);
            }

            if (!empty($this->filters['category'])) {
                $query->where('category', $this->filters['category']);
            }

            if (!empty($this->filters['search'])) {
                $search = $this->filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('summary', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
                });
            }

            $this->updates = $query->limit(20)->get();

            // Get unread updates for authenticated users
            if (auth()->check()) {
                $updateNotificationService = app(UpdateNotificationService::class);
                $this->unreadUpdates = $updateNotificationService
                    ->getUnreadUpdatesForUser(auth()->user())
                    ->pluck('id')
                    ->toArray();
            }
        } catch (\Exception $e) {
            // Handle database connection issues gracefully
            $this->updates = collect([]);
            $this->unreadUpdates = [];

            // Log the error for debugging
            Log::error('Failed to load updates in ChangelogPage: ' . $e->getMessage());
        }
    }

    public function applyFilters(): void
    {
        $this->loadUpdates();
    }

    public function clearFilters(): void
    {
        $this->filters = [
            'search' => '',
            'type' => '',
            'category' => '',
        ];
        $this->loadUpdates();
    }

    public function updatedFilters(): void
    {
        $this->loadUpdates();
    }

    public function markAsRead($updateId): void
    {
        try {
            if (auth()->check()) {
                $update = AppUpdate::find($updateId);
                if ($update) {
                    $updateNotificationService = app(UpdateNotificationService::class);
                    $updateNotificationService->markAsReadForUser($update, auth()->user());
                    $this->loadUpdates(); // Refresh the data

                    \Filament\Notifications\Notification::make()
                        ->title('Marked as read!')
                        ->success()
                        ->send();
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to mark update as read: ' . $e->getMessage());

            \Filament\Notifications\Notification::make()
                ->title('Failed to mark as read')
                ->danger()
                ->send();
        }
    }

    public function markAllAsRead(): void
    {
        try {
            if (auth()->check() && is_array($this->unreadUpdates) && count($this->unreadUpdates) > 0) {
                $updateNotificationService = app(UpdateNotificationService::class);

                foreach ($this->unreadUpdates as $updateId) {
                    $update = AppUpdate::find($updateId);
                    if ($update) {
                        $updateNotificationService->markAsReadForUser($update, auth()->user());
                    }
                }

                $this->loadUpdates(); // Refresh the data

                \Filament\Notifications\Notification::make()
                    ->title('All updates marked as read!')
                    ->success()
                    ->send();
            }
        } catch (\Exception $e) {
            Log::error('Failed to mark all updates as read: ' . $e->getMessage());

            \Filament\Notifications\Notification::make()
                ->title('Failed to mark all as read')
                ->danger()
                ->send();
        }
    }

    public function toggleReaction(int $updateId, string $reactionType): void
    {
        try {
            if (!auth()->check()) {
                return;
            }

            $update = AppUpdate::find($updateId);
            if (!$update) {
                return;
            }

            $user = auth()->user();

            // Check if user already reacted with this type
            $existingReaction = \App\Models\UpdateReaction::where('app_update_id', $updateId)
                ->where('user_id', $user->id)
                ->where('reaction_type', $reactionType)
                ->first();

            if ($existingReaction) {
                // Remove reaction
                $existingReaction->delete();
            } else {
                // Remove any other reaction from this user for this update
                \App\Models\UpdateReaction::where('app_update_id', $updateId)
                    ->where('user_id', $user->id)
                    ->delete();

                // Add new reaction
                \App\Models\UpdateReaction::create([
                    'app_update_id' => $updateId,
                    'user_id' => $user->id,
                    'reaction_type' => $reactionType,
                ]);
            }

            // Refresh the updates to show updated reaction counts
            $this->loadUpdates();
        } catch (\Exception $e) {
            Log::error('Failed to toggle reaction: ' . $e->getMessage());
        }
    }

    public function hasUserReacted(int $updateId, string $reactionType): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return \App\Models\UpdateReaction::where('app_update_id', $updateId)
            ->where('user_id', auth()->id())
            ->where('reaction_type', $reactionType)
            ->exists();
    }

    public function savePreferences(): void
    {
        try {
            if (!auth()->check()) {
                return;
            }

            $user = auth()->user();

            // Get or create user preferences
            $preferences = \App\Models\UserUpdatePreference::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'email_major_updates' => false,
                    'email_minor_updates' => false,
                    'email_patch_updates' => false,
                    'email_security_updates' => false,
                ]
            );

            // Update preferences
            $preferences->update([
                'email_major_updates' => $this->email_major_updates,
                'email_minor_updates' => $this->email_minor_updates,
                'email_patch_updates' => $this->email_patch_updates,
                'email_security_updates' => $this->email_security_updates,
            ]);

            // Close modal and show success message
            $this->dispatch('close-modal', id: 'preferences-modal');

            \Filament\Notifications\Notification::make()
                ->title('Preferences saved successfully!')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Log::error('Failed to save preferences: ' . $e->getMessage());

            \Filament\Notifications\Notification::make()
                ->title('Failed to save preferences')
                ->danger()
                ->send();
        }
    }



    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public function openPreferencesModal(): void
    {
        $this->loadUserPreferences();
        $this->dispatch('open-modal', id: 'preferences-modal');
    }

    protected function loadUserPreferences(): void
    {
        if (auth()->check()) {
            $preferences = UserUpdatePreference::getOrCreateForUser(auth()->user());

            $this->email_major_updates = $preferences->email_major_updates;
            $this->email_minor_updates = $preferences->email_minor_updates;
            $this->email_patch_updates = $preferences->email_patch_updates;
            $this->email_security_updates = $preferences->email_security_updates;
            $this->categories = $preferences->categories ?? [];
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('markAllRead')
                ->label('Mark All as Read')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->action(function () {
                    $this->markAllAsRead();
                    $this->loadUpdates(); // Refresh data
                })
                ->visible(function () {
                    return auth()->check() && count($this->unreadUpdates ?? []) > 0;
                }),

            Action::make('preferencesAction')
                ->label('Update Notification Preferences')
                ->icon('heroicon-o-cog-6-tooth')
                ->fillForm(function (): array {
                    if (auth()->check()) {
                        $preferences = UserUpdatePreference::getOrCreateForUser(auth()->user());
                        return [
                            'email_major_updates' => $preferences->email_major_updates,
                            'email_minor_updates' => $preferences->email_minor_updates,
                            'email_patch_updates' => $preferences->email_patch_updates,
                            'email_security_updates' => $preferences->email_security_updates,
                            'categories' => $preferences->categories ?? [],
                        ];
                    }
                    return [];
                })
                ->form([
                    Section::make('Update Types')
                        ->description('Choose which types of updates you\'d like to receive via email.')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Checkbox::make('email_major_updates')
                                        ->label('🎉 Major Updates')
                                        ->helperText('New features, major improvements, and significant changes'),

                                    Checkbox::make('email_minor_updates')
                                        ->label('✨ Minor Updates')
                                        ->helperText('Small features and enhancements'),

                                    Checkbox::make('email_patch_updates')
                                        ->label('🔧 Patch Updates')
                                        ->helperText('Bug fixes and minor improvements'),

                                    Checkbox::make('email_security_updates')
                                        ->label('🔒 Security Updates')
                                        ->helperText('Important security fixes and patches'),
                                ]),
                        ]),

                    Section::make('Categories')
                        ->description('Choose which categories of updates interest you most.')
                        ->schema([
                            CheckboxList::make('categories')
                                ->label('Update Categories')
                                ->options([
                                    'features' => '🚀 New Features',
                                    'fixes' => '🐛 Bug Fixes',
                                    'improvements' => '⚡ Improvements',
                                    'security' => '🔒 Security',
                                ])
                                ->helperText('Leave all unchecked to receive updates from all categories.')
                                ->columns(2),
                        ]),
                ])
                ->action(function (array $data) {
                    $this->savePreferencesFromAction($data);
                    $this->loadUserPreferences(); // Refresh the form data
                })
                ->modalWidth('2xl'),
        ];
    }

    protected function savePreferencesFromAction(array $data): void
    {
        try {
            if (auth()->check()) {
                $updateNotificationService = app(UpdateNotificationService::class);
                $updateNotificationService->updateUserPreferences(auth()->user(), [
                    'email_major_updates' => $data['email_major_updates'] ?? false,
                    'email_minor_updates' => $data['email_minor_updates'] ?? false,
                    'email_patch_updates' => $data['email_patch_updates'] ?? false,
                    'email_security_updates' => $data['email_security_updates'] ?? false,
                    'categories' => $data['categories'] ?? [],
                ]);

                Notification::make()
                    ->title('Preferences Updated!')
                    ->body('Your notification preferences have been saved successfully.')
                    ->success()
                    ->send();
            }
        } catch (\Exception $e) {
            Log::error('Failed to save user preferences: ' . $e->getMessage());

            Notification::make()
                ->title('Error')
                ->body('Failed to save preferences. Please try again.')
                ->danger()
                ->send();
        }
    }
}
