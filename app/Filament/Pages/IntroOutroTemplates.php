<?php

namespace App\Filament\Pages;

use App\Services\IntroOutroTemplateService;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\HtmlString;

class IntroOutroTemplates extends Page
{
    protected static ?int $navigationSort = 12;

    protected static ?string $navigationIcon = 'heroicon-o-microphone';

    protected static string $view = 'filament.pages.intro-outro-templates';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?string $title = 'Intro/Outro Templates';

    protected static ?string $navigationLabel = 'Audio Templates';

    public array $templates = [];

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin();
    }

    public function mount(): void
    {
        abort_unless(auth()->user()->isAdmin(), 403);

        $this->templates = IntroOutroTemplateService::getAllTemplates();

        // Transform templates for form
        $this->form->fill($this->mutateFormDataBeforeFill([]));
    }

    protected function getFormSchema(): array
    {
        $form = [];
        $availableLanguages = IntroOutroTemplateService::getAvailableLanguages();

        // Create tabs for each language
        $tabs = [];
        
        foreach ($availableLanguages as $langCode) {
            $languageName = $this->getLanguageName($langCode);
            
            $tabs[] = Tabs\Tab::make($languageName)
                ->schema([
                    Section::make('Intro Templates')
                        ->description('Templates for chapter introductions')
                        ->schema([
                            Repeater::make("templates.{$langCode}.intro")
                                ->label('Intro Templates')
                                ->schema([
                                    Forms\Components\Textarea::make('template')
                                        ->label('Template Text')
                                        ->required()
                                        ->rows(3)
                                        ->helperText(new HtmlString('
                                            <strong>Available placeholders:</strong><br>
                                            <code>{chapter_number}</code> - Chapter number<br>
                                            <code>{chapter_title}</code> - Chapter title<br>
                                            <code>{book_title}</code> - Book title<br>
                                            <code>{total_chapters}</code> - Total number of chapters
                                        '))
                                ])
                                ->defaultItems(3)
                                ->minItems(1)
                                ->maxItems(10)
                                ->addActionLabel('Add Intro Template')
                                ->collapsible()
                                ->itemLabel(fn (array $state): ?string => 
                                    isset($state['template']) 
                                        ? (strlen($state['template']) > 50 
                                            ? substr($state['template'], 0, 50) . '...' 
                                            : $state['template'])
                                        : 'New Template'
                                ),
                        ]),

                    Section::make('Outro Templates')
                        ->description('Templates for chapter conclusions')
                        ->schema([
                            Repeater::make("templates.{$langCode}.outro")
                                ->label('Outro Templates')
                                ->schema([
                                    Forms\Components\Textarea::make('template')
                                        ->label('Template Text')
                                        ->required()
                                        ->rows(3)
                                        ->helperText(new HtmlString('
                                            <strong>Available placeholders:</strong><br>
                                            <code>{chapter_number}</code> - Chapter number<br>
                                            <code>{chapter_title}</code> - Chapter title<br>
                                            <code>{book_title}</code> - Book title<br>
                                            <code>{next_chapter_number}</code> - Next chapter number<br>
                                            <code>{total_chapters}</code> - Total number of chapters
                                        '))
                                ])
                                ->defaultItems(3)
                                ->minItems(1)
                                ->maxItems(10)
                                ->addActionLabel('Add Outro Template')
                                ->collapsible()
                                ->itemLabel(fn (array $state): ?string => 
                                    isset($state['template']) 
                                        ? (strlen($state['template']) > 50 
                                            ? substr($state['template'], 0, 50) . '...' 
                                            : $state['template'])
                                        : 'New Template'
                                ),
                        ]),
                ]);
        }

        $form[] = Tabs::make('Language Templates')
            ->tabs($tabs)
            ->persistTabInQueryString();

        return $form;
    }

    public function submit(): void
    {
        // Transform the form data to match the expected JSON structure
        $transformedTemplates = [];
        
        foreach ($this->templates as $langCode => $langTemplates) {
            $transformedTemplates[$langCode] = [
                'intro' => array_map(fn($item) => $item['template'], $langTemplates['intro'] ?? []),
                'outro' => array_map(fn($item) => $item['template'], $langTemplates['outro'] ?? []),
            ];
        }

        // Create backup of current templates
        $currentTemplates = IntroOutroTemplateService::getAllTemplates();
        if (!empty($currentTemplates)) {
            $backupPath = base_path('resources/prompts/intro-outro-templates-backup-' . now()->format('Y-m-d-H-i-s') . '.json');
            file_put_contents($backupPath, json_encode($currentTemplates, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }

        // Update templates
        if (IntroOutroTemplateService::updateTemplates($transformedTemplates)) {
            Notification::make()
                ->title('Intro/Outro templates updated successfully!')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Failed to update templates')
                ->danger()
                ->send();
        }
    }

    private function getLanguageName(string $langCode): string
    {
        $languageNames = [
            'en' => 'English',
            'es' => 'Spanish',
            'fr' => 'French',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
            'zh' => 'Chinese',
            'ja' => 'Japanese',
            'ko' => 'Korean',
            'ar' => 'Arabic',
            'hi' => 'Hindi',
            'tr' => 'Turkish',
            'nl' => 'Dutch',
        ];

        return $languageNames[$langCode] ?? strtoupper($langCode);
    }

    protected function mutateFormDataBeforeFill(array $data = []): array
    {
        // Transform templates to match the form structure
        $transformedData = ['templates' => []];
        
        foreach ($this->templates as $langCode => $langTemplates) {
            $transformedData['templates'][$langCode] = [
                'intro' => array_map(fn($template) => ['template' => $template], $langTemplates['intro'] ?? []),
                'outro' => array_map(fn($template) => ['template' => $template], $langTemplates['outro'] ?? []),
            ];
        }

        return $transformedData;
    }
}
