<?php

namespace App\Filament\Pages;

use App\Filament\Resources\CampaignResource\Widgets\CampaignCountPerStatusChart;
use App\Filament\Resources\CampaignResource\Widgets\CampaignsCountPerPerDayChart;
use App\Filament\Resources\CampaignResource\Widgets\RechargePerPerDayChart;
use App\Filament\Resources\CampaignResource\Widgets\UsersCountPerPerDayChart;
use App\Filament\Widgets\AdminOverview;
use Filament\Pages\Page;
use Filament\Widgets\AccountWidget;

class Dashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament.pages.dashboard';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
    }

    protected function getHeaderWidgets(): array
    {
        $widgets = [
            AccountWidget::class,
        ];

        if (auth()->user()->isAdmin()) {
            $widgets[] = AdminOverview::class;
            $widgets[] = CampaignsCountPerPerDayChart::class;
            $widgets[] = UsersCountPerPerDayChart::class;
            $widgets[] = CampaignCountPerStatusChart::class;
        }

        return $widgets;
    }

    public function getHeaderWidgetsColumns(): int | string | array
    {
        return 2;
    }

    protected function getColumns(): int | string | array
    {
        return 2;
    }

    public function getTitle(): string
    {
        // return static::$title ?? __('filament::pages/dashboard.title');
        return static::$title ?? __('Dashboard');
    }
}
