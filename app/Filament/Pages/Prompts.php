<?php

namespace App\Filament\Pages;

use App\Models\Option;
use App\Service\PromptBuilder;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\HtmlString;

class Prompts extends Page
{
    protected static ?int $navigationSort = 11;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static string $view = 'filament.pages.prompts';

    protected static ?string $navigationGroup = 'Admin';

    public array $prompts = [];

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin();
    }

    public function mount(): void
    {
        abort_unless(auth()->user()->isAdmin(), 403);

        $this->prompts = (new PromptBuilder())->all();
    }

    protected function getFormSchema(): array
    {
        $form = [];

        foreach (__('prompts') as $key => $prompt) {
            $sectionSchema = [];
            // foreach ($sections as $key => $prompt) {

                $variables = (new PromptBuilder())->getVariables($prompt);

                $helpText = "<code> Variables: ";
                $helpText .= join(', ', $variables);
                $helpText .= "</code>";
                $sectionSchema[] = Forms\Components\Textarea::make('prompts.'  . $key)
                    ->label(title($key))
                    ->required(fn() => !in_array($key, ['meta_llama_3_system_message', 'openai_system_message']))
                    ->helperText(new HtmlString($helpText));
            // }

            $form[] = Fieldset::make(title($key) . ' Prompts')->schema($sectionSchema)->columns(count($sectionSchema) > 1 ? 2 : 1);
        }

        return $form;
    }

    public function submit(): void
    {
        // $this->validate((new PromptBuilder())->getValidationRules());

        if ($option = Option::where('key', 'prompts')->first()) {
            Option::updateOrCreate(['key' => str('prompts-backup-' . now())->slug()], ['value' => $option->value]);
        }

        Option::updateOrCreate(['key' => 'prompts'], ['value' => json_encode($this->prompts)]);

        Notification::make()->title('Prompts updated successfully!')->success()->send();
    }
}
