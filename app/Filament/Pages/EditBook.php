<?php

namespace App\Filament\Pages;

use App\Models\Campaign;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

class EditBook extends Page
{
    protected static string $view = 'filament.pages.edit-book';

    protected static bool $shouldRegisterNavigation = false;

    public Campaign $campaign;

    public function mount(Campaign $campaign): void
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            abort(403, 'Unauthorized access to this campaign.');
        }

        $this->campaign = $campaign;
    }
    public function getTitle(): string|Htmlable
    {
        return 'Edit Book: ' . ($this->campaign->title ?? $this->campaign->topic);
    }

    public function getHeading(): string|Htmlable
    {
        return '';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return $this->campaign->title ?? $this->campaign->topic;
    }

    public function getMaxContentWidth(): ?string
    {
        return 'full';
    }

    public function getViewData(): array
    {
        return [
            'campaign' => $this->campaign,
        ];
    }
}
