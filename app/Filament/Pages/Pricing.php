<?php

namespace App\Filament\Pages;

use App\Models\Plan;
use App\Services\LegacyUserService;
use Filament\Pages\Page;

class Pricing extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Pricing';
    
    protected static ?int $navigationSort = 6;

    protected static string $view = 'filament.pages.pricing';

    protected static ?string $title = '';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || !self::isLegacyUser();
    }

    public function isCurrentPlan(string $wplusCode): bool
    {
        $user = auth()->user();

        // Get the latest active subscription (most recent one)
        $latestActiveSubscription = $user->subscriptions()
            ->where('status', 'active')
            ->with('plan')
            ->orderBy('created_at', 'desc')
            ->first();

        // Only the latest active subscription should be marked as "Current Plan"
        if ($latestActiveSubscription && $latestActiveSubscription->plan) {
            return $latestActiveSubscription->plan->wplus_code === $wplusCode;
        }

        return false;
    }

    public static function isLegacyUser(): bool
    {
        $legacyService = new LegacyUserService();
        return $legacyService->isLegacyUser(auth()->user());
    }

    public function getPricingPlans(): array
    {
        // Fetch monthly plans from database
        $plans = Plan::where('type', 'monthly')
            ->where('show', 1)
            ->orderBy('amount')
            ->get();

        $formattedPlans = [];

        foreach ($plans as $plan) {
            $features = [];

            // Add credits info
            if ($plan->credits == 99999) {
                $features[] = 'Unlimited credits';
            } else {
                $features[] = number_format($plan->credits) . ' monthly credits';
            }

            // Add page limit info
            if ($plan->page_limit === null || $plan->is_unlimited) {
                $features[] = 'Unlimited pages';
            } else {
                $features[] = 'Up to ' . $plan->page_limit . ' pages per campaign';
            }

            // Add feature based on plan type
            if ($plan->includes_all_addons) {
                $features[] = 'All addons included';
                $features[] = 'Advanced eBook generation';
                $features[] = 'Multiple export formats';
                $features[] = 'Priority support';

                // Add premium features for higher tier plans
                if ($plan->amount >= 49) {
                    $features[] = 'Advanced customization';
                }

                if ($plan->amount >= 89) {
                    $features[] = 'Dedicated support';
                }
            } else {
                $features[] = 'Basic eBook generation';
                $features[] = 'PDF export';
                $features[] = 'Email support';
            }

            // Determine if this is the popular plan (Pro Plus)
            $isPopular = str_contains(strtolower($plan->name), 'pro') || $plan->amount == 49;

            // Generate description based on plan
            $description = $this->generatePlanDescription($plan);

            // Calculate savings for plans with addons included
            $savings = 0;
            if ($plan->includes_all_addons) {
                $savings = $this->calculateAddonSavings($plan->amount);
            }

            $formattedPlans[] = [
                'name' => $plan->name,
                'price' => '$' . number_format($plan->amount, 0),
                'period' => '/month',
                'description' => $description,
                'credits' => $plan->credits,
                'page_limit' => $plan->page_limit,
                'features' => $features,
                'addons_included' => $plan->includes_all_addons,
                'savings' => $savings,
                'url' => $plan->purchase_url,
                'wplus_code' => $plan->wplus_code,
                'popular' => $isPopular,
            ];
        }

        return $formattedPlans;
    }

    private function calculateAddonSavings(int $planAmount): int
    {
        // Individual addon prices based on business requirements
        $individualAddonPrices = [
            'flipbook_lead_collection' => 29,
            'audio_ebook_generator' => 19,
            'youtube_link_conversion' => 29,
            'research_tools_pack' => 19,
            'url_to_ebook_tool' => 19,
        ];

        $totalIndividualCost = array_sum($individualAddonPrices); // $115

        // Calculate savings: Total individual cost minus plan cost
        return max(0, $totalIndividualCost - $planAmount);
    }

    private function generatePlanDescription(Plan $plan): string
    {
        if (str_contains(strtolower($plan->name), 'Basic')) {
            return 'Perfect for getting started with eBook creation';
        } elseif (str_contains(strtolower($plan->name), 'premium')) {
            return 'Most popular plan with all addons included';
        } elseif (str_contains(strtolower($plan->name), 'pro')) {
            return 'For serious content creators and marketers';
        } elseif (str_contains(strtolower($plan->name), 'unlimited')) {
            return 'Unlimited everything for power users';
        }

        return $plan->description ?: 'Complete eBook creation solution';
    }
}
