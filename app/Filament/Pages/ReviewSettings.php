<?php

namespace App\Filament\Pages;

use App\Filament\Resources\CampaignResource\ReviewFormConfig;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Actions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Forms\Get;
use Filament\Forms\Components\Actions\Action;

class ReviewSettings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog';

    protected static ?string $navigationLabel = 'Review Settings';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 100;

    protected static string $view = 'filament.pages.review-settings';

    public ?array $reviewFormData = null;

    public function mount(): void
    {
        // Get the default review form configuration
        $this->reviewFormData = ReviewFormConfig::getDefaultConfig();

        // Fill the form with the configuration data
        $this->form->fill([
            'reviewFormData' => $this->reviewFormData,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Settings')
                    ->tabs([
                        Tab::make('Review Form Settings')
                            ->icon('heroicon-o-star')
                            ->schema([
                                Section::make('Default Review Form Configuration')
                                    ->description('Configure the default settings for all new eBook review forms')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                TextInput::make('reviewFormData.title')
                                                    ->label('Form Title')
                                                    ->required(),

                                                TextInput::make('reviewFormData.show_after_seconds')
                                                    ->label('Show After (seconds)')
                                                    ->numeric()
                                                    ->required(),

                                                Textarea::make('reviewFormData.subtitle')
                                                    ->label('Form Subtitle')
                                                    ->rows(2)
                                                    ->columnSpan(3),
                                            ]),

                                        Section::make('Form Fields')
                                            ->schema([
                                                Grid::make(2)
                                                    ->schema([
                                                        Toggle::make('reviewFormData.show_name_field')
                                                            ->label('Show Name Field')
                                                            ->reactive(),

                                                        Toggle::make('reviewFormData.name_field_required')
                                                            ->label('Name Field Required')
                                                            ->visible(fn ($get) => $get('reviewFormData.show_name_field')),

                                                        Toggle::make('reviewFormData.show_email_field')
                                                            ->label('Show Email Field')
                                                            ->reactive(),

                                                        Toggle::make('reviewFormData.email_field_required')
                                                            ->label('Email Field Required')
                                                            ->visible(fn ($get) => $get('reviewFormData.show_email_field')),

                                                        Toggle::make('reviewFormData.show_rating_field')
                                                            ->label('Show Rating Field')
                                                            ->reactive(),

                                                        Toggle::make('reviewFormData.rating_field_required')
                                                            ->label('Rating Field Required')
                                                            ->visible(fn ($get) => $get('reviewFormData.show_rating_field')),

                                                        Toggle::make('reviewFormData.show_comment_field')
                                                            ->label('Show Comment Field')
                                                            ->reactive(),

                                                        Toggle::make('reviewFormData.comment_field_required')
                                                            ->label('Comment Field Required')
                                                            ->visible(fn ($get) => $get('reviewFormData.show_comment_field')),
                                                    ]),
                                            ]),

                                        Section::make('Button & Messages')
                                            ->schema([
                                                Grid::make(2)
                                                    ->schema([
                                                        TextInput::make('reviewFormData.submit_button_text')
                                                            ->label('Submit Button Text')
                                                            ->required(),

                                                        TextInput::make('reviewFormData.close_button_text')
                                                            ->label('Close Button Text')
                                                            ->required(),

                                                        Textarea::make('reviewFormData.thank_you_message')
                                                            ->label('Thank You Message')
                                                            ->rows(2)
                                                            ->columnSpan(2),
                                                    ]),
                                            ]),
                                                       Actions::make([
                           Action::make('preview')
                                 ->label('Preview Format')
                                 ->icon('heroicon-o-eye')
                                 ->button()
                                 ->color('secondary')
                                 ->extraAttributes(['class' => 'preview-button'])
                                 ->action(function (Get $get, $livewire) {
                                     $formatData = [
                                         'title'             => $get('reviewFormData.title'),
                                         'subtitle'          => $get('reviewFormData.subtitle'),
                                         'show_after_seconds' => $get('reviewFormData.show_after_seconds'),
                                         'show_name_field'   => $get('reviewFormData.show_name_field'),
                                         'show_email_field'  => $get('reviewFormData.show_email_field'),
                                         'show_rating_field' => $get('reviewFormData.show_rating_field'),
                                         'show_comment_field' => $get('reviewFormData.show_comment_field'),
                                         'name_field_required' => $get('reviewFormData.name_field_required'),
                                         'email_field_required' => $get('reviewFormData.email_field_required'),
                                         'rating_field_required' => $get('reviewFormData.rating_field_required'),
                                         'comment_field_required' => $get('reviewFormData.comment_field_required'),
                                         'submit_button_text' => $get('reviewFormData.submit_button_text'),
                                         'close_button_text' => $get('reviewFormData.close_button_text'),
                                         'thank_you_message' => $get('reviewFormData.thank_you_message'),

                                     ];
                                     $livewire->dispatch('preview-review', ['formatData' => $formatData]);
                                 }),
                       ]),

                                    ]),
                            ]),
                    ])
                    ->columnSpan('full'),
            ]);
    }

    public function save(): void
    {
        $data = $this->form->getState();

        // Save the review form configuration
        ReviewFormConfig::saveAsDefault($data['reviewFormData']);

        Notification::make()
            ->title('Default review form settings saved successfully')
            ->success()
            ->send();
    }

    /**
     * Open the preview modal to show how the review form will appear
     */
    public function openPreviewModal(): void
    {
        try {
            // Get the current form state to ensure we have the latest values
            $formState = $this->form->getState();

            // Extract the reviewFormData from the form state
            $formData = $formState['reviewFormData'] ?? [];

            // Log the form data for debugging
            \Illuminate\Support\Facades\Log::info('Preview form data', [
                'formData' => $formData,
                'formState' => $formState
            ]);

            // Dispatch the event with the current form data
            // Use Livewire's dispatch method for browser events
            $this->dispatch('open-review-preview-modal', [
                'formData' => $formData
            ]);
        } catch (\Exception $e) {
            // Log any errors that occur
            \Illuminate\Support\Facades\Log::error('Error in openPreviewModal', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Dispatch the event with default values if there's an error
            $this->dispatch('open-review-preview-modal', [
                'formData' => ReviewFormConfig::getDefaultConfig()
            ]);
        }
    }

    public static function shouldRegisterNavigation(): bool
    {
        // Only show this page in navigation if the user is authenticated and is an admin
        return auth()->check() && auth()->user()->isAdmin();
    }
}
