<?php

namespace App\Filament\Widgets;

use App\Models\Campaign;
use App\Models\Ticket;
use App\Models\User;
use App\Models\Webhook;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class AdminOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    public static function canView(): bool
    {
        return 1;
    }

    protected function getColumns(): int
    {
        return 3;
    }

    protected function getStats(): array
    {
        $pendingCount = Ticket::where('status', '<', 3)->where(function ($q) {
            return $q->whereRaw('last_comment_user_id = user_id')->orWhereNull('last_comment_user_id');
        })->count();
        $openTicketCount = Ticket::where('status', '<', 3)->count();
        $pendingCount = "Pending $pendingCount";
        $openTicketCount = "Open $openTicketCount";
        $pendingCount = trim("$openTicketCount, $pendingCount");

        return [
            Stat::make('Campaigns', Campaign::where(function ($query) {
                if (auth()->user()->isAdmin() || auth()->user()->isSuperAdmin()) {
                    return $query;
                }
                return $query->where('user_id', auth()->id());
            })->count()),

            Stat::make('Users', User::whereNotNull('email_verified_at')->count())->description(User::count().' including unverified users.'),

            Stat::make('Tickets', Ticket::count())->description($pendingCount),
        ];
    }
}
