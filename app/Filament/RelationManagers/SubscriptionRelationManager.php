<?php

namespace App\Filament\RelationManagers;

use App\Filament\Resources\SubscriptionResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class SubscriptionRelationManager extends RelationManager
{
    protected static string $relationship = 'subscriptions';

    protected static ?string $recordTitleAttribute = 'id';

    public function form(Form $form): Form
    {
        return SubscriptionResource::form($form);
    }

    public function table(Table $table): Table
    {
        return SubscriptionResource::table($table);
    }
}
