<?php

namespace App\Filament\RelationManagers;

use App\Filament\Resources\SiteResource;
use App\Filament\Resources\SubscriptionResource;
use App\Filament\Resources\UserResource;
use App\Filament\Resources\WebhookResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class WebhookRelationManager extends RelationManager
{
    protected static string $relationship = 'webhooks';

    protected static ?string $recordTitleAttribute = 'id';

    public function form(Form $form): Form
    {
        return WebhookResource::form($form);
    }

    public function table(Table $table): Table
    {
        return WebhookResource::table($table);
    }
}
