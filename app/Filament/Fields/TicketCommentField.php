<?php

namespace App\Filament\Fields;

use Filament\Forms\Components\RichEditor;

class TicketCommentField
{
    static function make()
    {
        return RichEditor::make('content')
            ->translateLabel()
            ->required()
            ->fileAttachmentsDirectory('ticketing/'.date('Y-m-d'))
            ->fileAttachmentsDisk(app()->environment('local') ? 'public' : 's3')
            ->toolbarButtons([
                'attachFiles',
                // 'blockquote',
                'bold',
                // 'bulletList',
                'codeBlock',
                // 'h2',
                // 'h3',
                'italic',
                // 'link',
                // 'orderedList',
                'redo',
                'strike',
                'underline',
                'undo',
            ]);
    }

}
