<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class ChapterNumberService
{
    private static string $numbersPath = 'resources/prompts/chapter-numbers.json';
    
    /**
     * Get all chapter numbers for all languages
     */
    public static function getAllNumbers(): array
    {
        return Cache::remember('chapter_numbers', 3600, function () {
            $path = base_path(self::$numbersPath);
            
            if (!File::exists($path)) {
                return [];
            }
            
            $content = File::get($path);
            return json_decode($content, true) ?? [];
        });
    }
    
    /**
     * Get chapter numbers for a specific language
     */
    public static function getNumbersForLanguage(string $language): array
    {
        $allNumbers = self::getAllNumbers();
        $languageCode = IntroOutroTemplateService::getLanguageCode($language);
        
        return $allNumbers[$languageCode] ?? $allNumbers['en'] ?? [];
    }
    
    /**
     * Get localized chapter number
     */
    public static function getLocalizedChapterNumber(int $chapterNumber, string $language): string
    {
        $numbers = self::getNumbersForLanguage($language);
        
        // Return localized number if available, otherwise return numeric
        return $numbers[(string)$chapterNumber] ?? (string)$chapterNumber;
    }
    
    /**
     * Get next chapter number in localized format
     */
    public static function getLocalizedNextChapterNumber(int $currentChapter, string $language): string
    {
        return self::getLocalizedChapterNumber($currentChapter + 1, $language);
    }
    
    /**
     * Check if language has number translations
     */
    public static function hasNumberTranslations(string $language): bool
    {
        $numbers = self::getNumbersForLanguage($language);
        return !empty($numbers);
    }
    
    /**
     * Get available languages with number translations
     */
    public static function getAvailableLanguages(): array
    {
        $allNumbers = self::getAllNumbers();
        return array_keys($allNumbers);
    }
    
    /**
     * Clear numbers cache
     */
    public static function clearCache(): void
    {
        Cache::forget('chapter_numbers');
    }
    
    /**
     * Get number range for a language (for testing/validation)
     */
    public static function getNumberRange(string $language, int $start = 1, int $end = 30): array
    {
        $numbers = self::getNumbersForLanguage($language);
        $range = [];
        
        for ($i = $start; $i <= $end; $i++) {
            $range[$i] = $numbers[(string)$i] ?? (string)$i;
        }
        
        return $range;
    }
    
    /**
     * Get sample numbers for preview (first 5 numbers)
     */
    public static function getSampleNumbers(string $language): array
    {
        return self::getNumberRange($language, 1, 5);
    }
    
    /**
     * Validate chapter number is within supported range
     */
    public static function isValidChapterNumber(int $chapterNumber): bool
    {
        return $chapterNumber >= 1 && $chapterNumber <= 30;
    }
    
    /**
     * Get fallback number (English or numeric)
     */
    public static function getFallbackNumber(int $chapterNumber): string
    {
        $englishNumbers = self::getNumbersForLanguage('English');
        return $englishNumbers[(string)$chapterNumber] ?? (string)$chapterNumber;
    }
    
    /**
     * Get number statistics for admin interface
     */
    public static function getNumberStatistics(): array
    {
        $allNumbers = self::getAllNumbers();
        $stats = [
            'total_languages' => count($allNumbers),
            'numbers_per_language' => 30,
            'total_translations' => count($allNumbers) * 30,
            'languages' => []
        ];
        
        foreach ($allNumbers as $langCode => $numbers) {
            $stats['languages'][$langCode] = [
                'code' => $langCode,
                'name' => IntroOutroTemplateService::getLanguageName($langCode),
                'count' => count($numbers),
                'sample' => array_slice($numbers, 0, 3, true)
            ];
        }
        
        return $stats;
    }
    
    /**
     * Get language name from code (using existing service)
     */
    private static function getLanguageName(string $langCode): string
    {
        $languageNames = [
            'en' => 'English',
            'es' => 'Spanish', 
            'fr' => 'French',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
            'zh' => 'Chinese',
            'ja' => 'Japanese',
            'ko' => 'Korean',
            'ar' => 'Arabic',
            'hi' => 'Hindi',
            'tr' => 'Turkish',
            'nl' => 'Dutch',
        ];
        
        return $languageNames[$langCode] ?? strtoupper($langCode);
    }
}
