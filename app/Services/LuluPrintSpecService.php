<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class LuluPrintSpecService
{
    private const CACHE_KEY = 'lulu_print_spec55';
    private const CACHE_DURATION = 3600; // 1 hour

    /**
     * Path to the Lulu Print API specification JSON file
     * Located in storage/app/data/ following Laravel best practices
     */
    private const JSON_FILE_PATH = 'lulu-spec.json';
    //

    /**
     * Storage disk to use for file operations
     * Uses Laravel's local disk (storage/app)
     */
    private const STORAGE_DISK = 'local';

    /**
     * Get all active POD specifications from the JSON file
     */
    public static function getActiveSpecs(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_DURATION, function () {
            // Use Laravel's Storage facade to check if file exists
           
            // Get file content using Storage facade
            $jsonContent = file_get_contents(resource_path(self::JSON_FILE_PATH));
            $data = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Failed to parse Lulu Print API specification JSON: ' . json_last_error_msg());
            }

            if (!isset($data['data'])) {
                throw new \Exception('Invalid Lulu Print API specification format: missing "data" key');
            }

            // Filter only active specifications
            return array_filter($data['data'], function ($spec) {
                return isset($spec['ca']) && $spec['ca'] === 'Yes';
            });
        });
    }

    /**
     * Get all available book sizes (trim sizes) from active specs
     */
    public static function getAvailableBookSizes(): array
    {
        $specs = self::getActiveSpecs();
        $bookSizes = [];

        foreach ($specs as $spec) {
            if (isset($spec['sku'])) {
                // Extract trim size from SKU (first 9 characters)
                $trimSize = substr($spec['sku'], 0, 9);
                $bookType = $spec['bt'] ?? 'Unknown';
                $width = $spec['tw'] ?? 0;
                $height = $spec['th'] ?? 0;
                
                $bookSizes[$trimSize] = "{$bookType} ({$width}\" x {$height}\")";
            }
        }

        return array_unique($bookSizes);
    }

    /**
     * Get available paper types for a specific book size
     */
    public static function getAvailablePaperTypes(string $bookSize): array
    {
        $specs = self::getActiveSpecs();
        $paperTypes = [];

        foreach ($specs as $spec) {
            if (isset($spec['sku']) && str_starts_with($spec['sku'], $bookSize)) {
                $paperType = self::extractPaperTypeFromSKU($spec['sku']);
                if ($paperType && isset($spec['pt'])) {
                    $paperTypes[$paperType] = $spec['pt'];
                }
            }
        }

        return array_unique($paperTypes);
    }

    /**
     * Get available binding types for a specific book size and paper type
     */
    public static function getAvailableBindingTypes(string $bookSize, string $paperType = null): array
    {
        $specs = self::getActiveSpecs();
        $bindingTypes = [];

        foreach ($specs as $spec) {
            if (isset($spec['sku']) && str_starts_with($spec['sku'], $bookSize)) {
                if ($paperType === null || str_contains($spec['sku'], $paperType)) {
                    $bindingType = self::extractBindingTypeFromSKU($spec['sku']);
                    if ($bindingType && isset($spec['b'])) {
                        $bindingTypes[$bindingType] = $spec['b'];
                    }
                }
            }
        }

        return array_unique($bindingTypes);
    }

    /**
     * Get available ics for specific combination
     */
    public static function getAvailableInteriorColors(string $bookSize, string $paperType = null, string $bindingType = null): array
    {
        $specs = self::getActiveSpecs();
        $interiorColors = [];

        foreach ($specs as $spec) {
            if (self::matchesPartialSKU($spec['sku'], $bookSize, $paperType, $bindingType)) {
                $interiorColor = self::extractInteriorColorFromSKU($spec['sku']);
                if ($interiorColor && isset($spec['ic'])) {
                    $interiorColors[$interiorColor] = $spec['ic'];
                }
            }
        }

        return array_unique($interiorColors);
    }

    /**
     * Get available print qualities for specific combination
     */
    public static function getAvailablePrintQualities(string $bookSize, string $paperType = null, string $bindingType = null, string $interiorColor = null): array
    {
        $specs = self::getActiveSpecs();
        $printQualities = [];

        foreach ($specs as $spec) {
            if (self::matchesPartialSKU($spec['sku'], $bookSize, $paperType, $bindingType, $interiorColor)) {
                $printQuality = self::extractPrintQualityFromSKU($spec['sku']);
                if ($printQuality && isset($spec['pq'])) {
                    $printQualities[$printQuality] = $spec['pq'];
                }
            }
        }

        return array_unique($printQualities);
    }

    /**
     * Get available cover finishes for specific combination
     */
    public static function getAvailableCoverFinishes(string $bookSize, string $paperType = null, string $bindingType = null, string $interiorColor = null, string $printQuality = null): array
    {
        $specs = self::getActiveSpecs();
        $coverFinishes = [];

        foreach ($specs as $spec) {
            if (self::matchesPartialSKU($spec['sku'], $bookSize, $paperType, $bindingType, $interiorColor, $printQuality)) {
                $coverFinish = self::extractCoverFinishFromSKU($spec['sku']);
                if ($coverFinish && isset($spec['lm'])) {
                    $coverFinishes[$coverFinish] = $spec['lm'];
                }
            }
        }

        return array_unique($coverFinishes);
    }

    /**
     * Validate if a complete POD Package ID exists in the specifications
     */
    public static function isValidPodPackageId(string $podPackageId): bool
    {
        $specs = self::getActiveSpecs();

        foreach ($specs as $spec) {
            if (isset($spec['sku']) && $spec['sku'] === $podPackageId) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get specification details for a POD Package ID
     */
    public static function getSpecDetails(string $podPackageId): ?array
    {
        $specs = self::getActiveSpecs();

        foreach ($specs as $spec) {
            if (isset($spec['sku']) && $spec['sku'] === $podPackageId) {
                return $spec;
            }
        }

        return null;
    }

    /**
     * Extract paper type code from SKU
     */
    private static function extractPaperTypeFromSKU(string $sku): ?string
    {
        // Paper type is at positions 15-21 (0-indexed)
        if (strlen($sku) >= 22) {
            return substr($sku, 15, 7);
        }
        return null;
    }

    /**
     * Extract binding type code from SKU
     */
    private static function extractBindingTypeFromSKU(string $sku): ?string
    {
        // Binding type is at positions 13-14 (0-indexed)
        if (strlen($sku) >= 15) {
            return substr($sku, 13, 2);
        }
        return null;
    }

    /**
     * Extract ic code from SKU
     */
    private static function extractInteriorColorFromSKU(string $sku): ?string
    {
        // ic is at positions 9-10 (0-indexed)
        if (strlen($sku) >= 11) {
            return substr($sku, 9, 2);
        }
        return null;
    }

    /**
     * Extract pq code from SKU
     */
    private static function extractPrintQualityFromSKU(string $sku): ?string
    {
        // pq is at positions 11-13 (0-indexed)
        if (strlen($sku) >= 14) {
            return substr($sku, 11, 3);
        }
        return null;
    }

    /**
     * Extract cover finish code from SKU
     */
    private static function extractCoverFinishFromSKU(string $sku): ?string
    {
        // Cover finish is at position 22 (0-indexed)
        if (strlen($sku) >= 23) {
            return substr($sku, 22, 1);
        }
        return null;
    }

    /**
     * Check if SKU matches partial criteria
     */
    private static function matchesPartialSKU(string $sku, string $bookSize, string $paperType = null, string $bindingType = null, string $interiorColor = null, string $printQuality = null): bool
    {
        if (!str_starts_with($sku, $bookSize)) {
            return false;
        }

        if ($paperType && !str_contains($sku, $paperType)) {
            return false;
        }

        if ($bindingType && substr($sku, 13, 2) !== $bindingType) {
            return false;
        }

        if ($interiorColor && substr($sku, 9, 2) !== $interiorColor) {
            return false;
        }

        if ($printQuality && substr($sku, 11, 3) !== $printQuality) {
            return false;
        }

        return true;
    }

    /**
     * Clear the cache
     */
    public static function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY);
    }

    /**
     * Get the full path to the JSON specification file
     *
     * @return string Full path to the file
     */
    public static function getSpecificationFilePath(): string
    {
        return Storage::disk(self::STORAGE_DISK)->path(self::JSON_FILE_PATH);
    }

    /**
     * Check if the specification file exists
     *
     * @return bool True if file exists, false otherwise
     */
    public static function specificationFileExists(): bool
    {
        return Storage::disk(self::STORAGE_DISK)->exists(self::JSON_FILE_PATH);
    }

    /**
     * Get file information about the specification file
     *
     * @return array File information including size, last modified, etc.
     */
    public static function getSpecificationFileInfo(): array
    {
        if (!self::specificationFileExists()) {
            return [];
        }

        $disk = Storage::disk(self::STORAGE_DISK);
        $path = self::JSON_FILE_PATH;

        return [
            'path' => $path,
            'full_path' => $disk->path($path),
            'size' => $disk->size($path),
            'last_modified' => $disk->lastModified($path),
            'exists' => true,
        ];
    }

    /**
     * Reload specifications from file (bypassing cache)
     *
     * @return array Fresh specifications from file
     */
    public static function reloadSpecs(): array
    {
        self::clearCache();
        return self::getActiveSpecs();
    }
}
