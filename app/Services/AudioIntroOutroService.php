<?php

namespace App\Services;

use App\Models\Campaign;
use App\Models\Chapter;
use App\Services\IntroOutroTemplateService;

class AudioIntroOutroService
{
    /**
     * Generate intro text for a chapter
     */
    public static function generateChapterIntro(Chapter $chapter, Campaign $campaign): string
    {
        $chapterNumber = $chapter->chapter_number;
        $chapterTitle = strip_tags($chapter->title);
        $bookTitle = strip_tags($campaign->title ?: $campaign->topic);
        $language = $campaign->getForm('language') ?: 'English';
        $totalChapters = $campaign->chapters()->count();

        // Get template selection preference
        $templateSelection = $campaign->getMeta('intro_template_selection', 'random');

        if ($templateSelection === 'random') {
            $template = IntroOutroTemplateService::getRandomIntroTemplate($language);
        } else {
            // Parse specific template selection (e.g., "intro_0", "intro_1")
            if (strpos($templateSelection, 'intro_') === 0) {
                $index = (int) str_replace('intro_', '', $templateSelection);
                $template = IntroOutroTemplateService::getSpecificTemplate($language, 'intro', $index);
            } else {
                $template = IntroOutroTemplateService::getRandomIntroTemplate($language);
            }
        }

        // Fallback to English if no template found
        if (!$template) {
            $template = IntroOutroTemplateService::getRandomIntroTemplate('English')
                ?: "Welcome to Chapter {chapter_number} of {book_title}. In this chapter, we'll explore {chapter_title}.";
        }

        return IntroOutroTemplateService::generateIntro(
            $template,
            $chapterNumber,
            $chapterTitle,
            $bookTitle,
            $totalChapters,
            $language
        );
    }
    
    /**
     * Generate outro text for a chapter
     */
    public static function generateChapterOutro(Chapter $chapter, Campaign $campaign): string
    {
        $chapterNumber = $chapter->chapter_number;
        $chapterTitle = strip_tags($chapter->title);
        $bookTitle = strip_tags($campaign->title ?: $campaign->topic);
        $language = $campaign->getForm('language') ?: 'English';
        $totalChapters = $campaign->chapters()->count();

        // Get template selection preference
        $templateSelection = $campaign->getMeta('outro_template_selection', 'random');

        if ($templateSelection === 'random') {
            $template = IntroOutroTemplateService::getRandomOutroTemplate($language);
        } else {
            // Parse specific template selection (e.g., "outro_0", "outro_1")
            if (strpos($templateSelection, 'outro_') === 0) {
                $index = (int) str_replace('outro_', '', $templateSelection);
                $template = IntroOutroTemplateService::getSpecificTemplate($language, 'outro', $index);
            } else {
                $template = IntroOutroTemplateService::getRandomOutroTemplate($language);
            }
        }

        // Fallback to English if no template found
        if (!$template) {
            if ($chapterNumber < $totalChapters) {
                $template = "That concludes Chapter {chapter_number}. Join us in the next chapter as we continue our journey through {book_title}.";
            } else {
                $template = "That concludes our final chapter of {book_title}. Thank you for joining us on this journey.";
            }
        }

        return IntroOutroTemplateService::generateOutro(
            $template,
            $chapterNumber,
            $chapterTitle,
            $bookTitle,
            $totalChapters,
            $language
        );
    }
    
    /**
     * Calculate additional text length for chunk size estimation
     */
    public static function calculateIntroOutroLength(Chapter $chapter, Campaign $campaign, bool $includeIntro = true, bool $includeOutro = false): int
    {
        $totalLength = 0;
        
        if ($includeIntro) {
            $intro = self::generateChapterIntro($chapter, $campaign);
            $totalLength += strlen($intro);
        }
        
        if ($includeOutro) {
            $outro = self::generateChapterOutro($chapter, $campaign);
            $totalLength += strlen($outro);
        }
        
        return $totalLength;
    }
    
    /**
     * Prepend intro to chapter text
     */
    public static function prependIntro(string $chapterText, Chapter $chapter, Campaign $campaign): string
    {
        $intro = self::generateChapterIntro($chapter, $campaign);
        return $intro . "\n\n" . $chapterText;
    }
    
    /**
     * Append outro to chapter text
     */
    public static function appendOutro(string $chapterText, Chapter $chapter, Campaign $campaign): string
    {
        $outro = self::generateChapterOutro($chapter, $campaign);
        return $chapterText . "\n\n" . $outro;
    }
    
    /**
     * Get intro/outro configuration from campaign
     */
    public static function getAudioEnhancementConfig(Campaign $campaign): array
    {
        return [
            'include_intro' => $campaign->getMeta('include_chapter_intro', false),
            'include_outro' => $campaign->getMeta('include_chapter_outro', false),
            'intro_voice' => $campaign->getMeta('intro_voice_model', null), // For future use
            'outro_voice' => $campaign->getMeta('outro_voice_model', null), // For future use
        ];
    }
    
    /**
     * Check if audio enhancements are enabled for campaign
     */
    public static function hasAudioEnhancements(Campaign $campaign): bool
    {
        $config = self::getAudioEnhancementConfig($campaign);
        return $config['include_intro'] || $config['include_outro'];
    }
    
    /**
     * Generate sample intro/outro for preview
     */
    public static function generateSampleIntroOutro(string $bookTitle = "Your Book Title", int $chapterNumber = 1): array
    {
        return [
            'intro' => "Welcome to Chapter {$chapterNumber} of {$bookTitle}. In this chapter, we'll explore important concepts and insights.",
            'outro' => "That concludes Chapter {$chapterNumber}. Join us in the next chapter as we continue our journey through {$bookTitle}."
        ];
    }
}
