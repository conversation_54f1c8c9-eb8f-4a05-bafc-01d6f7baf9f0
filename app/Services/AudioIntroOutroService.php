<?php

namespace App\Services;

use App\Models\Campaign;
use App\Models\Chapter;

class AudioIntroOutroService
{
    /**
     * Generate intro text for a chapter
     */
    public static function generateChapterIntro(Chapter $chapter, Campaign $campaign): string
    {
        $chapterNumber = $chapter->chapter_number;
        $chapterTitle = strip_tags($chapter->title);
        $bookTitle = strip_tags($campaign->title ?: $campaign->topic);
        
        // Generate polished intro text
        $introTemplates = [
            "Welcome to Chapter {$chapterNumber} of {$bookTitle}. In this chapter, we'll explore {$chapterTitle}.",
            "Chapter {$chapterNumber}: {$chapterTitle}. Let's dive into this important topic.",
            "You're listening to Chapter {$chapterNumber} of {$bookTitle}. Today we're covering {$chapterTitle}.",
            "This is Chapter {$chapterNumber}: {$chapterTitle}. Let's begin our exploration.",
            "Welcome to Chapter {$chapterNumber}. In this section, we'll discuss {$chapterTitle}."
        ];
        
        // Select template based on chapter number for variety
        $templateIndex = ($chapterNumber - 1) % count($introTemplates);
        return $introTemplates[$templateIndex];
    }
    
    /**
     * Generate outro text for a chapter
     */
    public static function generateChapterOutro(Chapter $chapter, Campaign $campaign): string
    {
        $chapterNumber = $chapter->chapter_number;
        $totalChapters = $campaign->chapters()->count();
        $bookTitle = strip_tags($campaign->title ?: $campaign->topic);
        
        if ($chapterNumber < $totalChapters) {
            $outroTemplates = [
                "That concludes Chapter {$chapterNumber}. Join us in the next chapter as we continue our journey through {$bookTitle}.",
                "Thank you for listening to Chapter {$chapterNumber}. We'll see you in Chapter " . ($chapterNumber + 1) . ".",
                "This wraps up Chapter {$chapterNumber}. Stay tuned for more insights in the upcoming chapter.",
                "That's the end of Chapter {$chapterNumber}. The next chapter awaits with more valuable content.",
                "Chapter {$chapterNumber} is complete. Continue with us as we explore more in the next chapter."
            ];
        } else {
            // Final chapter outro
            $outroTemplates = [
                "That concludes our final chapter of {$bookTitle}. Thank you for joining us on this journey.",
                "This brings us to the end of {$bookTitle}. We hope you found this content valuable and insightful.",
                "Thank you for listening to {$bookTitle}. We hope this audiobook has provided you with valuable knowledge.",
                "That's the conclusion of {$bookTitle}. Thank you for your time and attention throughout this audiobook.",
                "We've reached the end of our journey through {$bookTitle}. Thank you for listening."
            ];
        }
        
        $templateIndex = ($chapterNumber - 1) % count($outroTemplates);
        return $outroTemplates[$templateIndex];
    }
    
    /**
     * Calculate additional text length for chunk size estimation
     */
    public static function calculateIntroOutroLength(Chapter $chapter, Campaign $campaign, bool $includeIntro = true, bool $includeOutro = false): int
    {
        $totalLength = 0;
        
        if ($includeIntro) {
            $intro = self::generateChapterIntro($chapter, $campaign);
            $totalLength += strlen($intro);
        }
        
        if ($includeOutro) {
            $outro = self::generateChapterOutro($chapter, $campaign);
            $totalLength += strlen($outro);
        }
        
        return $totalLength;
    }
    
    /**
     * Prepend intro to chapter text
     */
    public static function prependIntro(string $chapterText, Chapter $chapter, Campaign $campaign): string
    {
        $intro = self::generateChapterIntro($chapter, $campaign);
        return $intro . "\n\n" . $chapterText;
    }
    
    /**
     * Append outro to chapter text
     */
    public static function appendOutro(string $chapterText, Chapter $chapter, Campaign $campaign): string
    {
        $outro = self::generateChapterOutro($chapter, $campaign);
        return $chapterText . "\n\n" . $outro;
    }
    
    /**
     * Get intro/outro configuration from campaign
     */
    public static function getAudioEnhancementConfig(Campaign $campaign): array
    {
        return [
            'include_intro' => $campaign->getMeta('include_chapter_intro', false),
            'include_outro' => $campaign->getMeta('include_chapter_outro', false),
            'intro_voice' => $campaign->getMeta('intro_voice_model', null), // For future use
            'outro_voice' => $campaign->getMeta('outro_voice_model', null), // For future use
        ];
    }
    
    /**
     * Check if audio enhancements are enabled for campaign
     */
    public static function hasAudioEnhancements(Campaign $campaign): bool
    {
        $config = self::getAudioEnhancementConfig($campaign);
        return $config['include_intro'] || $config['include_outro'];
    }
    
    /**
     * Generate sample intro/outro for preview
     */
    public static function generateSampleIntroOutro(string $bookTitle = "Your Book Title", int $chapterNumber = 1): array
    {
        return [
            'intro' => "Welcome to Chapter {$chapterNumber} of {$bookTitle}. In this chapter, we'll explore important concepts and insights.",
            'outro' => "That concludes Chapter {$chapterNumber}. Join us in the next chapter as we continue our journey through {$bookTitle}."
        ];
    }
}
