<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class IntroOutroTemplateService
{
    private static string $templatesPath = 'resources/prompts/intro-outro-templates.json';
    
    /**
     * Get all intro/outro templates
     */
    public static function getAllTemplates(): array
    {
        return Cache::remember('intro_outro_templates', 3600, function () {
            $path = base_path(self::$templatesPath);
            
            if (!File::exists($path)) {
                return [];
            }
            
            $content = File::get($path);
            return json_decode($content, true) ?? [];
        });
    }
    
    /**
     * Get templates for a specific language
     */
    public static function getTemplatesForLanguage(string $language): array
    {
        $allTemplates = self::getAllTemplates();
        $languageCode = self::getLanguageCode($language);
        
        return $allTemplates[$languageCode] ?? $allTemplates['en'] ?? [
            'intro' => [],
            'outro' => []
        ];
    }
    
    /**
     * Get intro templates for a language
     */
    public static function getIntroTemplates(string $language): array
    {
        $templates = self::getTemplatesForLanguage($language);
        return $templates['intro'] ?? [];
    }
    
    /**
     * Get outro templates for a language
     */
    public static function getOutroTemplates(string $language): array
    {
        $templates = self::getTemplatesForLanguage($language);
        return $templates['outro'] ?? [];
    }
    
    /**
     * Get available languages
     */
    public static function getAvailableLanguages(): array
    {
        $templates = self::getAllTemplates();
        return array_keys($templates);
    }
    
    /**
     * Convert language name to language code
     */
    public static function getLanguageCode(string $language): string
    {
        $languageMap = [
            'English' => 'en',
            'English (US)' => 'en',
            'English (UK)' => 'en',
            'Spanish' => 'es',
            'Español' => 'es',
            'French' => 'fr',
            'Français' => 'fr',
            'German' => 'de',
            'Deutsch' => 'de',
            'Italian' => 'it',
            'Italiano' => 'it',
            'Portuguese' => 'pt',
            'Português' => 'pt',
            'Russian' => 'ru',
            'Русский' => 'ru',
            'Chinese' => 'zh',
            '中文' => 'zh',
            'Traditional Chinese' => 'zh',
            '繁體中文' => 'zh',
            'Japanese' => 'ja',
            '日本語' => 'ja',
            'Korean' => 'ko',
            '한국어' => 'ko',
            'Arabic' => 'ar',
            'العربية' => 'ar',
            'Hindi' => 'hi',
            'हिन्दी' => 'hi',
            'Turkish' => 'tr',
            'Türkçe' => 'tr',
            'Dutch' => 'nl',
            'Nederlands' => 'nl',
        ];
        
        return $languageMap[$language] ?? strtolower(substr($language, 0, 2));
    }
    
    /**
     * Generate intro text with template
     */
    public static function generateIntro(
        string $template,
        int $chapterNumber,
        string $chapterTitle,
        string $bookTitle,
        ?int $totalChapters = null,
        string $language = 'English'
    ): string {
        $localizedChapterNumber = \App\Services\ChapterNumberService::getLocalizedChapterNumber($chapterNumber, $language);

        $replacements = [
            '{chapter_number}' => $localizedChapterNumber,
            '{chapter_title}' => strip_tags($chapterTitle),
            '{book_title}' => strip_tags($bookTitle),
            '{total_chapters}' => $totalChapters,
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }
    
    /**
     * Generate outro text with template
     */
    public static function generateOutro(
        string $template,
        int $chapterNumber,
        string $chapterTitle,
        string $bookTitle,
        ?int $totalChapters = null,
        string $language = 'English'
    ): string {
        $localizedChapterNumber = \App\Services\ChapterNumberService::getLocalizedChapterNumber($chapterNumber, $language);
        $localizedNextChapterNumber = \App\Services\ChapterNumberService::getLocalizedNextChapterNumber($chapterNumber, $language);

        $replacements = [
            '{chapter_number}' => $localizedChapterNumber,
            '{chapter_title}' => strip_tags($chapterTitle),
            '{book_title}' => strip_tags($bookTitle),
            '{next_chapter_number}' => $localizedNextChapterNumber,
            '{total_chapters}' => $totalChapters,
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }
    
    /**
     * Get random intro template for language
     */
    public static function getRandomIntroTemplate(string $language): ?string
    {
        $templates = self::getIntroTemplates($language);
        
        if (empty($templates)) {
            return null;
        }
        
        return $templates[array_rand($templates)];
    }
    
    /**
     * Get random outro template for language
     */
    public static function getRandomOutroTemplate(string $language): ?string
    {
        $templates = self::getOutroTemplates($language);
        
        if (empty($templates)) {
            return null;
        }
        
        return $templates[array_rand($templates)];
    }
    
    /**
     * Get specific template by index
     */
    public static function getSpecificTemplate(string $language, string $type, int $index): ?string
    {
        $templates = $type === 'intro' 
            ? self::getIntroTemplates($language)
            : self::getOutroTemplates($language);
            
        return $templates[$index] ?? null;
    }
    
    /**
     * Clear templates cache
     */
    public static function clearCache(): void
    {
        Cache::forget('intro_outro_templates');
    }
    
    /**
     * Update templates (for admin interface)
     */
    public static function updateTemplates(array $templates): bool
    {
        try {
            $path = base_path(self::$templatesPath);
            $content = json_encode($templates, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
            File::put($path, $content);
            self::clearCache();
            
            return true;
        } catch (\Exception) {
            return false;
        }
    }
    
    /**
     * Get template options for form dropdown
     */
    public static function getTemplateOptionsForLanguage(string $language): array
    {
        $introTemplates = self::getIntroTemplates($language);
        $outroTemplates = self::getOutroTemplates($language);
        
        $options = ['random' => 'Random Template'];
        
        foreach ($introTemplates as $index => $template) {
            $preview = strlen($template) > 50 ? substr($template, 0, 50) . '...' : $template;
            $options["intro_{$index}"] = "Intro " . ($index + 1) . ": " . $preview;
        }
        
        foreach ($outroTemplates as $index => $template) {
            $preview = strlen($template) > 50 ? substr($template, 0, 50) . '...' : $template;
            $options["outro_{$index}"] = "Outro " . ($index + 1) . ": " . $preview;
        }
        
        return $options;
    }
}
