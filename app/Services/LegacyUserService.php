<?php

namespace App\Services;

use App\Models\User;
use App\Models\Plan;
use Carbon\Carbon;

/**
 * Service class for handling legacy user protection logic
 * 
 * Legacy users are those registered before August 1st, 2024
 * They maintain unlimited access to all features regardless of plan restrictions
 */
class LegacyUserService
{
    /**
     * The cutoff date for legacy users
     * Users registered before this date are considered legacy users
     */
    public const LEGACY_CUTOFF_DATE = '2025-07-31';

    /**
     * Check if a user is a legacy user
     * 
     * @param User $user
     * @return bool
     */
    public function isLegacyUser(User $user): bool
    {
        if (!$user->created_at) {
            return false;
        }

        return $user->created_at->lt(Carbon::parse(self::LEGACY_CUTOFF_DATE));
    }


    /**
     * Check if a user should bypass page limit restrictions
     * Legacy users have unlimited pages regardless of their plan
     * 
     * @param User $user
     * @return bool
     */
    public function shouldBypassPageLimit(User $user): bool
    {
        return $this->isLegacyUser($user);
    }

    /**
     * Get the effective page limit for a user
     * Returns null (unlimited) for legacy users, otherwise returns plan limit
     * 
     * @param User $user
     * @return int|null
     */
    public function getEffectivePageLimit(User $user): ?int
    {
        if ($this->shouldBypassPageLimit($user)) {
            return null; // Unlimited for legacy users
        }

        $currentPlan = $user->getCurrentActivePlan();
        return $currentPlan ? $currentPlan->getPageLimit() : 0;
    }

    /**
     * Check if a plan is a legacy plan (created before cutoff date)
     * 
     * @param Plan $plan
     * @return bool
     */
    public function isLegacyPlan(Plan $plan): bool
    {
        if (!$plan->created_at) {
            return false;
        }

        return $plan->created_at->lt(Carbon::parse(self::LEGACY_CUTOFF_DATE));
    }
}
