<?php

namespace App\Enum;

class AICoverImageRatioEnum
{
    // Define constants for different image sizes
    const A4 = "1024x1536";       // Best match from GPT-4o (1:1.5, close to 1:1.41 A4)
    const Letter = "1024x1536";   // Also a good match for 1:1.29
    const Legal = "1024x1536";    // DALL·E 3 supports 1:1.75, close to Legal (1:1.65) but still we'll use gpt 4o for better image
    // Instance variable to store the size value
    private string $size;
    // Constructor to set the size value
    public function __construct(string $size)
    {
        // Ensure size uses the correct "x" character instead of "×"
        $this->size = str_replace('×', 'x', $size);
    }

    // Static method to handle string input and return the corresponding instance
    public static function fromString(string $value): self
    {
        // Convert input to lowercase and match to corresponding constant
        return match (strtolower($value)) {
            'a4' => new self(self::A4),
            'letter' => new self(self::Letter),
            'legal' => new self(self::Legal),
            default => throw new \InvalidArgumentException("Invalid page size: $value"),
        };
    }

    // Method to get the size corresponding to the constant
    public function getSize(): string
    {
        return $this->size;
    }

    // Optionally, if you want a more direct conversion to OpenAI-compatible sizes
    public static function getSizeForModel(string $value, string $userAiModel = AIModelEnum::DALL_E_3->value): string
    {
        return match ($userAiModel) {
            AIModelEnum::GPT_IMAGE_1->value => self::getGptImage1Size($value),
            AIModelEnum::DALL_E_3->value => self::getDallE3Size($value),
            default => self::getDefaultSize($value),
        };
    }

    private static function getGptImage1Size(string $value): string
    {
        return match ($value) {
            self::A4, self::Letter => "1024x1536",
            self::Legal => "1024x1792",
            default => "1024x1536",
        };
    }

    private static function getDallE3Size(string $value): string
    {
        return match ($value) {
            self::A4, self::Letter => "1024x1024",
            self::Legal => "1024x1792",
            default => "1024x1024",
        };
    }

    private static function getDefaultSize(string $value): string
    {
        return match ($value) {
            self::A4, self::Letter => "1024x1536",
            self::Legal => "1024x1792",
            default => throw new \InvalidArgumentException("Invalid page size: $value"),
        };
    }
}
