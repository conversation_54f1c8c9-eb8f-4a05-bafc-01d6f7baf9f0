<?php

namespace App\Enum;

class ImageSources
{
    const AI_IMAGES = 'ai_images';
    const STOCK_IMAGES = 'stock_images';
    const GOOGLE_SEARCH_IMAGES = 'google_search_images';
    const EXTRACT_IMAGES_FROM_URL = 'extract_images_from_url';

    const DEFAULT_IMAGE_COUNT = 5;
    const DALL_E_3 = "dall-e-3";
    const GPT_IMAGE_1 = "gpt-image-1";


    static function get(): array
    {
        $options = [
            //self::EXTRACT_IMAGES_FROM_URL => 'Extract Images from URL when available',
            //self::STOCK_IMAGES => 'Stock Images (unsplash, pixabay)',
            //self::GOOGLE_SEARCH_IMAGES => 'Google Search Images',
        ];

        if (auth()->user()->hasGpt4Access() || auth()->user()->hasGptImage1Access()) {
            $options[self::AI_IMAGES] = 'AI Generated Images';
        }

        return $options;
    }

    static function all(): array
    {
        $options = [
            //self::STOCK_IMAGES,
            //self::GOOGLE_SEARCH_IMAGES,
            //self::EXTRACT_IMAGES_FROM_URL,
        ];

        if (auth()->user()->hasGpt4Access() || auth()->user()->hasGptImage1Access()) {
            $options[] = self::AI_IMAGES;
        }

        return $options;
    }

    static function aiImageModels(): array
    {
        $options = [];

        if (auth()->check() && auth()->user()->hasGpt4Access()) {
            $options[self::DALL_E_3] = 'OpenAi DALL-E 3';
        }

        if (auth()->check() && auth()->user()->hasGptImage1Access()) {
            $options[self::GPT_IMAGE_1] = 'OpenAI GPT-Image-1';
        }

        return $options;
    }
}
