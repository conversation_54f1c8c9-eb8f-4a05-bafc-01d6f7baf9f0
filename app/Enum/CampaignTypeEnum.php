<?php

namespace App\Enum;

use App\Models\SiteSetting;
use Filament\Support\Contracts\HasLabel;

enum CampaignTypeEnum: string implements HasLabel
{
    case DEFAULT = 'default';
    case SHORT_REPORT = 'short_report';
    case INFORMATIONAL_POST = 'informational_post';
    case YOUTUBE = 'youtube_video';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::DEFAULT => "Instant eBook",
            self::SHORT_REPORT => "Short Report / Lead Magnet",
            self::INFORMATIONAL_POST => "URL to eBook",
            self::YOUTUBE => "Video to eBook",
        };
    }

    static function getAllowedTypes(): array
    {
//        if (auth()->check() && !plan()?->hasPermissionToCampaigns()) {
//            return [];
//        }

        $types = [
            self::DEFAULT->value => self::DEFAULT->getLabel(),
            self::SHORT_REPORT->value => self::SHORT_REPORT->getLabel(),
        ];
        if(isCampaignTypeEnabled(FeatureEnum::VIDEO_TO_EBOOK->value) && auth()->user()->getUser()->hasVideoToEbook() ){
            $types[self::INFORMATIONAL_POST->value] = self::INFORMATIONAL_POST->getLabel();
            $types[self::YOUTUBE->value] = self::YOUTUBE->getLabel();
        }

        return $types;
    }

    static function getShortTypes(): array
    {
        return [
            self::DEFAULT->value => 'Default',
            self::SHORT_REPORT->value => 'Short Report',
            self::INFORMATIONAL_POST->value => 'Informational',
            self::YOUTUBE->value => 'YouTube',
        ];
    }

    public function isShortReport(): bool
    {
        return $this === self::SHORT_REPORT;
    }
}


