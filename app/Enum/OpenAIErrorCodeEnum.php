<?php

namespace App\Enum;

enum OpenAIErrorCodeEnum: string
{
    case INVALID_AUTHENTICATION = '401';
    case INCORRECT_API_KEY = '402';
    case MEMBERSHIP_REQUIRED = '403';
    case UNSUPPORTED_COUNTRY = '404';
    case RATE_LIMIT_REACHED = '429';
    case ENGINE_OVERLOADED = '503';
    case SERVER_ERROR = '500';
    case SLOW_DOWN = '504';

    public function getMessage(): string
    {
        return match ($this) {
            self::INVALID_AUTHENTICATION => 'Invalid Authentication: Ensure the correct OpenAI API key and requesting organization are being used.',
            self::INCORRECT_API_KEY => 'Incorrect OpenAI API key provided: Ensure the API key used is correct, clear your browser cache, or generate a new one.',
            self::MEMBERSHIP_REQUIRED => 'You must be a member of an organization to use the OpenAI API: Contact OpenAI to get added to a new organization or ask your organization manager to invite you to an organization.',
            self::UNSUPPORTED_COUNTRY => 'Country, region, or territory not supported: Please see the OpenAI documentation for more information.',
            self::RATE_LIMIT_REACHED => 'Rate limit reached for requests: You are sending requests too quickly. Pace your requests according to the OpenAI rate limit guide.',
            self::ENGINE_OVERLOADED => 'The engine is currently overloaded: Please try again later or reduce your OpenAI request rate. High traffic is currently affecting the OpenAI service.',
            self::SERVER_ERROR => 'Server error while processing your request: Retry after a brief wait or check the OpenAI status page for updates.',
            self::SLOW_DOWN => 'Slow Down: A sudden increase in your OpenAI request rate is impacting service reliability. Please reduce your request rate and try again.',
            default => 'An unknown error occurred while communicating with the OpenAI service. Please try again later.'
        };
    }
}
