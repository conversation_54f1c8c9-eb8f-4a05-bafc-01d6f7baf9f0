<?php

namespace App\Enum;

use Filament\Support\Contracts\HasLabel;

enum CreditLogActionEnum: string implements HasLabel
{
    case BILLING = 'billing';
    case GATEWAY_REFUND = 'gateway-refund';
    case REFUND = 'refund';
    case CAMPAIGN = 'campaign';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::BILLING => 'Billing',
            self::REFUND => 'Refund',
            self::CAMPAIGN => 'Campaign',
            self::GATEWAY_REFUND => 'Gateway Refund',
        };
    }
}
