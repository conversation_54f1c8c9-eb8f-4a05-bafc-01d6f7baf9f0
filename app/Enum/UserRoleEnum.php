<?php

namespace App\Enum;

enum UserRoleEnum: string
{
    case USER = 'user';
    case ADMIN = 'admin';
    case SUPER_ADMIN = 'super_admin';

    case SUPPORT = 'support';

    function isAdmin(): bool
    {
        return in_array($this->value, [self::ADMIN->value, self::SUPER_ADMIN->value]);
    }

    function isSuperAdmin(): bool
    {
        return $this->value == self::SUPER_ADMIN->value;
    }
    function isSupport(): bool
    {
        return $this->value == self::SUPPORT->value;
    }

    public static function getRoles(): array
    {
        return [
            self::USER->value => 'User',
            self::ADMIN->value => 'Admin',
            self::SUPPORT->value => 'Support',
            self::SUPER_ADMIN->value => 'Super Admin',
        ];
    }

    public static function adminAccessibleRoles(): array
    {
        return [
            self::ADMIN->value,
            self::SUPER_ADMIN->value
        ];
    }

    public static function ticketAccessibleRoles(): array
    {
        return [
            self::ADMIN->value,
            self::SUPER_ADMIN->value,
            self::SUPPORT->value,
        ];
    }
}
