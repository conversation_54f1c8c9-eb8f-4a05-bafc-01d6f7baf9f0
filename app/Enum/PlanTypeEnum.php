<?php

namespace App\Enum;

use Filament\Support\Contracts\HasLabel;

enum PlanTypeEnum: string  implements <PERSON><PERSON>abe<PERSON>
{
    case FREE = 'free';
    case LIFETIME = 'lifetime';
    case LTD_MONTHLY = 'ltd_monthly';
    case LTD_YEARLY = 'ltd_yearly';
    case MONTHLY = 'monthly';
    case YEARLY = 'yearly';
    case REFUND = 'refund';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::FREE => title(self::FREE->value),
            self::LIFETIME => title(self::LIFETIME->value),
            self::LTD_MONTHLY => title(self::LTD_MONTHLY->value),
            self::LTD_YEARLY => title(self::LTD_YEARLY->value),
            self::MONTHLY => title(self::MONTHLY->value),
            self::YEARLY => title(self::YEARLY->value),
            self::REFUND => title(self::REFUND->value),
        };
    }

    static function getRecurringSubscriptionTypes(): array
    {
        return [
            self::MONTHLY,
            self::YEARLY,
        ];
    }

    static function getRecurringLTDTypes(): array
    {
        return [
            self::LTD_MONTHLY,
            self::LTD_YEARLY,
        ];
    }
}
