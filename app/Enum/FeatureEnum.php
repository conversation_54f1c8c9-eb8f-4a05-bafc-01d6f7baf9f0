<?php

namespace App\Enum;

use App\Models\SiteSetting;
use Filament\Support\Contracts\HasLabel;

enum FeatureEnum: string implements HasLabel
{

    case RESEARCH_TOOLS = 'research_tools';
    case LINK_SHARING = 'link_sharing';
    case VIDEO_TO_EBOOK = 'video_to_ebook';
    case LEAD_COLLECTION = 'lead_collection';
    case AUDIO_BOOK_GENERATION = 'audio_book_generation';
    case LULU_INTEGRATION = 'lulu_integration';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::RESEARCH_TOOLS => 'Research Tools',
            self::LINK_SHARING => 'Link Sharing',
            self::LEAD_COLLECTION => 'Lead Collection',
            self::VIDEO_TO_EBOOK => 'Video to ebook',
            self::AUDIO_BOOK_GENERATION => 'Audio Book Generation',
            self::LULU_INTEGRATION => 'Lulu Integration',
        };
    }

    static function getAllowedTypes(): array
    {
//        if (auth()->check() && !plan()?->hasPermissionToCampaigns()) {
//            return [];
//        }

        $types = [
            self::RESEARCH_TOOLS->value => self::RESEARCH_TOOLS->getLabel(),
            self::LINK_SHARING->value => self::LINK_SHARING->getLabel(),
            self::LEAD_COLLECTION->value => self::LEAD_COLLECTION->getLabel(),
            self::VIDEO_TO_EBOOK->value => self::VIDEO_TO_EBOOK->getLabel(),
            self::AUDIO_BOOK_GENERATION->value => self::AUDIO_BOOK_GENERATION->getLabel(),
            self::LULU_INTEGRATION->value => self::LULU_INTEGRATION->getLabel(),
        ];

        return $types;
    }

    static function getShortTypes(): array
    {
        return [
            self::RESEARCH_TOOLS->value => 'Research Tools',
            self::LINK_SHARING->value => 'Link Sharing',
            self::LEAD_COLLECTION->value => 'Lead Collection',
            self::VIDEO_TO_EBOOK => 'Video to ebook',
            self::AUDIO_BOOK_GENERATION->value => 'Audio Book Generation',
            self::LULU_INTEGRATION->value => 'Lulu Integration',
        ];
    }

    static function getOptions(): array
    {
        return [
            self::RESEARCH_TOOLS->value => self::RESEARCH_TOOLS->getLabel(),
            self::LINK_SHARING->value => self::LINK_SHARING->getLabel(),
            self::LEAD_COLLECTION->value => self::LEAD_COLLECTION->getLabel(),
            self::VIDEO_TO_EBOOK->value => self::VIDEO_TO_EBOOK->getLabel(),
            self::AUDIO_BOOK_GENERATION->value => self::AUDIO_BOOK_GENERATION->getLabel(),
            self::LULU_INTEGRATION->value => self::LULU_INTEGRATION->getLabel(),
        ];
    }
}


