<?php

namespace App\Enum;

class ImageStyles
{


    //illustration,cartoon,photorealistic
    const ILLUSTRATION = 'illustration';
    const CARTOON = 'cartoon';
    const PHOTOREALISTIC = 'photorealistic';




    static function get(): array
    {
        $options = [];
        $options[self::ILLUSTRATION] = 'Illustration';
        $options[self::CARTOON] = 'Cartoon';
        $options[self::PHOTOREALISTIC] = 'Photorealistic';

        return $options;
    }

    static function all(): array
    {
        $options = [
            self::ILLUSTRATION,
            self::CARTOON,
            self::PHOTOREALISTIC,
        ];


        return $options;
    }

    static function aiImageStyles(): array
    {
        $options = [
            self::ILLUSTRATION => 'Illustration',
            self::CARTOON => 'Cartoon',
            self::PHOTOREALISTIC => 'Photorealistic',
        ];
        


        return $options;
    }
}
