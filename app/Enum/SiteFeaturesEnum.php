<?php

namespace App\Enum;

use App\Service\YouTubeDetector;
use Filament\Support\Contracts\HasLabel;

enum SiteFeaturesEnum: string implements HasLabel {
    case MEDIUM = 'medium';
    case REDDIT = 'reddit';
    case YOUTUBE = 'youtube';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::MEDIUM => 'Medium',
            self::REDDIT => 'Reddit',
            self::YOUTUBE => 'Youtube',

        };
    }

    public static function getOptions(): array
    {
        return [
            self::MEDIUM->value => 'Medium',
            self::REDDIT->value => 'Reddit',
            self::YOUTUBE->value => 'Youtube',

        ];
    }

    public static function isValidUrl($url)
    {
        if (platformName() == SiteFeaturesEnum::YOUTUBE->value) {
            return (new YouTubeDetector($url))->isValidYouTubeUrl();

        } else {
            return str_contains($url, CampaignPlatformEnum::platformUrl(platformName()));
        }

    }
}
