<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CampaignReview extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'rating',
        'comment',
        'campaign_id',
    ];

    /**
     * Get the campaign that owns the review.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }
}
