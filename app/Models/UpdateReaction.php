<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UpdateReaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'app_update_id',
        'user_id',
        'reaction_type',
    ];

    /**
     * Get the update that this reaction belongs to
     */
    public function appUpdate(): BelongsTo
    {
        return $this->belongsTo(AppUpdate::class);
    }

    /**
     * Get the user who made this reaction
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Available reaction types
     */
    public static function getReactionTypes(): array
    {
        return [
            'like' => '😊',
            'love' => '😍',
            'celebrate' => '🎉',
        ];
    }

    /**
     * Get the emoji for this reaction type
     */
    public function getEmojiAttribute(): string
    {
        return self::getReactionTypes()[$this->reaction_type] ?? '😊';
    }
}
