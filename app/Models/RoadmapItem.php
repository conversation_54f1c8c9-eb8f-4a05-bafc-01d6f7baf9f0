<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class RoadmapItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'summary',
        'content',
        'status',
        'priority',
        'category',
        'estimated_date',
        'completed_date',
        'is_published',
        'metadata',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'estimated_date' => 'date',
        'completed_date' => 'date',
        'metadata' => 'array',
    ];

    /**
     * Scope to get only published items
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to get items by status
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get items by priority
     */
    public function scopeByPriority(Builder $query, string $priority): Builder
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get items by category
     */
    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get upcoming items (not completed)
     */
    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->whereIn('status', ['planned', 'in_progress']);
    }

    /**
     * Scope to get completed items
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'planned' => 'gray',
            'in_progress' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'gray'
        };
    }

    /**
     * Get priority badge color
     */
    public function getPriorityBadgeColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'gray',
            'medium' => 'info',
            'high' => 'warning',
            'critical' => 'danger',
            default => 'gray'
        };
    }

    /**
     * Get category emoji
     */
    public function getCategoryEmojiAttribute(): string
    {
        return match($this->category) {
            'features' => '🚀',
            'improvements' => '⚡',
            'fixes' => '🐛',
            'infrastructure' => '🏗️',
            default => '📋'
        };
    }

    /**
     * Get status emoji
     */
    public function getStatusEmojiAttribute(): string
    {
        return match($this->status) {
            'planned' => '📋',
            'in_progress' => '⚡',
            'completed' => '✅',
            'cancelled' => '❌',
            default => '📋'
        };
    }

    /**
     * Get formatted estimated date
     */
    public function getFormattedEstimatedDateAttribute(): ?string
    {
        if (!$this->estimated_date) {
            return null;
        }

        $date = Carbon::parse($this->estimated_date);
        $now = Carbon::now();

        if ($date->isToday()) {
            return 'Today';
        } elseif ($date->isTomorrow()) {
            return 'Tomorrow';
        } elseif ($date->isCurrentWeek()) {
            return $date->format('l'); // Day name
        } elseif ($date->isCurrentMonth()) {
            return $date->format('M j'); // Jan 15
        } elseif ($date->isCurrentYear()) {
            return $date->format('M j'); // Jan 15
        } else {
            return $date->format('M j, Y'); // Jan 15, 2024
        }
    }

    /**
     * Check if item is overdue
     */
    public function getIsOverdueAttribute(): bool
    {
        if (!$this->estimated_date || $this->status === 'completed' || $this->status === 'cancelled') {
            return false;
        }

        return Carbon::parse($this->estimated_date)->isPast();
    }

    /**
     * Get progress percentage for in-progress items
     */
    public function getProgressPercentageAttribute(): int
    {
        return match($this->status) {
            'planned' => 0,
            'in_progress' => $this->metadata['progress'] ?? 50,
            'completed' => 100,
            'cancelled' => 0,
            default => 0
        };
    }

    /**
     * Get time until estimated date
     */
    public function getTimeUntilAttribute(): ?string
    {
        if (!$this->estimated_date || $this->status === 'completed' || $this->status === 'cancelled') {
            return null;
        }

        $date = Carbon::parse($this->estimated_date);
        $now = Carbon::now();

        if ($date->isPast()) {
            return 'Overdue by ' . $now->diffForHumans($date, true);
        }

        return 'In ' . $now->diffForHumans($date, true);
    }

    /**
     * Likes relationship
     */
    public function likes(): HasMany
    {
        return $this->hasMany(RoadmapLike::class);
    }

    /**
     * Get total likes count
     */
    public function getLikesCountAttribute(): int
    {
        return $this->likes()->count();
    }

    /**
     * Check if current user has liked this item
     */
    public function getIsLikedByCurrentUserAttribute(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return $this->likes()->where('user_id', auth()->id())->exists();
    }
}
