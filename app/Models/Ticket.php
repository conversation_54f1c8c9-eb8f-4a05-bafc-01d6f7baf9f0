<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Ticket extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function ticketable(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(config('filament-ticketing.user-model'));
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    public function assigned_to(): BelongsTo
    {
        return $this->belongsTo(config('filament-ticketing.user-model'));
    }

    public function priorityColor(): string
    {
        $colors = [1 => 'success', 2 => 'primary', 3 => 'warning', 4 => 'danger'];

        return $colors[$this->priority] ?? 'danger';
    }
}
