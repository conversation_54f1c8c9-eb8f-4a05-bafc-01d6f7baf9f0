<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class UserUpdatePreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'email_major_updates',
        'email_minor_updates',
        'email_patch_updates',
        'email_security_updates',
        'categories',
        'unsubscribe_token',
        'last_email_sent_at',
    ];

    protected $casts = [
        'email_major_updates' => 'boolean',
        'email_minor_updates' => 'boolean',
        'email_patch_updates' => 'boolean',
        'email_security_updates' => 'boolean',
        'categories' => 'array',
        'last_email_sent_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->unsubscribe_token)) {
                $model->unsubscribe_token = Str::random(64);
            }
        });
    }

    /**
     * Get the user that owns the preference
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if user wants email notifications for a specific update type
     */
    public function wantsEmailForType(string $type): bool
    {
        return match($type) {
            'major' => $this->email_major_updates,
            'minor' => $this->email_minor_updates,
            'patch' => $this->email_patch_updates,
            'security' => $this->email_security_updates,
            default => false
        };
    }

    /**
     * Check if user wants email notifications for a specific category
     */
    public function wantsEmailForCategory(string $category): bool
    {
        if (empty($this->categories)) {
            return true; // Default to all categories if none specified
        }

        return in_array($category, $this->categories);
    }

    /**
     * Check if user should receive email for an update
     */
    public function shouldReceiveEmailFor(AppUpdate $update): bool
    {
        return $this->wantsEmailForType($update->type) &&
               $this->wantsEmailForCategory($update->category);
    }

    /**
     * Generate unsubscribe URL
     */
    public function getUnsubscribeUrlAttribute(): string
    {
        return route('update-notifications.unsubscribe', $this->unsubscribe_token);
    }

    /**
     * Update last email sent timestamp
     */
    public function updateLastEmailSent(): void
    {
        $this->update(['last_email_sent_at' => now()]);
    }

    /**
     * Get or create preferences for a user
     */
    public static function getOrCreateForUser(User $user): self
    {
        return static::firstOrCreate(
            ['user_id' => $user->id],
            [
                'email_major_updates' => true,
                'email_minor_updates' => false,
                'email_patch_updates' => false,
                'email_security_updates' => true,
                'categories' => null,
            ]
        );
    }
}
