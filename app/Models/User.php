<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Enum\AccountCampaignTypeEnum;
use App\Enum\AIModelEnum;
use App\Enum\CampaignTypeEnum;
use App\Enum\PlanSlugEnum;
use App\Enum\UserRoleEnum;
use App\Traits\HasLogger;
use Filament\Facades\Filament;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use App\Traits\HasCreditLog;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Support\Arr;
use App\Services\LegacyUserService;

class User extends Authenticatable implements FilamentUser, MustVerifyEmail
{
    use <PERSON><PERSON><PERSON>Tokens, HasCredit<PERSON>og, HasLogger, HasFactory, Notifiable, HasCreditLog;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'meta',
        'credits',
        'credits_used',
        'youtube_api_key',
        'billing_address',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'youtube_api_key' => 'array',
        'meta' => 'array',
        'role' => UserRoleEnum::class,
    ];
    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }
    public function isAdmin(): bool
    {
        if ($this->role) {
            return $this->role->isAdmin() || $this->role->isSuperAdmin();
        }

        return false;
    }
    public function isSuperAdmin(): bool
    {
        if ($this->role) {
            return $this->role->isSuperAdmin();
        }
        return false;
    }
    public function isSupport(): bool
    {
        if ($this->role) {
            return $this->role->isSupport();
        }

        return false;
    }
    public function isUser(): bool
    {
        return $this->role === UserRoleEnum::USER;
    }
    public function getUser(): User|Model|null
    {
        return Filament::getTenant() ?: auth()->user() ?: $this;
    }
    public function isRecordOwner(int $recordUserId): bool
    {
        return $this->getUser()->id === $recordUserId;
    }

    public function isCreditAvailable(string $campaignType, int $limit)
    {
        return $this->remainCredit() >= $this->campaignCreditCost($campaignType, $limit);
    }
    public function isCreditAvailableForAccountCampaign(string $campaignType, int $limit)
    {
        return $this->remainCredit() >= $this->accountCampaignCreditCost($campaignType, $limit);
    }
    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class);
    }
    public function webhooks(): HasMany
    {
        return $this->hasMany(Webhook::class);
    }
    
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class);
    }

    public function creditlogs(): HasMany
    {
        return $this->hasMany(CreditLog::class);
    }
    public function campaignCreditCost(string $campaignType, int $limit)
    {
        return 1;
        // $rates = CampaignTypeEnum::creditCostRates($campaignType);
        // return $limit * $rates;
    }
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class)->orderByDesc('created_at');
    }
    public function unlimitedSubscription(): ?Subscription
    {
        return $this->subscriptions()->whereHas('plan', fn($q) => $q->where('is_unlimited', true))->first();
    }

    public function subscription(): ?Subscription
    {
        return $this->subscriptions->first();
    }

    public function hasValidSubscription(): bool
    {
        return $this->getUser()->subscription() && $this->getUser()->subscription()->isActive;
    }
    public function remainCredit()
    {
        $credits = $this->credits - $this->credits_used;
        return $credits > 0 ? (float) $credits : 0;
    }

    public function canUpdateTicket(): bool
    {
        return in_array($this->role->value, UserRoleEnum::ticketAccessibleRoles());
    }
    public function useCredit($credit = 0): bool
    {
        $user = $this->getUser();

        if (!$user || $user->credits < $user->credits_used) {
            return false;
        }

        $user->credits_used = $user->credits_used + $credit;
        $user->save();

        return true;
    }
    public function refundCredit($credit = 0): bool
    {
        $user = $this->getUser();

        $user->credits_used = $user->credits_used - $credit;
        $user->save();

        return true;
    }

    public function getAvailableAIModels(): array
    {
        $options = [];

        if ($this->getUser()?->hasOpenRouterAccess()) {
            $options = [
                AIModelEnum::OPEN_ROUTER->value . "-" . AIModelEnum::OPENROUTER_OPENAI_GPT_45->value => AIModelEnum::getLabels()[AIModelEnum::OPENROUTER_OPENAI_GPT_45->value],
                AIModelEnum::GPT_3_5_TURBO->value => AIModelEnum::getLabels()[AIModelEnum::GPT_3_5_TURBO->value],
            ];
        } else {
            $options = [
                AIModelEnum::GPT_3_5_TURBO->value => AIModelEnum::getLabels()[AIModelEnum::GPT_3_5_TURBO->value],
            ];
        }
        //

        if ($this->getUser()?->hasGpt4Access()) {
            //$options[AIModelEnum::GPT_4->value] = AIModelEnum::getLabels()[AIModelEnum::GPT_4->value];
            $options[AIModelEnum::GPT_4_TURBO->value] = AIModelEnum::getLabels()[AIModelEnum::GPT_4_TURBO->value];
            $options[AIModelEnum::GPT_4O_MINI->value] = AIModelEnum::getLabels()[AIModelEnum::GPT_4O_MINI->value];
            $options[AIModelEnum::GPT_4O->value] = AIModelEnum::getLabels()[AIModelEnum::GPT_4O->value] . ' (Recommended)';
        }

        if ($this->getUser()?->hasOpenRouterAccess()) {
            foreach (AIModelEnum::getOpenRouterModels() as $model) {
                if ($model == AIModelEnum::OPENROUTER_OPENAI_GPT_45->value) {
                    continue;
                }
                $options[AIModelEnum::OPEN_ROUTER->value . '-' . $model] = AIModelEnum::getLabels()[$model];
            }
        }

        return $options;
    }

    public function hasOpenRouterAccess(): bool
    {
        return $this->userAiModels()
            ->where('ai_model', AIModelEnum::OPEN_ROUTER->value)->count() > 0;
    }


    public function userAiModels()
    {
        return $this->hasMany(UserAiModel::class);
    }

    /**
     * Get the user's update preferences
     */
    public function updatePreferences()
    {
        return $this->hasOne(UserUpdatePreference::class);
    }

    /**
     * Get the user's update notifications
     */
    public function updateNotifications()
    {
        return $this->hasMany(UserUpdateNotification::class);
    }

    /**
     * Get unread update notifications count
     */
    public function getUnreadUpdatesCountAttribute(): int
    {
        return $this->updateNotifications()->unread()->count();
    }

    public function getOpenAiApiKey(): ?string
    {
        return $this->userAiModels()
            ->where('ai_model', AIModelEnum::OPEN_AI->value)
            ->value('api_key');
    }

    public function getOpenRouterApiKey(): ?string
    {
        return $this->userAiModels()
            ->where('ai_model', AIModelEnum::OPEN_ROUTER->value)
            ->value('api_key');
    }

    public function getAiApiKey(string $aiModel): ?string
    {
        $aiModel = strtolower($aiModel);
        $provider = AIModelEnum::getProviderForModel($aiModel);

        return match ($provider) {
            AIModelEnum::OPEN_AI => $this->getOpenAiApiKey(),
            AIModelEnum::OPEN_ROUTER => $this->getOpenRouterApiKey(),
            default => null,
        };
    }

    function hasGpt4Access(): bool
    {
        return (bool) $this->userAiModels()
            ->where('ai_model', 'openai')
            ->value('has_gpt4');
    }

    function hasGptImage1Access(): bool
    {
        return (bool) $this->userAiModels()
                           ->where('ai_model', 'openai')
                           ->value('has_gpt_image_1');
    }

    public function getLuluAPI(): ?array
    {
        return $this->getMeta('luluAPI');
    }

    function getMeta($key, $default = null)
    {
        return Arr::get($this->meta, $key, $default);
    }

    function hasResearchTool(): bool
    {
        if ($this->hasValidSubscription()) {
            $plans = $this->subscriptions()->where('status', 'active')->with('plan')->get()->pluck('plan');

            foreach ($plans as $plan) {
                if ($plan->includes_all_addons || $plan->hasPermissionResearchTools()) {
                    return true;
                }
            }
        }
        return false;
    }
    function hasLinkSharing(): bool
    {
        if ($this->hasValidSubscription()) {
            $plans = $this->subscriptions()->where('status', 'active')->with('plan')->get()->pluck('plan');

            foreach ($plans as $plan) {
                if ($plan->includes_all_addons || $plan->hasPermissionLinkSharing()) {
                    return true;
                }
            }
        }
        return false;
    }
    function hasLeadCollection(): bool
    {
        if ($this->hasValidSubscription()) {
            $plans = $this->subscriptions()->where('status', 'active')->with('plan')->get()->pluck('plan');

            foreach ($plans as $plan) {
                if ($plan->includes_all_addons || $plan->hasPermissionLeadCollection()) {
                    return true;
                }
            }
        }
        return false;
    }
    function hasAudioBookGeneration(): bool
    {
        if ($this->hasValidSubscription()) {
            $plans = $this->subscriptions()->where('status', 'active')->with('plan')->get()->pluck('plan');

            foreach ($plans as $plan) {
                if ($plan->includes_all_addons || $plan->hasPermissionAudioBookGeneration()) {
                    return true;
                }
            }
        }
        return false;
    }
    function hasLuluIntegration(): bool
    {
        if ($this->hasValidSubscription()) {
            $plans = $this->subscriptions()->where('status', 'active')->with('plan')->get()->pluck('plan');

            foreach ($plans as $plan) {
                if ($plan->hasPermissionLuluIntegration()) {
                    return true;
                }
            }
        }
        return false;
    }
    function hasVideoToEbook(): bool
    {
        if ($this->hasValidSubscription()) {
            $plans = $this->subscriptions()->where('status', 'active')->with('plan')->get()->pluck('plan');
            foreach ($plans as $plan) {
                if ($plan->includes_all_addons || $plan->hasPermissionVideoToEbook()) {
                    return true;
                }
            }
        }
        return false;
    }
    
    function hasUnlimitedPlan(): bool
    {
        if ($this->hasValidSubscription()) {
            $plans = $this->subscriptions()->where('status', 'active')->with('plan')->get()->pluck('plan');
            foreach ($plans as $plan) {
                if ($plan && $plan?->slug == 'agency') {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Check if this user is a legacy user (registered before Aug 1st, 2024)
     * Legacy users have unlimited access to all features
     */
    public function isLegacyUser(): bool
    {
        return app(LegacyUserService::class)->isLegacyUser($this);
    }

    /**
     * Get the current active plan for this user
     */
    public function getCurrentActivePlan(): ?Plan
    {
        $subscription = $this->subscriptions()->where('status', 'active')->latest()->first();
        return $subscription ? $subscription->plan : null;
    }

    /**
     * Get remaining pages for this user
     * Legacy users have unlimited pages
     */
    public function getPageLimit(): ?int
    {
        $legacyService = app(LegacyUserService::class);
        return $legacyService->getEffectivePageLimit($this);
    }
}
