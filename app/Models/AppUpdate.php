<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AppUpdate extends Model
{
    use HasFactory;

    protected $fillable = [
        'version',
        'title',
        'summary',
        'content',
        'type',
        'category',
        'is_published',
        'send_email_notification',
        'email_sent_at',
        'metadata',
        'released_at',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'send_email_notification' => 'boolean',
        'email_sent_at' => 'datetime',
        'released_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user notifications for this update
     */
    public function userNotifications(): HasMany
    {
        return $this->hasMany(UserUpdateNotification::class);
    }

    /**
     * Scope to get only published updates
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to get updates by type
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get updates by category
     */
    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get recent updates
     */
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('released_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Scope to get major updates
     */
    public function scopeMajor(Builder $query): Builder
    {
        return $query->where('type', 'major');
    }

    /**
     * Check if this update should trigger email notifications
     */
    public function shouldSendEmailNotification(): bool
    {
        return $this->send_email_notification &&
               $this->is_published &&
               is_null($this->email_sent_at);
    }

    /**
     * Mark email as sent
     */
    public function markEmailAsSent(): void
    {
        $this->update(['email_sent_at' => now()]);
    }

    /**
     * Get formatted version for display
     */
    public function getFormattedVersionAttribute(): string
    {
        return "v{$this->version}";
    }

    /**
     * Get type badge color
     */
    public function getTypeBadgeColorAttribute(): string
    {
        return match($this->type) {
            'major' => 'success',
            'minor' => 'info',
            'patch' => 'warning',
            default => 'secondary'
        };
    }

    /**
     * Get category badge color
     */
    public function getCategoryBadgeColorAttribute(): string
    {
        return match($this->category) {
            'features' => 'primary',
            'fixes' => 'danger',
            'improvements' => 'info',
            'security' => 'warning',
            default => 'secondary'
        };
    }

    /**
     * Get processed markdown content with proper image URLs
     */
    public function getProcessedContentAttribute(): string
    {
        if (!$this->content) {
            return '';
        }

        try {
            $content = $this->content;

            // Process images in markdown content
            $content = $this->processMarkdownImages($content);

            return Str::markdown($content);
        } catch (\Exception $e) {
            \Log::error('Failed to process changelog content for update ' . $this->id . ': ' . $e->getMessage());

            // Fallback to basic markdown processing
            return Str::markdown($this->content);
        }
    }

    /**
     * Process markdown images to ensure proper URLs
     */
    private function processMarkdownImages(string $content): string
    {
        // Pattern to match markdown images: ![alt](url)
        $pattern = '/!\[([^\]]*)\]\(([^)]+)\)/';

        return preg_replace_callback($pattern, function ($matches) {
            $altText = $matches[1];
            $imageUrl = $matches[2];

            // Handle different URL formats
            $processedUrl = $this->processImageUrl($imageUrl);

            return "![{$altText}]({$processedUrl})";
        }, $content);
    }

    /**
     * Process individual image URL to ensure it's accessible
     */
    private function processImageUrl(string $imageUrl): string
    {
        // If it's already a full URL with correct domain, return as is
        if (str_starts_with($imageUrl, config('app.url'))) {
            return $imageUrl;
        }

        // Handle localhost URLs - convert to proper app URL
        if (str_starts_with($imageUrl, 'http://localhost/storage/')) {
            $path = str_replace('http://localhost/storage/', '', $imageUrl);
            return $this->generateStorageUrl($path);
        }

        // Handle relative storage URLs
        if (str_starts_with($imageUrl, '/storage/')) {
            $path = str_replace('/storage/', '', $imageUrl);
            return $this->generateStorageUrl($path);
        }

        // Handle Filament temporary upload URLs
        if (str_contains($imageUrl, 'livewire-tmp')) {
            $imageUrl = $this->convertTempUrlToPermanent($imageUrl);
        }

        // If it's a storage path without URL, generate proper URL
        if (!str_starts_with($imageUrl, 'http')) {
            return $this->generateStorageUrl($imageUrl);
        }

        return $imageUrl;
    }

    /**
     * Generate proper storage URL for a given path
     */
    private function generateStorageUrl(string $path): string
    {
        try {
            if (app()->environment('local')) {
                // For local environment, use public disk
                if (Storage::disk('public')->exists($path)) {
                    return Storage::disk('public')->url($path);
                }
            } else {
                // For production, use S3 with temporary URL
                if (Storage::disk('s3')->exists($path)) {
                    return Storage::disk('s3')->temporaryUrl($path, now()->addHours(24));
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to generate storage URL for path: ' . $path . ' - ' . $e->getMessage());
        }

        // Fallback: construct URL manually
        return config('app.url') . '/storage/' . $path;
    }

    /**
     * Convert temporary upload URL to permanent storage path
     */
    private function convertTempUrlToPermanent(string $tempUrl): string
    {
        // Extract filename from temporary URL
        $filename = basename(parse_url($tempUrl, PHP_URL_PATH));

        // Check if file exists in the changelog images directory
        $permanentPath = 'changelog/images/' . date('Y/m/d') . '/' . $filename;

        $disk = app()->environment('local') ? 'public' : 's3';

        if (Storage::disk($disk)->exists($permanentPath)) {
            return $permanentPath;
        }

        // If not found, return original URL
        return $tempUrl;
    }
}
