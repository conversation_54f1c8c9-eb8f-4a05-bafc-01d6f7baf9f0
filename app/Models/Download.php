<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Download extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = ["id"];

    protected $casts = [
        'meta' => 'json',
    ];

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getMeta($key, $default = null)
    {
        return $this->meta[$key] ?? $default;
    }

    public function getPdf()
    {
        return $this->path ?? '';
    }

    /**
     * Get the review form configuration.
     *
     * @return array
     */
    public function getReviewFormConfig(): array
    {
        $defaultConfig = \App\Filament\Resources\CampaignResource\ReviewFormConfig::getDefaultConfig();

        $config = $this->review_form_config ?? [];

        return array_merge($defaultConfig, $config);
    }

     /**
     * Check if a specific field should be shown in the review form.
     *
     * @param string $field
     * @return bool
     */
    public function shouldShowReviewField(string $field): bool
    {
        $config = $this->getReviewFormConfig();
        return $config["show_{$field}_field"] ?? false;
    }

    /**
     * Check if a specific field is required in the review form.
     *
     * @param string $field
     * @return bool
     */
    public function isReviewFieldRequired(string $field): bool
    {
        $config = $this->getReviewFormConfig();
        return $config["{$field}_field_required"] ?? false;
    }
}
