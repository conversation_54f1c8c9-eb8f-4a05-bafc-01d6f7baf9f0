<?php

namespace App\Models;

use App\Enum\AIModelEnum;
use App\Enum\CampaignStatusEnum;
use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Traits\HasLogger;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ResearchEbookCampaign extends Model
{
    use HasLogger;
    use HasFactory;

    protected $guarded = ["id"];

    protected $casts = [
        'status' => CampaignStatusEnum::class,
        'type' => ResearchEbookCampaignIdeaTypeEnum::class,
        'form' => 'array',
        'meta' => 'array',
    ];

    function getForm($key, $default = null)
    {
        return $this->form[$key] ?? $default;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getOpenAiApiKey(): ?string
    {
        return $this->user?->getOpenAiApiKey();
    }

    public function getOpenRouterApiKey(): ?string
    {
        return $this->user?->getOpenRouterApiKey();
    }

    public function getAiApiKey(string $aiModel): ?string
    {
        $aiModel = strtolower($aiModel);
        $provider = AIModelEnum::getProviderForModel($aiModel);

        return match ($provider) {
            AIModelEnum::OPEN_AI => $this->getOpenAiApiKey(),
            AIModelEnum::OPEN_ROUTER => $this->getOpenRouterApiKey(),
            default => null,
        };
    }

    public function getMeta($key, $default = null)
    {
        return $this->meta[$key] ?? $default;
    }

    public function entries(): HasMany
    {
        return $this->hasMany(ResearchCampaignEntry::class);
    }
    public function popularBooksInNicheEntries(): HasMany
    {
        return $this->hasMany(PopularBookInNicheEntry::class);
    }

    public function addErrorToMeta(string $errorKey, string $errorMessage): void
    {
        // Retrieve the current meta data, or initialize an empty array if it doesn't exist
        $meta = $this->meta ?? [];

        // Check if the 'errors' key exists in the meta data
        if (!isset($meta['errors'])) {
            // If 'errors' does not exist, create it
            $meta['errors'] = [];
        }

        // Add the new error to the 'errors' array
        $meta['errors'][$errorKey] = $errorMessage;

        // Save the updated meta data
        $this->meta = $meta;
        $this->save();
    }
}
