<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EbookFormat extends Model
{
    use HasFactory;

    protected $guarded = ["id"];
    //boot on update,save
    protected static function boot(): void
    {
        parent::boot();
        static::saving(function (EbookFormat $format) {
            try {
                if (json_decode(request()->components[0]["snapshot"])->data->data[0]->background_image ?? "") {
                    $backgroundImage = json_decode(request()->components[0]["snapshot"])->data->data[0]->background_image;
                    $format->background_image = $backgroundImage=='none'?null:$backgroundImage;
                    $format->background_opacity = json_decode(request()->components[0]["snapshot"])->data->data[0]->background_opacity;
                    $format->withoutEvents(function () use ($format) {
                        $format->save();
                    });
                }
            } catch (\Exception $e) {
                //ignore
            }
        });
    }

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'background_opacity' => 'float',
    ];

    /**
     * Get the background opacity value with a default of 1.0 if not set
     *
     * @return float
     */
    public function getBackgroundOpacityAttribute($value)
    {
        return $value ?? 1.0;
    }
}
