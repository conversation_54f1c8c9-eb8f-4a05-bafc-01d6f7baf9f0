<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BackgroundImage extends Model
{
    use HasFactory;
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'opacity' => 'float',
        'is_admin' => 'boolean',
    ];

    /**
     * Get the opacity value with a default of 1.0 if not set
     *
     * @return float
     */
    public function getOpacityAttribute($value)
    {
        return $value ?? 1.0;
    }

    /**
     * Get the user that owns the background image.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
