<?php

namespace App\Models;

use App\Enum\CampaignStatusEnum;
use App\Traits\HasLogger;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Section extends Model
{
    use HasFactory, HasLogger, SoftDeletes;

    protected $casts = [
        'status' => CampaignStatusEnum::class,
        'meta' => 'json'
    ];

    protected $guarded = ["id"];

    protected $fillable = [
        'chapter_id',
        'title',
        'intro',
        'key_points',
        'body',
        'section_total_words',
        'status',
        'meta',
        'order',
        'chapter_section_number'
    ];

    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    public function getPlainTitleAttribute(): string
    {
        return strip_tags($this->title);
    }

    public function getSectionQueueName(): string
    {
        if (app()->environment('local')) {
            return (string) 'section-1';
        }
        return (string) 'section-'.($this->id % 10);
    }

    public function getMeta($key, $default = null)
    {
        return $this->meta[$key] ?? $default;
    }
}
