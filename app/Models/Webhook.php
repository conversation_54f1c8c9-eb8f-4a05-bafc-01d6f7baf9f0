<?php

namespace App\Models;

use App\Traits\HasLogger;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;

class Webhook extends Model
{
    use HasFactory;
    use HasLogger;

    protected $guarded = ["id"];

    protected $casts = [
        'payload' => 'json',
    ];

    public function payload()
    {
        return optional($this->payload);
    }

    function getSidAttribute()
    {
        return Arr::get($this->payload, 'WP_SID');
    }

    function getModeAttribute()
    {
        return Arr::get($this->payload, 'WP_ACTION');
    }

    function getEmailAttribute()
    {
        return Arr::get($this->payload, 'WP_BUYER_EMAIL');
    }

    function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function getItemAttribute()
    {
        return Arr::get($this->payload, 'WP_ITEM_NAME');
    }

    public function getAmountAttribute()
    {
        return Arr::get($this->payload, 'WP_SALE_AMOUNT') . ' ' . Arr::get($this->payload, 'WP_SALE_CURRENCY');
    }

    public function getDateAttribute()
    {
        return $this->created_at->format('d/m/Y');
    }

}
