<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Template extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected static function boot(): void
    {
        parent::boot();

        static::updating(function ($template) {
            if ($template->isDirty('is_default') && $template->getOriginal('is_default') == 0 && $template->is_default == 1) {
                Template::where('id', '!=', $template->id)
                    ->update(['is_default' => 0]);
            }
        });
    }
}
