<?php

namespace App\Models;

use App\Enum\CampaignStatusEnum;
use App\Jobs\OptimizeContentImagesJob;
use App\Traits\HasLogger;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Chapter extends Model
{
    use HasFactory, HasLogger, SoftDeletes;

    protected $casts = [
        'status' => CampaignStatusEnum::class,
        'meta' => 'json',
    ];

    protected $guarded = ["id"];

    protected static function boot(): void
    {
        parent::boot();
        static::updated(function (Chapter $chapter) {
           if($chapter->isDirty('intro') && str_contains($chapter->intro, 'optimized=false')) {
               //run job optimizecontentimage
               OptimizeContentImagesJob::dispatch($chapter);
            }
        });
    }
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    public function sections(): HasMany
    {
        return $this->hasMany(Section::class)->orderBy('order');
    }

    public function getPlainTitleAttribute(): string
    {
        return strip_tags($this->title);
    }

    function getChapterQueueName(): string
    {
        if (app()->environment('local')) {
            return (string) 'chapter-1';
        }
        return (string) 'chapter-'.($this->id % 10);
    }


    public function getMeta($key, $default = null)
    {
        try {
            return $this->meta[$key] ?? $default;
        } catch (\Exception $e) {
            $this->log("Failed to get meta. " . formatLogMessage($e->getMessage()));
            return $default;
        }
    }

    public function saveMeta($key, $value): void
    {
        try {
            $meta = $this->meta ?? [];
            $meta[$key] = $value;
            $this->meta = $meta;
            $this->save();
        } catch (\Exception $e) {
            $this->log("Failed to save meta. " . formatLogMessage($e->getMessage()));
        }
    }

    public function getAudioUrl(?string $model = null): ?string
    {
        $s3Path = $this->getAudioS3Path($model);
        if (!$s3Path) {
            return null;
        }

        // Generate secure download URL through our controller
        $params = ['chapter' => $this->id];
        if ($model) {
            $params['model'] = $model;
        }
        return route('download.chapter.audio.single', $params);
    }

    public function getAudioS3Path(?string $model = null): ?string
    {
        if ($model) {
            $audioModels = $this->getMeta('audio_models', []);
            return $audioModels[$model]['s3_path'] ?? null;
        }

        // If no model specified, return the first available audio
        $audioModels = $this->getMeta('audio_models', []);
        if (empty($audioModels)) {
            // Fallback to old format for backward compatibility
            return $this->getMeta('audio_s3_path');
        }

        return array_values($audioModels)[0]['s3_path'] ?? null;
    }

    public function hasAudio(?string $model = null): bool
    {
        if ($model) {
            $audioModels = $this->getMeta('audio_models', []);
            return !empty($audioModels[$model]['s3_path'] ?? null);
        }

        // Check if any audio model exists
        $audioModels = $this->getMeta('audio_models', []);
        if (!empty($audioModels)) {
            return true;
        }

        // Fallback to old format for backward compatibility
        return !empty($this->getMeta('audio_s3_path'));
    }

    public function getAvailableAudioModels(): array
    {
        $audioModels = $this->getMeta('audio_models', []);

        // Include backward compatibility
        if (empty($audioModels) && $this->getMeta('audio_s3_path')) {
            $voice = $this->getMeta('audio_voice', 'unknown');
            return [$voice];
        }

        return array_keys($audioModels);
    }

    public function getAudioModelInfo(string $model): ?array
    {
        $audioModels = $this->getMeta('audio_models', []);
        return $audioModels[$model] ?? null;
    }

    public function getAudioVoice(): ?string
    {
        return $this->getMeta('audio_voice');
    }

    public function getAudioGeneratedAt(): ?string
    {
        return $this->getMeta('audio_generated_at');
    }
}
