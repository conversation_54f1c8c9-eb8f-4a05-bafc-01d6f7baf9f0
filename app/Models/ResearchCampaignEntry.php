<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ResearchCampaignEntry extends Model
{
    use HasFactory;

    protected $guarded = ["id"];

    protected $casts = [
        'entry_content' => 'array',
    ];

    public function toggleFavorite(?bool $toggle = null)
    {
        $favotire = empty($toggle) ? !$this->favorite : $toggle;
        return $this->update(['favorite' => $favotire]);
    }

    function researchEbookCampaign(): BelongsTo
    {
        return $this->belongsTo(ResearchEbookCampaign::class, 'research_ebook_campaign_id');
    }
}
