<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PopularBookInNicheEntry extends Model
{
    use HasFactory;

    protected $guarded = ["id"];

    protected $casts = [
        "key_takeways" => "array",
    ];

    function researchEbookCampaign(): BelongsTo
    {
        return $this->belongsTo(ResearchEbookCampaign::class, 'research_ebook_campaign_id');
    }
    
    public function toggleFavorite(?bool $toggle = null)
    {
        $favorite = empty($toggle) ? !$this->favorite : $toggle;
        return $this->update(['favorite' => $favorite]);
    }
}
