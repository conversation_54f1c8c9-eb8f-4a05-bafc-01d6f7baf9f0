<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SiteSetting extends Model
{
    use HasFactory;

    protected $guarded = ["id"];
    protected $casts = [
        'status' => 'boolean',
        'payload' => 'array',
    ];

    public static function themeColors()
    {
        return config('site-settings')['theme_colors'] ?? [];
    }

    public static function setCachedSettings()
    {
        try {
            $settings = Cache::rememberForever('site-settings', function () {
                return SiteSetting::all()->pluck('payload', 'name')->toArray();
            });
            config(['site-settings' => $settings]);
            config(['site-features' => $settings['features'] ?? []]);
            config(['site-campaign-types' => $settings['campaign_types'] ?? []]);
        } catch (\Exception $e) {
            logger($e);
        }
    }

    static function getSalesPageUrl()
    {
        return 'https://ebookwriter.ai';
    }

   static function getFavicon(): string
   {
        return asset('favicon.png');
   }
}
