<?php

namespace App\Models;

use App\Enum\CreditLogActionEnum;
use App\Enum\CreditLogActionTypeEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CreditLog extends Model
{
    use HasFactory, Prunable;

    protected $guarded = ['id'];

    protected $casts = [
        'meta' => 'array',
        'action' => CreditLogActionEnum::class,
        'action_type' => CreditLogActionTypeEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::where('created_at', '<=', now()->subMonth());
    }
}
