<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;

class Subscription extends Model
{
    use HasFactory;
    protected $guarded = ["id", "created_at", "updated_at"];
    protected $casts = [
        'meta' => 'array',
        'subscribed_at' => 'datetime',
        'subscription_updated_at' => 'datetime',
        'renew_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    function getPlanNameAttribute()
    {
        return $this->plan?->name;
    }


    function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }
    function getManagePageUrlAttribute(): string
    {
        if (!$this->wplus_purchase_id) {
            return 'https://warriorplus.com/account/purchases';
        }

        return "https://warriorplus.com/buy/subscription/" . str_replace('sale_', '', $this->wplus_purchase_id);
    }
    function webhooks(): HasMany
    {
        return $this->hasMany(Webhook::class);
    }
    function getMeta($key, $default = null)
    {
        return Arr::get($this->meta, $key, $default);
    }
    function getContentQuota()
    {
        return $this->plan?->name == 'Credit Refund' ? ($this->meta['content_quota'] ?? 0) : $this->plan?->content_quota;
    }
}
