<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class UserUpdateNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'app_update_id',
        'is_read',
        'email_sent',
        'read_at',
        'email_sent_at',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'email_sent' => 'boolean',
        'read_at' => 'datetime',
        'email_sent_at' => 'datetime',
    ];

    /**
     * Get the user that owns the notification
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the app update for this notification
     */
    public function appUpdate(): BelongsTo
    {
        return $this->belongsTo(AppUpdate::class);
    }

    /**
     * Scope to get unread notifications
     */
    public function scopeUnread(Builder $query): Builder
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope to get read notifications
     */
    public function scopeRead(Builder $query): Builder
    {
        return $query->where('is_read', true);
    }

    /**
     * Scope to get notifications where email was sent
     */
    public function scopeEmailSent(Builder $query): Builder
    {
        return $query->where('email_sent', true);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(): void
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    /**
     * Mark email as sent
     */
    public function markEmailAsSent(): void
    {
        if (!$this->email_sent) {
            $this->update([
                'email_sent' => true,
                'email_sent_at' => now(),
            ]);
        }
    }

    /**
     * Create notification for user and update
     */
    public static function createForUserAndUpdate(User $user, AppUpdate $update): self
    {
        return static::firstOrCreate(
            [
                'user_id' => $user->id,
                'app_update_id' => $update->id,
            ],
            [
                'is_read' => false,
                'email_sent' => false,
            ]
        );
    }

    /**
     * Get unread count for user
     */
    public static function getUnreadCountForUser(User $user): int
    {
        return static::where('user_id', $user->id)
            ->unread()
            ->count();
    }
}
