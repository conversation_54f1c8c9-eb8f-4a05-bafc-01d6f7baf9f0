<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Listeners\AssignTicket;
use App\Listeners\CreateNewUserListener;
use App\Listeners\SendNewUserCredentialsByEmail;
use App\Listeners\SendTicketAssignmentNotification;
use App\Listeners\SendTicketNewCommentEmail;
use App\Listeners\SendTicketNewResponseEmail;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        \Sgcomptech\FilamentTicketing\Events\NewTicket::class => [
            AssignTicket::class,
        ],
        \Sgcomptech\FilamentTicketing\Events\NewAssignment::class => [
            SendTicketAssignmentNotification::class,
        ],
        \Sgcomptech\FilamentTicketing\Events\NewComment::class => [
            SendTicketNewCommentEmail::class,
        ],
        \Sgcomptech\FilamentTicketing\Events\NewResponse::class => [
            SendTicketNewResponseEmail::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
