<?php

namespace App\Mail;

use App\Models\Subscription;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class WelcomeEmail extends Mailable
{
    use Queueable, SerializesModels;

    public Subscription $subscription;
    public User $user;
    public ?string $password;

    public function __construct(Subscription $subscription, ?User $user = null, $password = null)
    {
        $this->subscription = $subscription;
        $this->user = $user ?: $this->subscription->user;
        $this->password = $password;
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Welcome Aboard, '.$this->user->first_name.'! Your '.config('app.name').' Credentials',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.welcome-email',
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
