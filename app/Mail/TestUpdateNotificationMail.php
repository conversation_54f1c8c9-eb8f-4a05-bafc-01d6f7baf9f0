<?php

namespace App\Mail;

use App\Models\AppUpdate;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class TestUpdateNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public AppUpdate $update;
    public string $testEmail;
    public string $testName;

    /**
     * Create a new message instance.
     */
    public function __construct(AppUpdate $update, string $testEmail, string $testName = 'Test User')
    {
        // Validate required parameters
        if (!$update) {
            throw new \InvalidArgumentException('Update parameter is required');
        }

        if (!$testEmail) {
            throw new \InvalidArgumentException('Test email is required');
        }

        // Additional validation for update
        if (!$update->title || !$update->version) {
            throw new \InvalidArgumentException('Update must have title and version');
        }

        $this->update = $update;
        $this->testEmail = $testEmail;
        $this->testName = $testName;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $appName = config('app.name', 'eBookWriter');
        $emoji = $this->getUpdateEmoji();

        return new Envelope(
            subject: "[TEST] {$emoji} New Update Available! {$appName} v{$this->update->version}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        // Create test user data
        $testUser = (object) [
            'name' => $this->testName,
            'email' => $this->testEmail,
            'first_name' => explode(' ', $this->testName)[0] ?? $this->testName,
        ];

        // Create test preferences
        $testPreferences = (object) [
            'unsubscribe_token' => 'test-token-preview',
        ];

        return new Content(
            markdown: 'mail.test-update-notification',
            with: [
                'update' => $this->update,
                'user' => $testUser,
                'preferences' => $testPreferences,
                'updateTypeDisplayName' => $this->getUpdateTypeDisplayName(),
                'categoryDisplayName' => $this->getCategoryDisplayName(),
                'unsubscribeUrl' => '#', // Placeholder for test
                'isTest' => true,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get emoji for update type
     */
    private function getUpdateEmoji(): string
    {
        return match($this->update->type) {
            'major' => '🎉',
            'minor' => '✨',
            'patch' => '🔧',
            'security' => '🔒',
            default => '📢'
        };
    }

    /**
     * Get update type display name
     */
    public function getUpdateTypeDisplayName(): string
    {
        return match($this->update->type) {
            'major' => 'Major Update',
            'minor' => 'Feature Update',
            'patch' => 'Bug Fix',
            'security' => 'Security Update',
            default => 'Update'
        };
    }

    /**
     * Get category display name
     */
    public function getCategoryDisplayName(): string
    {
        return match($this->update->category) {
            'features' => 'New Features',
            'fixes' => 'Bug Fixes',
            'improvements' => 'Improvements',
            'security' => 'Security',
            default => 'General'
        };
    }
}
