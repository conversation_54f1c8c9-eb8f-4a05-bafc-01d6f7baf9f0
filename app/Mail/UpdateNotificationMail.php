<?php

namespace App\Mail;

use App\Models\AppUpdate;
use App\Models\User;
use App\Models\UserUpdatePreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class UpdateNotificationMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public AppUpdate $update;
    public User $user;
    public UserUpdatePreference $preferences;

    /**
     * Create a new message instance.
     */
    public function __construct(AppUpdate $update, User $user, UserUpdatePreference $preferences)
    {
        // Validate required parameters
        if (!$update) {
            throw new \InvalidArgumentException('Update parameter is required');
        }

        if (!$user) {
            throw new \InvalidArgumentException('User parameter is required');
        }

        if (!$preferences) {
            throw new \InvalidArgumentException('Preferences parameter is required');
        }

        // Additional validation for update
        if (!$update->title || !$update->version) {
            throw new \InvalidArgumentException('Update must have title and version');
        }

        $this->update = $update;
        $this->user = $user;
        $this->preferences = $preferences;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        if (!$this->update) {
            throw new \RuntimeException('Update is not set');
        }

        $appName = config('app.name', 'eBookWriter');
        $emoji = $this->getUpdateEmoji();

        return new Envelope(
            subject: "{$emoji} New Update Available! {$appName} v{$this->update->version}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'mail.update-notification',
            with: [
                'update' => $this->update,
                'user' => $this->user,
                'preferences' => $this->preferences,
                'updateTypeDisplayName' => $this->getUpdateTypeDisplayName(),
                'categoryDisplayName' => $this->getCategoryDisplayName(),
                'unsubscribeUrl' => $this->getUnsubscribeUrl(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get emoji for update type
     */
    private function getUpdateEmoji(): string
    {
        return match($this->update->type) {
            'major' => '🎉',
            'minor' => '✨',
            'patch' => '🔧',
            'security' => '🔒',
            default => '📢'
        };
    }

    /**
     * Get update type display name
     */
    public function getUpdateTypeDisplayName(): string
    {
        return match($this->update->type) {
            'major' => 'Major Update',
            'minor' => 'Feature Update',
            'patch' => 'Bug Fix',
            'security' => 'Security Update',
            default => 'Update'
        };
    }

    /**
     * Get category display name
     */
    public function getCategoryDisplayName(): string
    {
        return match($this->update->category) {
            'features' => 'New Features',
            'fixes' => 'Bug Fixes',
            'improvements' => 'Improvements',
            'security' => 'Security',
            default => 'General'
        };
    }

    /**
     * Get unsubscribe URL
     */
    public function getUnsubscribeUrl(): string
    {
        return route('update-notifications.unsubscribe', $this->preferences->unsubscribe_token);
    }
}
