<?php

namespace App\Traits;

use App\Service\CreditLogsActivity;
use App\Enum\CreditLogActionEnum;
use App\Models\User;

trait HasCreditLog
{
    /**
     * Log credit activity for a specific action.
     *
     * @param User $user
     * @param CreditLogActionEnum $action
     * @param float $credit
     * @param bool $alreadyDeduct
     * @param string $description
     *
     * @return void
     */
    public function logCreditActivity(
        User $user,
        CreditLogActionEnum $action,
        float $credit = 0,
        string $description = '',
        string $descPrefix = '',
        bool $alreadyDeduct = false,
        array $logMeta = []
    ): void
    {
        $logActivity = new CreditLogsActivity(
            user: $user,
            action: $action,
            description: $description,
            descPrefix: $descPrefix,
            logMeta: $logMeta,
        );

        match ($action) {
            CreditLogActionEnum::CAMPAIGN, CreditLogActionEnum::GATEWAY_REFUND => $logActivity->deductCredit(credit: $credit, alreadyDeduct: $alreadyDeduct),
            CreditLogActionEnum::BILLING, CreditLogActionEnum::REFUND => $logActivity->addCredit(credit: $credit, alreadyDeduct: $alreadyDeduct),
        };
    }
}
