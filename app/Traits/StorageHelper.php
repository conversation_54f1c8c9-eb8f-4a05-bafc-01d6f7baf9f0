<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Models\Download;

trait StorageHelper
{
    protected static function getValidCoverImageUrl($record): ?string
    {
        if (self::isValidS3FileUrl($record->cover_image)) {
            return $record->cover_image;
        }

        if (self::isValidS3FileUrl(optional($record->campaign)->cover_image)) {
            return $record->campaign->cover_image;
        }

        return null;
    }

    protected static function isValidS3FileUrl(?string $url): bool
    {
        // If the URL is empty, return false
        if (blank($url)) {
            return false;
        }

        // Check if it's a valid URL
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            // It's a URL, check if it's an S3 URL and cache the result
            return self::isS3Url($url);
        }

        // It's likely an S3 path, parse and check it
        $path = self::parseStoragePathFromUrl($url);

        // If path is null or invalid, return false
        if (blank($path)) {
            return false;
        }

        // Cache the S3 existence check for 6 hours
        return Cache::remember("s3.exists:$path", now()->addHours(6), function () use ($path) {
            return Storage::disk('s3')->exists($path); // Check if file exists in S3
        });
    }

    /**
     * Check if the URL is a valid S3 URL.
     */
    protected static function isS3Url(string $url): bool
    {
        // Define the base URL for your S3 domain (this will be used to check if the URL matches)
        $s3Base = 'https://aiebook.nyc3.digitaloceanspaces.com/';

        // If the URL starts with the S3 base, extract the file path
        if (str_starts_with($url, $s3Base)) {
            // Extract the S3 path from the URL
            $path = ltrim(str_replace($s3Base, '', $url), '/');

            // Cache the result for 6 hours
            return Cache::remember("s3.exists:$path", now()->addHours(6), function () use ($path) {
                return Storage::disk('s3')->exists($path); // Check if the file exists in S3
            });
        }

        // If it's not an S3 URL, return false
        return false;
    }

    /**
     * Parse the S3 file path from the URL.
     */
    protected static function parseStoragePathFromUrl(string $url): ?string
    {
        // Customize this according to your space domain setup
        $base = 'https://aiebook.nyc3.digitaloceanspaces.com/';

        if (str_starts_with($url, $base)) {
            // Extract the path after the base URL
            return ltrim(str_replace($base, '', $url), '/');
        }

        // If it doesn't match the base, return null (it's a path)
        return $url;  // Return the path if it's not a URL
    }
    
    protected function getTemporaryUrl(Download $record): string
    {
        return Storage::disk('s3')->temporaryUrl($record->path,
            now()->addHours(2)
        );
    }
}

