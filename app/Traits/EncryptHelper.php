<?php

namespace App\Traits;

trait EncryptHelper
{
    protected static string $method = 'AES-128-ECB';
    protected static ?string $key = null;

    public static function init(): void
    {
        self::$key = config('app.key');
    }

    public static function encrypt($input): string
    {
        self::ensureKey();
        $encrypted = openssl_encrypt((string)$input, self::$method, self::$key, OPENSSL_RAW_DATA);
        return rtrim(strtr(base64_encode($encrypted), '+/', '-_'), '=');
    }

    public static function decrypt($encoded): string
    {
        self::ensureKey();
        $data = base64_decode(strtr($encoded, '-_', '+/'));
        return openssl_decrypt($data, self::$method, self::$key, OPENSSL_RAW_DATA);
    }

    protected static function ensureKey(): void
    {
        if (!self::$key) {
            self::$key = config('app.key');
        }
    }
}

