<?php

namespace App\Actions;

use App\Enum\CampaignTypeEnum;
use App\Models\Campaign;
use App\Service\AIModel\Contracts\AIClientInterface;

class SummarizeContentAction
{
    public function execute(string $content, Campaign $campaign)
    {
        try {
            $aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            $data = [
                'content' => $content,
            ];

            $wordCount = str_word_count(strip_tags($content));
            if ($wordCount < 500 && $campaign->type == CampaignTypeEnum::INFORMATIONAL_POST->value) {
                $data['url'] = $campaign->getForm('content_url');
            }

            $campaign->log("Content summary prompt data" . json_encode($data));

            $prompt = array_key_exists('url', $data) ?
                promptBuilder(prompts()['content_summary_by_url'], $data) :
                promptBuilder(prompts()['content_summary'], $data);

            $prompt .= prompts()['ignore_terms'];

            $campaign->log("Campaign content url summary prompt" . formatLogMessage($prompt));

            $summary = $aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model));

            $campaign->log("Generated content summary: " . formatLogMessage($summary));
            return formatAIResponse($summary)["summary"] ?? "";

        } catch (\Exception $e){
            $campaign->log("Failed to generate content summary from AI model. " . formatLogMessage($e->getMessage()));
            return '';
        }
    }
}
