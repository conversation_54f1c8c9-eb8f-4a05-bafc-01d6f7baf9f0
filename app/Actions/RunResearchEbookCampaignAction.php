<?php

namespace App\Actions;

use App\Enum\CampaignStatusEnum;
use App\Jobs\DispatchChapterSectionsJob;
use App\Jobs\GenerateCampaignContextJob;
use App\Jobs\GenerateEbookChaptersJob;
use App\Jobs\GenerateEbookTitleJob;
use App\Jobs\ResearchEbookCampaignJob;
use App\Models\Campaign;
use App\Models\ResearchEbookCampaign;
use App\Service\CalculateEbookWordFormulaService;
use App\Service\CampaignImageProcessingJobService;
use Illuminate\Support\Facades\Bus;

class RunResearchEbookCampaignAction
{
    public function execute(ResearchEbookCampaign $campaign, bool $retry = false): void
    {
        if (!$campaign->getOpenAiApiKey()){
            $campaign->log("OpenAI api key not found.");
            $campaign->update([
                "status" => CampaignStatusEnum::FAILED
            ]);
            return;
        }

        $campaign->log("Marking campaign status IN PROGRESS for campaign: {$campaign->id}");

        $this->markCampaignInProgress($campaign);

        if ($retry) {
            $campaign->log("Campaign retry condition meet true and dispatching campaign job: {$campaign->id}");
            ResearchEbookCampaignJob::dispatch($campaign->id)->onQueue("research_ebook_campaign");
            return;
        }

        ResearchEbookCampaignJob::dispatch($campaign->id)->onQueue("research_ebook_campaign");
    }

    private function markCampaignInProgress(ResearchEbookCampaign $campaign): void
    {
        $campaign->update(['status' => CampaignStatusEnum::IN_PROGRESS]);
    }

}
