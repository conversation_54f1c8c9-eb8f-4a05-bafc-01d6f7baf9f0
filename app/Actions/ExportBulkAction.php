<?php

namespace App\Actions;

use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Models\PopularBookInNicheEntry;
use App\Models\ResearchCampaignEntry;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\TrashedFilter;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component as LivewireComponent;

class ExportBulkAction extends BulkAction
{
    use CanCustomizeProcess;

    public static function getDefaultName(): ?string
    {
        return 'export';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->action(function (): void {
            $paa_entry_ids = [];
            $type = "";

            $this->process(static function (Collection $records, LivewireComponent $livewire) use (&$paa_entry_ids, &$type): void {
                $recordClass = self::getRecordClass($livewire->ownerRecord->type);
                $records = getBulkSelectedRecord(ResearchCampaignEntry::class, $records, $livewire);
                $paa_entry_ids = $records->pluck('id')->toArray();
                $type = $livewire->ownerRecord->type;
            });



            //livewire redirect to download csv

            $this->redirect(route('content.csv.download', ['ids' => implode(',', $paa_entry_ids), 'export_type' => $this->name, "type" => $type]));
        });

    }

    protected static function getRecordClass($type): ?string
    {
        return match ($type) {
            ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE => PopularBookInNicheEntry::class,
            ResearchEbookCampaignIdeaTypeEnum::BOOK_TITLE_GENERATION => ResearchCampaignEntry::class,
            ResearchEbookCampaignIdeaTypeEnum::BEST_KEYWORD_CATEGORIES_FOR_EBOOK => ResearchCampaignEntry::class,
            ResearchEbookCampaignIdeaTypeEnum::GENERATE_AUTHOR_BIO => ResearchCampaignEntry::class,
            default => throw new \Exception($type . " not available"),
        };
    }
}
