<?php

namespace App\Actions;

use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class RecordFavoriteAction
{
    static function make()
    {
        return Action::make('toggle_favorite')
                     ->label(function ($record) {
                         return $record->favorite ? 'Unfavorite' : 'Favorite';
                     })
                     ->action(function ($record) {
                         $record->toggleFavorite();
                         $notify = $record->favorite ? 'Favorited' : 'Unfavorited';
                         Notification::make()->success()->title($notify)->send();
                     })
                     ->color('warning')
                     ->icon(function ($record) {
                         return $record->favorite ? 'heroicon-m-star' : 'heroicon-o-star';
                     });
    }
}
