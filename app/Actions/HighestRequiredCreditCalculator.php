<?php

namespace App\Actions;

use App\Enum\CreditDeductEnum;

class HighestRequiredCreditCalculator
{
    public static function getHighestRequiredCredits($pageLength)
    {
        $jsonPath = resource_path('ebook_plan/ebook_plan.json');

        if (!file_exists($jsonPath)) {
            throw new \RuntimeException("Ebook plan JSON file not found.");
        }

        $allPageLengths = json_decode(file_get_contents($jsonPath), true);

        if (!isset($allPageLengths[$pageLength])) {
            throw new \InvalidArgumentException("No plan found for page length: {$pageLength}");
        }

        $plan = $allPageLengths[$pageLength];

        $maxChapters = self::getMaxFromRangeString($plan['total_chapters']);
        $maxSectionsPerChapter = self::getMaxFromRangeString($plan['sections_per_chapter_range']);

        $totalSections = $maxChapters * $maxSectionsPerChapter;

        return
            ($maxChapters * CreditDeductEnum::EACH_CHAPTER_CREDIT->value) +
            ($totalSections * CreditDeductEnum::EACH_SECTION_CREDIT->value) +
            CreditDeductEnum::EBOOK_GENERATION_CREDIT->value;
    }

    protected static function getMaxFromRangeString(string $range): int
    {
        preg_match_all('/\d+/', $range, $matches);

        if (empty($matches[0])) {
            throw new \InvalidArgumentException("Invalid range string: {$range}");
        }

        return max(array_map('intval', $matches[0]));
    }
}
