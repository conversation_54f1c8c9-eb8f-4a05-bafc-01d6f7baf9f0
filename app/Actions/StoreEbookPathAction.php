<?php

namespace App\Actions;

use App\Models\Campaign;
use App\Models\Download;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class StoreEbookPathAction
{
    public function store(int $ebookId, string $ebookPath, string $key): void
    {
        $ebook = Campaign::find($ebookId);

        $ttlInSeconds = 604800; // 1 week, as per isS3FileExist default
        //Cache::forget($ebookPath);
        Cache::remember($ebookPath, $ttlInSeconds, function () use ($ebookPath) {
            return Storage::disk('s3')->exists($ebookPath);
        });

        //storing to Download
        $fileVersion = Download::where('type', $key)
                               ->where('campaign_id', $ebook->id)
                               ->count() + 1;


        Download::create([
            'type' => $key,
            "path" => $ebookPath,
            "cover_image" => $ebook->cover_image ?? null,
            'file_version' => $fileVersion,
            "user_id" => $ebook->user->id,
            "campaign_id" => $ebook->id,
        ]);

//        // Get existing URL data as an array, or use an empty array if it's null
//        $urlData = $ebook->url ?? [];
//
//        // Add or update the new key with the provided path
//        $urlData[$key] = $ebookPath;
//
//        // Update the model with the modified array
//        $ebook->update(['url' => $urlData]);
    }

}
