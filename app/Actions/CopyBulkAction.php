<?php

namespace App\Actions;

use App\Models\ResearchCampaignEntry;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component as LivewireComponent;

class CopyBulkAction extends BulkAction
{
    use CanCustomizeProcess;

    public static function getDefaultName(): ?string
    {
        return 'copy';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->action(function (): void {

            $keywords = (object)[];

            $this->process(static function (Collection $records, LivewireComponent $livewire) use (&$keywords): void {
                $records = getBulkSelectedRecord(ResearchCampaignEntry::class, $records, $livewire);
                $keywords = $records->pluck('entry');
            });

            $this->getLivewire()->js(view('scripts.copy-to-clipboard', ['text' => $keywords->join(PHP_EOL)])->render());

            $this->deselectRecordsAfterCompletion();

            Notification::make()->success()->title('Copied')->send();
        });
    }
}
