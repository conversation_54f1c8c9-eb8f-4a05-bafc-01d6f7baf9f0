<?php

namespace App\Actions;

use Campo\UserAgent;
use Illuminate\Support\Facades\Http;
class WebBrowserAction
{
    public function execute($url, $campaign = null)
    {
        $cacheKey = 'WebBrowser:'.$url;
        $html = null;

        try {
            //cache()->forget($cacheKey);
            $html = cache()->remember($cacheKey, now()->addDay(), function () use ($url) {
                return Http::withUserAgent(UserAgent::random())->get($url)->throw()->body();
            });
        } catch (\Exception $e) {
            $campaign->log('Webbrowser Exception : '.$e->getMessage());
            cache()->forget($cacheKey);
        }

        if (!$html) {
            try {
                $campaign->log('[USING_SCRAPEOWL_PROXY] Extracting from '.$url.' with proxy');

                $html = cache()->remember($cacheKey, now()->addDay(), function () use($url) {
                    return Http::withOptions(['proxy' => get_scrapeowl_proxy()])
                               ->withUserAgent(UserAgent::random())
                               ->timeout(300)
                               ->connectTimeout(60)
                               ->withoutVerifying()
                               ->get($url)
                               ->throw()
                               ->body();
                });
            } catch (\Exception $e) {
                $campaign->log("Failed to extract from ".$url.' '.$e->getMessage());
                cache()->forget($cacheKey);
            }
        }

        if (!$html) {
            try {
                $campaign->log('[USING_BROWSERLESS] Extracting from '.$url);

                $html = cache()->remember($cacheKey, now()->addDay(), function () use ($url) {
                    $query = http_build_query([
                        'token' => config('services.browserless.api_key'),
                        'blockAds' => true,
                        'stealth' => true,
                        'ignoreHTTPSErrors' => true,
                    ]);

                    $response = Http::timeout(300)->connectTimeout(60)->post('https://chrome.browserless.io/content?'.$query, [
                        'url' => $url,
                        'userAgent' => UserAgent::random(),
                    ])->throw();

                    if ($response->header('x-response-code') != 200) {
                        throw new \Exception('Failed to extract from '.$url.' x-response-code: '.$response->header('x-response-code'));
                    }

                    return $response->body();
                });
            } catch (\Exception $e) {
                $campaign->log('Exception : '.$e->getMessage());
                cache()->forget($cacheKey);
            }
        }

        return $html;
    }
}
