<?php

namespace App\Service;

use Illuminate\Support\Facades\Http;

class ScrapeOwl
{
    static function getWithMobile($url, $query)
    {
        return Http::scrapeowl()->post('/', [
            'url' => url_add_query($url, $query),
            'api_key' => config('services.scrapeowl.api_key'),
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Linux; Android 4.4.2; Nexus 4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36'
            ]
        ])->json('html');
    }

    static function get($url, $query = [])
    {
        return Http::scrapeowl()->post('/', [
            'url' => url_add_query($url, $query),
            'api_key' => config('services.scrapeowl.api_key'),
        ])->json('html');
    }
}

