<?php

namespace App\Service\AIModel;

use App\Enum\AIModelEnum;
use App\Enum\OpenAIErrorCodeEnum;
use App\Service\AIModel\Contracts\AIClientInterface;
use App\Service\AIModel\Contracts\ImageResponseHandlerInterface;
use App\Service\AIModel\ImageHandlers\DallEImageHandler;
use App\Service\AIModel\ImageHandlers\GptImageHandler;
use Illuminate\Support\Facades\Log;
use OpenAI as PHPOpenAI;
use OpenAI\Exceptions\ErrorException;


class OpenAIClient implements AIClientInterface
{
    private const EBOOK_IMAGES_DIR = 'ai_ebook_images/';
    private const COVER_IMAGES_DIR = 'ai_cover_images/';
    
    public function callAIModel(string $prompt, mixed $apiCredential, $model = "gpt-4o-mini", int $maxTokens = null): string
    {

        if (!$apiCredential){
            Log::debug("Open AI api key not found");
            throw new \Exception('Open AI api key not found.');
        }

        try {
            $openAI = PHPOpenAI::client($apiCredential);
            $response = $openAI->chat()->create([
                'model' => $model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => $prompt],
                ],
                'max_tokens' => $maxTokens, // Default to 50 if not provided
            ]);
            return $response['choices'][0]['message']['content'];

        } catch (ErrorException $e) {
            $statusCode = $e->getStatusCode();

            $userMessage = OpenAIErrorCodeEnum::from($statusCode)->getMessage();

            throw new \RuntimeException($userMessage);
        }
    }

    public function streamAIModel(string $prompt, mixed $apiCredential, callable $callback, $model = "gpt-4o-mini"): void
    {
        if (!$apiCredential) {
            Log::debug("Open AI api key not found");
            throw new \Exception('Open AI api key not found.');
        }

        try {
            Log::info("Starting OpenAI streaming request", [
                'model' => $model,
                'prompt_length' => strlen($prompt)
            ]);

            $openAI = PHPOpenAI::client($apiCredential);
            $stream = $openAI->chat()->createStreamed([
                'model' => $model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => $prompt],
                ],
                'stream' => true,
            ]);

            $chunkCount = 0;
            foreach ($stream as $response) {
                $chunkCount++;
                $content = $response->choices[0]->delta->content ?? '';
                if (!empty($content)) {
                    Log::debug("Received chunk", [
                        'chunk_number' => $chunkCount,
                        'content_length' => strlen($content)
                    ]);
                    $callback($content);
                }
            }

            Log::info("OpenAI streaming completed", [
                'total_chunks' => $chunkCount
            ]);
        } catch (\Exception $e) {
            Log::error("Error streaming OpenAI API", [
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            // Provide more specific error messages based on the exception type
            if (strpos($e->getMessage(), 'API key') !== false) {
                throw new \RuntimeException('Invalid OpenAI API key. Please check your API key configuration.');
            } elseif (strpos($e->getMessage(), 'quota') !== false || strpos($e->getMessage(), 'rate limit') !== false) {
                throw new \RuntimeException('OpenAI API quota exceeded or rate limit reached. Please try again later.');
            } elseif (strpos($e->getMessage(), 'network') !== false || strpos($e->getMessage(), 'connection') !== false) {
                throw new \RuntimeException('Network connection error. Please check your internet connection and try again.');
            } else {
                throw new \RuntimeException('There was an issue communicating with the OpenAI service: ' . $e->getMessage());
            }
        }
    }

    public function generateImage(string $prompt, $apiCredential, $model = "dall-e-3", string $size = '1024x1024', int $n = 1)
    {
        try {
            $openAI = PHPOpenAI::client($apiCredential);

            // Build base payload
            $payload = [
                'model' => $model,
                'prompt' => $prompt,
                'n' => $n,
                'size' => $size,
            ];

            // Add response_format only if using DALL·E
            if ($model === "dall-e-3") {
                $payload['response_format'] = 'url';
            }

            $response = $openAI->images()->create($payload);

            return $this->processImageResponse($response->toArray(), $model, self::EBOOK_IMAGES_DIR);

        } catch (\Exception $e) {
            Log::error("Image generation failed: " . $e->getMessage());
            throw new \RuntimeException('There was an issue generating the image. ' . $e->getMessage());
        }
    }

    private function processImageResponse(array $response, string $model, string $directory): string
    {
        $handler = $this->getImageResponseHandler($model);
        return $handler->handle($response, $directory);
    }

    private function getImageResponseHandler(string $model): ImageResponseHandlerInterface
    {
        return match ($model) {
            'dall-e-3' => new DallEImageHandler(),
            'gpt-image-1' => new GptImageHandler(),
            default => throw new \RuntimeException("Unsupported model: $model")
        };
    }

    public function generateCoverImage(string $prompt, $apiCredential, string $size = '1024x1536', $userAiModel = 'dall-e-3', int $n = 1)
    {
        try {
            $model = match (true) {
                $size === "1024x1792" => "dall-e-3",
                $userAiModel === AIModelEnum::GPT_IMAGE_1->value => "gpt-image-1",
                default => "dall-e-3"
            };

            $openAI = PHPOpenAI::client($apiCredential);

            // Build base payload
            $payload = [
                'model' => $model,
                'prompt' => $prompt,
                'n' => $n,
                'size' => $size,
            ];

            // Add response_format only if using DALL·E
            if ($model === "dall-e-3") {
                $payload['response_format'] = 'url';
            }

            $response = $openAI->images()->create($payload);

            return $this->processImageResponse($response->toArray(), $model, self::COVER_IMAGES_DIR);

        } catch (\Exception $e) {
            Log::error("Cover image generation failed: " . $e->getMessage());
            throw new \RuntimeException('There was an issue generating the cover image. ' . $e->getMessage());
        }
    }


    public function generateAudio(string $text, $apiCredential, string $voice = 'alloy', string $model = 'tts-1', string $format = 'mp3')
    {
        try {
            if (!$apiCredential) {
                Log::debug("OpenAI API key not found");
                throw new \Exception('OpenAI API key not found.');
            }

            $openAI = PHPOpenAI::client($apiCredential);
            $response = $openAI->audio()->speech([
                'model' => $model,
                'input' => $text,
                'voice' => $voice,
                'response_format' => $format,
            ]);
            // dd($response);
            // if (!isset($response['data'])) { // Correct way to check if response contains valid data
            //     Log::error("Invalid response from OpenAI API: " . json_encode($response));
            //     throw new \RuntimeException('Failed to generate audio. Please try again later.');
            // }

            return $response; // Returns binary audio data

        } catch (\Exception $e) {
            Log::error("Error generating audio with OpenAI API: " . $e->getMessage());
            throw new \RuntimeException('There was an issue generating the audio. Please try again later.');
        }
    }

}
