<?php

namespace App\Service\AIModel\ImageHandlers;

use Illuminate\Support\Facades\Log;

class GptI<PERSON>Handler extends AbstractImageHandler
{
    /**
     * Handle GPT image response (base64 format)
     *
     * @param array $response OpenAI API response
     * @param string $directory S3 directory to store the image
     * @return string S3 URL of the uploaded image
     * @throws \RuntimeException
     */
    public function handle(array $response, string $directory): string
    {
        $this->validateResponse($response, 'b64_json');
        
        $base64Data = $response['data'][0]['b64_json'];
        Log::info("Generated GPT image (base64 format)");
        
        // Decode base64 image data
        $imageData = base64_decode($base64Data);
        if ($imageData === false) {
            throw new \RuntimeException('Failed to decode base64 image data');
        }
        
        return $this->uploadToS3($imageData, $directory);
    }
}
