<?php

namespace App\Service\AIModel\ImageHandlers;

use App\Service\AIModel\Contracts\ImageResponseHandlerInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

abstract class AbstractImageHandler implements ImageResponseHandlerInterface
{
    private const TEMP_IMAGE_PREFIX = 'app/tmp_image_';
    private const IMAGE_EXTENSION = '.jpg';

    /**
     * Upload image data to S3 and return the URL
     *
     * @param string $imageData
     * @param string $directory
     * @return string S3 URL
     * @throws \RuntimeException
     */
    protected function uploadToS3(string $imageData, string $directory): string
    {
        // Save to temp file
        $tempPath = storage_path(self::TEMP_IMAGE_PREFIX . uniqid() . self::IMAGE_EXTENSION);
        
        if (file_put_contents($tempPath, $imageData) === false) {
            throw new \RuntimeException('Failed to create temporary image file');
        }

        try {
            // Upload to S3
            $filename = $directory . uniqid() . self::IMAGE_EXTENSION;
            $uploadSuccess = Storage::disk('s3')->put($filename, fopen($tempPath, 'r+'), 'public');
            
            if (!$uploadSuccess) {
                throw new \RuntimeException('Failed to upload image to S3');
            }

            // Get public S3 URL
            $url = Storage::disk('s3')->url($filename);
            Log::info("Image uploaded to S3: $url");
            
            return $url;
            
        } finally {
            // Always clean up temp file
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }
        }
    }

    /**
     * Validate that response contains expected data
     *
     * @param array $response
     * @param string $expectedKey
     * @throws \RuntimeException
     */
    protected function validateResponse(array $response, string $expectedKey): void
    {
        if (!isset($response['data'][0][$expectedKey])) {
            Log::warning("No image data found in OpenAI response: " . json_encode($response));
            throw new \RuntimeException("Failed to generate image. No {$expectedKey} data returned.");
        }
    }
}
