<?php

namespace App\Service\AIModel\ImageHandlers;

use Illuminate\Support\Facades\Log;

class DallEImageHandler extends AbstractImageHandler
{
    /**
     * Handle DALL-E image response (URL format)
     *
     * @param array $response
     * @param string $directory
     * @return string S3 URL of the uploaded image
     * @throws \RuntimeException
     */
    public function handle(array $response, string $directory): string
    {
        $this->validateResponse($response, 'url');
        
        $imageUrl = $response['data'][0]['url'];
        Log::info("Generated DALL-E image URL: " . $imageUrl);
        
        // Download image from OpenAI URL
        $imageData = file_get_contents($imageUrl);
        if ($imageData === false) {
            throw new \RuntimeException('Failed to download image from OpenAI URL');
        }
        
        return $this->uploadToS3($imageData, $directory);
    }
}
