<?php

namespace App\Service\AIModel;

use App\Enum\AIModelEnum;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use GuzzleHttp\Client;


class OpenRouterClient implements AIClientInterface
{
    private string $url = "https://openrouter.ai/api/v1/chat/completions";
    public function callAIModel(string $prompt, mixed $apiCredential, $model = "openai/gpt-4o", int $maxTokens = null): string
    {
        if (!$apiCredential){
            Log::debug("OpenRouter api key not found");
            throw new \Exception('OpenRouter api key not found.');
        }

        $model = str_starts_with($model, AIModelEnum::OPEN_ROUTER->value . '-') ? str_replace('openrouter-', '', $model) : $model;

        $formattedPrompt = [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $prompt,
                ],
                [
                    'role' => 'user',
                    'content' => $prompt,
                ],
            ],
        ];

        try {
            $httpClient = new Client([
                'timeout' => 300,
            ]);
            $response = $httpClient->post($this->url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiCredential,
                    'Content-Type' => 'application/json',
                ],
                'json' => $formattedPrompt,
            ]);

            $body = $response->getBody()->getContents();
            $result = json_decode($body, true);

            // Extract the content from OpenRouter's response
            $content = $result['choices'][0]['message']['content'] ?? '';

            return $content; // Return the JSON string directly

        } catch (\Exception $e) {
            Log::error("Error calling OpenRouter API: " . $e->getMessage());
            throw new \RuntimeException('There was an issue communicating with the OpenRouter service. Please try again later.' . $e->getMessage());
        }finally {
            DB::disconnect(); // Ensure cleanup
        }
    }

}
