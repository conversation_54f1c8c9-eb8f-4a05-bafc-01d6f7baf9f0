<?php

namespace App\Service;

use App\Service\AIModel\Contracts\AIClientInterface;
use Symfony\Component\HttpFoundation\Response;

class CalculateEbookWordFormulaService
{
    // Map page sizes to approximate base words per page
    private array $pageSizeBaseWords = [
        'a4' => 290,      // Updated average base words per page
        'letter' => 280,  // Adjusted for letter size
        'legal' => 300,   // Adjusted for legal size
    ];

// Map font sizes to word adjustments
    private array $fontSizeAdjustments = [
        12 => 0,        // Baseline for 12 pt
        14 => -20,      // Slight reduction for larger font
        16 => -40,      // Further reduction
        18 => -60,      // Largest reduction
    ];

// Map line spacing to multipliers
    private array $lineSpacingMultipliers = [
        1.0 => 1.1,  // Slight increase for single spacing
        1.5 => 1.0,  // Baseline for 1.5 line spacing
        2.0 => 0.85, // Reduction for double spacing
    ];

// Preset margin adjustments
    private array $marginPresets = [
        'narrow' => ['top' => 0.5, 'bottom' => 0.5, 'left' => 0.5, 'right' => 0.5],
        'moderate' => ['top' => 1, 'bottom' => 1, 'left' => 1, 'right' => 1],
        'wide' => ['top' => 1.5, 'bottom' => 1.5, 'left' => 1.5, 'right' => 1.5],
    ];

    /**
     * Calculate the total words for the given settings and total pages.
     */
    public function calculateWords($settings, int $totalPages): int
    {
        $pageSize = strtolower($settings->page_size);
        $fontSize = (int) $settings->font_size;
        $lineSpacing = (float) $settings->line_space;
        $margins = $this->getMargins($settings->margins);

        // Get base words per page for the page size
        $baseWordsPerPage = $this->pageSizeBaseWords[$pageSize] ?? 280; // Default to A4 base if unknown

        // Adjust for font size
        $baseWordsPerPage += $this->fontSizeAdjustments[$fontSize] ?? 0;

        // Adjust for line spacing
        $baseWordsPerPage *= $this->lineSpacingMultipliers[$lineSpacing] ?? 1.0;

        // Adjust for margins
        $totalMargin = $margins['top'] + $margins['bottom'] + $margins['left'] + $margins['right'];
        if ($totalMargin > 4) {
            $baseWordsPerPage *= 0.85; // Reduce words for larger margins
        } elseif ($totalMargin <= 2) {
            $baseWordsPerPage *= 1.1;  // Increase words for smaller margins
        }

        // Calculate total words
        return $this->calculateTotalWords($baseWordsPerPage, $totalPages);
    }

    public function ebookPlan($campaign, $requiredWords)
    {
        $planData = ebookPlan($campaign->page_length);
        $campaign->update([
                "ebook_plan" => $planData,
            ]);

        $campaign->log("Formatted ebook plan response: " . formatLogMessage($planData));
//        try {
//            $aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
//            $data = [
//                'words' => $requiredWords,
//            ];
//
//            $campaign->log("Campaign planning prompt data" . json_encode($data));
//
//            $prompt = promptBuilder(prompts()['ebook_plan'], $data);
//
//            $campaign->log("Campaign planning prompt" . formatLogMessage($prompt));
//
//            $plan = $aiClient->callAIModel($prompt, $campaign->getOpenAiApiKey());
//
//            $campaign->log("Generated ebook plan: " . formatLogMessage($plan));
//            $planData = formatAIResponse($plan);
//            $campaign->update([
//                "ebook_plan" => $planData,
//            ]);
//            $campaign->log("Formatted ebook plan response: " . formatLogMessage($planData));
//
//        } catch (\Exception $e){
//            $campaign->log("Failed to generate ebook plan from AI model. " . formatLogMessage($e->getMessage()));
//        }
    }

    /**
     * Get margins from presets.
     */
    private function getMargins(string $preset): array
    {
        return $this->marginPresets[$preset] ?? $this->marginPresets['moderate'];
    }

    /**
     * Calculate total words based on words per page and total pages.
     */
    private function calculateTotalWords(float $wordsPerPage, int $totalPages): int
    {
        return (int) ($wordsPerPage * $totalPages);
    }
}
