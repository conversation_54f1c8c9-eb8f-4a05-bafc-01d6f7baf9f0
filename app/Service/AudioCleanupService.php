<?php

namespace App\Service;

use App\Models\Campaign;
use App\Models\Download;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AudioCleanupService
{
    /**
     * Safely delete S3 files and return results
     */
    public function deleteS3Files(array $files): array
    {
        $deletedFiles = [];
        $failedFiles = [];

        foreach ($files as $file) {
            try {
                if (Storage::disk('s3')->exists($file)) {
                    Storage::disk('s3')->delete($file);
                    $deletedFiles[] = $file;
                    Log::info("Successfully deleted S3 file: {$file}");
                } else {
                    Log::warning("File does not exist on S3: {$file}");
                }
            } catch (\Exception $e) {
                $failedFiles[] = $file;
                Log::error("Failed to delete S3 file {$file}: " . $e->getMessage());
            }
        }

        return [
            'deleted' => $deletedFiles,
            'failed' => $failedFiles
        ];
    }

    /**
     * Clean up expired audio files from S3 and downloads table
     */
    public function cleanupExpiredAudioCampaigns(string $audioDirectory = 'audio'): array
    {
        Log::info("Starting audio cleanup process for directory: {$audioDirectory}");

        // Get expiration days from correct config location
        $expirationDays = config('filesystems.cleanup.expiration_days', 10);
        Log::info("Using expiration days: {$expirationDays}");

        if ($expirationDays <= 0) {
            Log::warning("Invalid expiration days configuration: {$expirationDays}. Skipping cleanup.");
            return ['error' => 'Invalid expiration days configuration'];
        }

        // Get expired downloads (older than expiration days)
        $cutoffDate = Carbon::now()->subDays($expirationDays);
        $expiredDownloads = Download::where('type', 'mp3')
            ->where('created_at', '<', $cutoffDate)
            ->get();

        Log::info("Found " . $expiredDownloads->count() . " expired audio downloads");

        if ($expiredDownloads->isEmpty()) {
            Log::info("No expired audio downloads found.");
            return [
                'expired_downloads' => 0,
                'deleted_files' => 0,
                'updated_campaigns' => 0,
                'failed_deletions' => 0,
                'deleted_downloads' => 0
            ];
        }

        return $this->processExpiredDownloads($expiredDownloads);
    }

    /**
     * Process expired downloads - delete files and update campaigns
     */
    private function processExpiredDownloads($expiredDownloads): array
    {
        $expiredFiles = $expiredDownloads->pluck('path')->toArray();
        $campaignIds = $expiredDownloads->pluck('campaign_id')->unique()->toArray();

        Log::info("Processing " . count($expiredFiles) . " expired files for " . count($campaignIds) . " campaigns");

        // Delete S3 files
        $deletionResults = $this->deleteS3Files($expiredFiles);
        $deletedFiles = $deletionResults['deleted'];
        $failedDeletions = $deletionResults['failed'];

        // Update campaigns for successfully deleted files
        $updatedCampaigns = 0;
        if (!empty($deletedFiles)) {
            $updatedCampaigns = $this->updateCampaignsForDeletedFiles($deletedFiles, $campaignIds);
        }

        // Delete download records for successfully deleted files
        $deletedDownloads = 0;
        if (!empty($deletedFiles)) {
            $deletedDownloads = $this->deleteDownloadRecords($expiredDownloads, $deletedFiles);
        }

        $result = [
            'expired_downloads' => $expiredDownloads->count(),
            'deleted_files' => count($deletedFiles),
            'updated_campaigns' => $updatedCampaigns,
            'failed_deletions' => count($failedDeletions),
            'deleted_downloads' => $deletedDownloads
        ];

        Log::info("Audio cleanup completed: " . json_encode($result));
        return $result;
    }

    /**
     * Update campaigns for deleted audio files
     */
    private function updateCampaignsForDeletedFiles(array $deletedFiles, array $campaignIds): int
    {
        $updatedCount = 0;

        foreach ($campaignIds as $campaignId) {
            try {
                $campaign = Campaign::find($campaignId);
                if (!$campaign) {
                    Log::warning("Campaign not found: {$campaignId}");
                    continue;
                }

                $audioFileName = $campaign->getMeta('audio');
                if (!$audioFileName) {
                    continue;
                }

                // Check if this campaign's audio file was deleted
                $audioPath = 'audio/' . $audioFileName;
                if (in_array($audioPath, $deletedFiles)) {
                    $campaign->log("Audio file expired and deleted from S3: {$audioFileName}");
                    $campaign->saveMeta('audio', null);
                    $updatedCount++;
                    Log::info("Updated campaign {$campaign->id} - removed audio reference: {$audioFileName}");
                }
            } catch (\Exception $e) {
                Log::error("Failed to update campaign {$campaignId}: " . $e->getMessage());
            }
        }

        return $updatedCount;
    }

    /**
     * Delete download records for successfully deleted files
     */
    private function deleteDownloadRecords($expiredDownloads, array $deletedFiles): int
    {
        $deletedCount = 0;

        foreach ($expiredDownloads as $download) {
            try {
                if (in_array($download->path, $deletedFiles)) {
                    $download->delete();
                    $deletedCount++;
                    Log::info("Deleted download record {$download->id} for file: {$download->path}");
                }
            } catch (\Exception $e) {
                Log::error("Failed to delete download record {$download->id}: " . $e->getMessage());
            }
        }

        return $deletedCount;
    }
}
