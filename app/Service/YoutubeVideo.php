<?php

namespace App\Service;

use App\Service\YoutubeClient;
use Illuminate\Support\Facades\Http;

class YoutubeVideo
{
    public ?string $descriptionRaw = null;

    public function __construct(
        public ?string $id = null,
        public ?string $title = null,
        public ?string $description = null,
        public ?string $channel = null,
        public ?string $transcription = null
    ) {

    }

    public static function filterVideos(array $videos): array
    {
        return collect($videos)->map(function ($videoItem) {
            $id = $videoItem['snippet']['resourceId']['videoId'] ?? $videoItem['id']['videoId'] ?? $videoItem['id'];
            $title = html_entity_decode($videoItem['snippet']['title'] ?? null);
            $description = html_entity_decode($videoItem['snippet']['description'] ?? null);
            $channel = $videoItem['snippet']['channelTitle'] ?? null;
            return new YoutubeVideo($id, $title, $description, $channel);
        })->filter(function ($video) {
            return $video->id && $video->title;
        })->values()->toArray();
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'channel' => $this->channel,
            'description' => $this->description,
            'transcription' => $this->transcription,
            'thumbnail' => $this->getThumbnailUrl(),
            'url' => $this->getVideoUrl(),
        ];
    }

    function getThumbnailUrl(): ?string
    {
        $url = 'https://i.ytimg.com/vi/'.$this->id.'/maxresdefault.jpg';

        if (Http::head($url)->ok()) {
            return $url;
        }

        $url = 'https://i.ytimg.com/vi/'.$this->id.'/hqdefault.jpg';

        if (Http::head($url)->ok()) {
            return $url;
        }

        return null;
    }

    function getVideoUrl(): string
    {
        return 'https://www.youtube.com/watch?v='.$this->id;
    }

    function getShortsUrl(): string
    {
        return 'https://www.youtube.com/shorts/'.$this->id;
    }

    public static function fromArray(mixed $getMeta): YoutubeVideo
    {
        return new YoutubeVideo(
            id: $getMeta['id'],
            title: $getMeta['title'],
            description: $getMeta['description'],
            channel: $getMeta['channel'],
            transcription: $getMeta['transcription'],
        );
    }

    function extract(): ?YoutubeVideo
    {
        return YoutubeClient::extractVideo($this);
    }

    static function fromUrl(string $url): ?YoutubeVideo
    {
        $id = YoutubeClient::getYoutubeIdFromUrl($url);

        if (!$id) {
            return null;
        }

        return new YoutubeVideo(id: $id);
    }

    public function isShorts(): bool
    {
        if (str_contains($this->title, '#shorts')) {
            return true;
        }

        $response = Http::withoutRedirecting()->head($this->getShortsUrl())->throw();

        if ($response->redirect()) { // if it redirects, it's not a shorts
            return false;
        }

        if ($response->ok()) { // if it's ok, it's a shorts
            return true;
        }

        return false;
    }
}
