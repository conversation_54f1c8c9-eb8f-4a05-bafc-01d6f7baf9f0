<?php

namespace App\Service;

use DOMDocument;
use DOMXPath;

class ContentExtractorService
{
    public function extractContent(string $html): string
    {
        $dom = $this->loadHtmlDocument($html);
        $xpath = new DOMXPath($dom);

        $element = $this->findMainContentElement($dom, $xpath);

        if (!$element || !$this->isContentSubstantive($xpath, $element)) {
            return '';
        }

        $innerHTML = $this->extractInnerHtml($dom, $element);
        $plainText = $this->convertHtmlToPlainText($innerHTML);

        return $this->validateAndReturnPlainText($plainText);
    }

    private function loadHtmlDocument(string $html): DOMDocument
    {
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        libxml_clear_errors();

        return $dom;
    }

    private function findMainContentElement(DOMDocument $dom, DOMXPath $xpath): ?\DOMElement
    {
        $element = $dom->getElementsByTagName('article')->item(0);

        if (!$element) {
            $element = $dom->getElementsByTagName('main')->item(0);
        }

        if (!$element) {
            $element = $dom->getElementsByTagName('body')->item(0);

            if ($element) {
                $this->removeUnwantedElements($xpath, $element);
            }
        }

        return $element;
    }

    private function removeUnwantedElements(DOMXPath $xpath, \DOMElement $element): void
    {
        $nodesToRemove = $xpath->query('//script | //style | //noscript | //meta | //link | //iframe');
        foreach ($nodesToRemove as $node) {
            $node->parentNode->removeChild($node);
        }
    }

    private function isContentSubstantive(DOMXPath $xpath, \DOMElement $element): bool
    {
        $pCount = $xpath->query(".//p", $element)->length;
        return $pCount >= 3;
    }

    private function extractInnerHtml(DOMDocument $dom, \DOMElement $element): string
    {
        $innerHTML = '';
        foreach ($element->childNodes as $child) {
            $innerHTML .= $dom->saveHTML($child);
        }

        return $innerHTML;
    }

    private function convertHtmlToPlainText(string $innerHTML): string
    {
        $blockTags = 'p|div|li|br|h[1-6]|ul|ol|blockquote';
        $innerHTML = preg_replace("/<\s*($blockTags)(\s[^>]*)?>/i", "\n<$1$2>", $innerHTML);
        $innerHTML = preg_replace("/<\/\s*($blockTags)\s*>/i", "</$1>\n", $innerHTML);

        $plainText = strip_tags($innerHTML);

        $plainText = preg_replace('/\n\s*\n/', "\n\n", $plainText);
        $plainText = preg_replace('/[ \t]+/', ' ', $plainText);

        return trim($plainText);
    }

    private function validateAndReturnPlainText(string $plainText): string
    {
        return strlen($plainText) >= 100 ? $plainText : '';
    }
}
