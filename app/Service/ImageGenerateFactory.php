<?php

namespace App\Service;

use App\Enum\ImageSources;
use App\Service\Contracts\ImageGenerateInterface;

class ImageGenerateFactory
{
    public static function create($source): ImageGenerateInterface
    {
        return match ($source) {
            ImageSources::AI_IMAGES => app(AIImageService::class),
            ImageSources::GOOGLE_SEARCH_IMAGES => app(GoogleSearchImageService::class),
            default => throw new \Exception('Unexpected match value'),
        };
    }
}
