<?php

namespace App\Service;

use App\Models\User;
//use App\Models\YoutubeVideo;
//use App\Services\ScrapeOwl;
use App\Service\YouTubeDetector;
use Campo\UserAgent;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

use Throwable;
use voku\helper\HtmlDomParser;


class YoutubeClient
{
    const BASE_URI = 'https://www.googleapis.com/youtube/v3/';

    public Client $client;

    public function __construct(public ?User $user = null)
    {
        $this->client = new Client([
            'base_uri' => self::BASE_URI,
        ]);
    }

    /**
     * @param $tmp
     * @return string|null
     * @throws \Exception
     */
    private static function extractTranscriptionFromHtml($tmp): ?string
    {
        // dump($tmp);

        $captions = explode("playerCaptionsTracklistRenderer", $tmp);

        if (empty($captions[1])) {
            return '';
        }

        $captions2 = explode("audioTracks", $captions[1]);
        $captionArray = substr($captions2[0], 2);
        $captionArray = substr($captionArray, 0, -3);
        //echo "http://youtube.com/watch?v=" . $video_ids;

        //$ronTest = json_encode($captions2[0],JSON_FORCE_OBJECT);
        $ronTest2 = json_decode($captionArray."]}");

        if (empty($ronTest2)) {
            return '';
        }
        //$total_language = count($ronTest2->captionTracks);

        //if($total_language<=1){
        $videoSubtitle = '';
        // foreach($ronTest2->captionTracks as $captionTrack){
        $captionTrack = $ronTest2->captionTracks[0];
        $baseUrl = $captionTrack->baseUrl;

        // dump($baseUrl);

        // $xml = simpleXML_load_file($baseUrl, "SimpleXMLElement", LIBXML_NOCDATA);

        $tmp = @file_get_contents($baseUrl);

        if (!$tmp) {
            try {
                $tmp = Http::scrapeowl()->post('/', ['url' => $baseUrl, 'api_key' => config('services.scrapeowl.api_key')])->json('html');
            } catch (\Exception $e) {
                throw new \Exception("Scrapowl error: ".$e->getMessage());
            }
        }

        if (!$tmp) {
            return null;
        }

        $dom = HtmlDomParser::str_get_html($tmp);

        $subCount = 1;
        foreach ($dom->find('text') as $text) {
            if ($subCount == 35) {
                $videoSubtitle .= "<br><br>";
                $subCount = 1;
                $videoSubtitle .= ucfirst($text->plaintext)." ";
            } else {
                $videoSubtitle .= ucfirst($text->plaintext)." ";
            }
            $subCount++;
        }

        $videoSubtitle = str_replace(['<p>', '</p>'], ' ', $videoSubtitle);
        $videoSubtitle = str_replace(['<br>', '<br/>', '<br />'], ' ', $videoSubtitle);
        $videoSubtitle = trim(preg_replace('/\s\s+/', ' ', $videoSubtitle));

        return html_entity_decode(html_entity_decode($videoSubtitle ?: '') ?: '');
    }

    /**
     * @throws Throwable
     * @throws GuzzleException
     */
    public function getChannelVideos($urlOrChannelId, $nextPageToken = null, $maxResult = 10, $orderByOldest = false)
    {
        $channelId = (new YouTubeDetector($urlOrChannelId))->extractChannelID();

        if (!$channelId) {
            // Return an error or throw an exception if the channel ID is not found.
            return ['error' => 'Invalid channel URL or ID'];
        }

        if (str_starts_with($channelId, '@')) {
            // for new youtube channel url (i.e https://www.youtube.com/@SharkTankGlobal)/**/
            $channelId = cache()->remember('youtube:handle:'.$channelId, now()->addWeek(), function () use ($urlOrChannelId) {
                try {
                    $html = file_get_contents($urlOrChannelId);
                } catch (\Exception $e) {
                    $html = Http::scrapeowl()->post('/', ['url' => $urlOrChannelId, 'api_key' => config('services.scrapeowl.api_key')])->throw()->json('html');
                }
                preg_match('/content="UC([a-zA-Z0-9_-]{22})"/', $html, $matches);
                throw_unless($matches[1] ?? null, new \Exception('Invalid channel URL or ID : '.$urlOrChannelId));
                return 'UC'.$matches[1];
            });
        }

        if ($orderByOldest) {
            return $this->getChannelVideosInReverseOrder($channelId, $nextPageToken);
        }

        return cache()->remember('youtube_channel_'.$channelId.':'.$nextPageToken.":".$maxResult, now()->addHours(3), function () use ($nextPageToken, $channelId, $maxResult) {
            $response = $this->client->request('GET', 'search', [
                'query' => [
                    'key' => $this->user?->getYoutubeApiKey() ?: config('services.youtube.api_key'),
                    'part' => 'snippet',
                    'maxResults' => $maxResult,
                    'channelId' => $channelId,
                    'type' => 'video',
                    'order' => 'date',
                    'pageToken' => $nextPageToken,
                ]
            ]);
            return json_decode($response->getBody()->getContents(), true);
        });
    }

    public function searchVideos($keyword, $nextPageToken = null, $maxResult = 10)
    {
        return cache()->remember('youtube_search_'.$keyword.':'.$nextPageToken.":".$maxResult, now()->addHour(), function () use ($nextPageToken, $keyword, $maxResult) {
            $response = $this->client->request('GET', 'search', [
                'query' => [
                    'q' => $keyword,
                    'key' => $this->user?->getYoutubeApiKey() ?: config('services.youtube.api_key'),
                    'part' => 'snippet',
                    'type' => 'video',
                    'maxResults' => $maxResult,
                    'videoEmbeddable' => true,
                    'pageToken' => $nextPageToken,
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        });
    }

    public function getPlaylistVideos($urlOrPlaylistId, $nextPageToken = null, $maxResult = 10)
    {
        $playlistId = (new YouTubeDetector($urlOrPlaylistId))->getPlayListId();

        if (!$playlistId) {
            // Return an error or throw an exception if the playlist ID is not found.
            return ['error' => 'Invalid playlist URL or ID'];
        }

        return cache()->remember('youtube_playlist_'.$playlistId.':'.$nextPageToken.':'.$maxResult, now()->addHour(), function () use ($nextPageToken, $playlistId, $maxResult) {
            $response = $this->client->request('GET', 'playlistItems', [
                'query' => [
                    'key' => $this->user?->getYoutubeApiKey() ?: config('services.youtube.api_key'),
                    'part' => 'snippet',
                    'maxResults' => $maxResult,
                    'playlistId' => $playlistId,
                    'pageToken' => $nextPageToken,
                ]
            ]);

            return json_decode($response->getBody()->getContents(), true);
        });
    }

    static function searchV2($keyword)
    {
        $html = cache()->remember('youtube_search_v2_'.$keyword, now()->addDay(), function () use ($keyword) {
            return ScrapeOwl::get('https://youtube.com/results', [
                'search_query' => $keyword
            ]);
        });

        // preg_match_all('/watch\?v=(.*?)"/', $html, $matches);

        preg_match_all('/"videoId":"(.*?)".*?"text":"(.*?)".*?"label":"(.*? by .*? \d+ .*? ago .*? views)"/s', $html, $matches, PREG_SET_ORDER);

        $result = [];
        foreach ($matches as $match) {
            $result[] = [
                'videoId' => $match[1],
                'title' => $match[2],
                // 'info' => $match[3],
            ];
        }
    }

    function getRandomEmbed($keyword): ?string
    {
        try {
            $videos = $this->searchVideos($keyword);

            if (count($videos['items'] ?? []) == 0) {
                return null;
            }

            $video = collect($videos['items'])->first();

            $videoId = Arr::get($video, 'id.videoId');

            // html embed code iframe
            return $this->getEmbedCode($videoId);
        } catch (\Exception $e) {
            Log::warning('YoutubeSearch::getRandomEmbed: '.$keyword.' : Exception: '.$e->getMessage());
            return null;
        }
    }

    /**
     * @throws GuzzleException
     */
    function getVideo(string $videoId): array
    {
        // $youtubeVideo = YoutubeVideo::fromUrl($videoId);
        // $youtubeVideo = (new YouTubeDetector($videoId))->isVideoUrl() ? $this->getYoutubeIdFromUrl($videoId) : $videoId;
        $videoId = (new YouTubeDetector($videoId))->isSingleVideoUrl() ? $this->getYoutubeIdFromUrl($videoId) : $videoId;

        $client = new Client([
            'base_uri' => self::BASE_URI,
        ]);

        $response = $client->request('GET', 'videos', [
            'query' => [
                'id' => $videoId,
                'key' => $this->user?->getYoutubeApiKey() ?: config('services.youtube.api_key'),
                'part' => 'snippet',
            ],
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws \Exception
     */
    static function extractVideo(YoutubeVideo $youtubeVideo): ?YoutubeVideo
    {
        // $tmp = cache()->remember('YoutubeVideo:'.$youtubeVideo->id, now()->addHours(3), function () use ($youtubeVideo) {
        // $tmp = file_get_contents('http://www.youtube.com/watch?v='.$youtubeVideo->id);
        $tmp = null; // this is not working on production server

        if (!$tmp) {
            try {
                // scrapeowl is not working on production server
                // $tmp = Http::scrapeowl()->post('/', [
                //     'url' => 'https://www.youtube.com/watch?v='.$youtubeVideo->id,
                //     'api_key' => config('services.scrapeowl.api_key'),
                //     'headers' => [
                //         'User-Agent' => "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
                //     ],
                // ])->json('html');

//                $tmp = retry(3, function () use ($youtubeVideo) {
//                    return Http::withOptions(["proxy" => Arr::random(get_residential_proxies())])
//                        ->withoutVerifying()
//                        ->withUserAgent(Arr::random([
//                            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
//                            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15',
//                            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:116.0) Gecko/20100101 Firefox/116.0',
//                            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36'
//                        ]))
//                        ->connectTimeout(60)
//                        ->timeout(300)
//                        ->get('https://www.youtube.com/watch?v='.$youtubeVideo->id)
//                        ->throw()
//                        ->body();
//                });

                $tmp = retry(3, function () use ($youtubeVideo) {
                    $http = Http::withoutVerifying()
                                ->withUserAgent(Arr::random([
                                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
                                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15',
                                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:116.0) Gecko/20100101 Firefox/116.0',
                                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36'
                                ]))
                                ->connectTimeout(60)
                                ->timeout(300);

                    // Add proxy only if not in local environment
                    if (!app()->environment('local')) {
                        $http->withOptions(["proxy" => Arr::random(get_residential_proxies())]);
                    }

                    return $http->get('https://www.youtube.com/watch?v='.$youtubeVideo->id)
                                ->throw()
                                ->body();
                });


            } catch (\Exception $e) {
                throw new \Exception("Shifter error: ".$e->getMessage());
            }
        }

        // return $tmp;
        // });

        // Log::debug("check tmp file: " . $tmp);

        if (!$tmp) {
            return null;
        }

        $dom = HtmlDomParser::str_get_html($tmp);

        // dump($dom->find('title', 0)?->text, $dom->find('title', 1)?->text);

        $youtubeVideo->title = $dom->find('title', 0)?->text;

        if (preg_match('/"attributedDescriptionBodyText":{"content":"(.*?)","commandRuns"/', $tmp, $matches)) {
            $youtubeVideo->descriptionRaw = html_entity_decode($matches[1] ?? '');
            $youtubeVideo->descriptionRaw = str_replace('\n', PHP_EOL, $youtubeVideo->descriptionRaw);
            $youtubeVideo->description = $matches[1] ?? '';

            // remove any http or https link in the description
            $youtubeVideo->description = str($youtubeVideo->description)->explode('\n')->filter(function ($line) {
                return !str_contains($line, 'http://') && !str_contains($line, 'https://');
            })->implode(PHP_EOL);

            $youtubeVideo->description = preg_replace('/(https?:\/\/[^\s]+)/', ' ', $youtubeVideo->description);

            // remove extra spaces
            $youtubeVideo->description = preg_replace('/\s+/', ' ', $youtubeVideo->description);
        }

        if (preg_match('/"channelName":"(.*?)",/', $tmp, $matches)) {
            $youtubeVideo->channel = $matches[1] ?? '';
        }

        if (!$youtubeVideo->channel && preg_match('/"ownerChannelName":"(.*?)",/', $tmp, $matches)) {
            $youtubeVideo->channel = $matches[1] ?? '';
        }

        $youtubeVideo->title = html_entity_decode($youtubeVideo->title);
        $youtubeVideo->title = trim(str_replace('- YouTube', '', $youtubeVideo->title));
        $youtubeVideo->channel = html_entity_decode($youtubeVideo->channel);
        $youtubeVideo->description = html_entity_decode($youtubeVideo->description);
        $youtubeVideo->transcription = self::extractTranscriptionFromHtml($tmp);

        return $youtubeVideo;
    }

    static function getYoutubeIdFromUrl($targets): string|null
    {
        foreach (explode_targets($targets) as $target) {

            $pattern = '#^(?:https?://)?(?:(?:www|m)\.)?(?:youtube\.com/(?:watch\?.*?v=|shorts/)|youtu\.be/)([\w-]{10,12}).*$#i';

            $result = preg_match($pattern, $target, $matches);

            if ($result && ($matches[1] ?? null)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * @param  mixed  $videoId
     * @return string
     */
    static public function getEmbedCode(mixed $videoId): string
    {
        return "<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/$videoId\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>";
    }

    /**
     * @throws GuzzleException
     */
    public function validateApiKey($apiKey)
    {
        $response = $this->client->request('GET', 'search', [
            'query' => [
                'q' => 'open ai',
                'key' => $apiKey,
                'part' => 'snippet',
                'type' => 'video',
                'maxResults' => 1,
                'videoEmbeddable' => true,
            ],
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    public static function saveApiKey($apiKey): void
    {
        // merge with existing API keys
        $currentKeys = collect(auth()->user()->youtube_api_key);
        $updatedKeys = $currentKeys->push($apiKey)->unique()->toArray();

        auth()->user()->update([
            'youtube_api_key' => $updatedKeys,
        ]);
    }

    public static function clearApiKey($apiKeys): void
    {
        $exisingApiKeys = auth()->user()->youtube_api_key;
        if (!empty($exisingApiKeys)) {
            foreach ($exisingApiKeys as $key => $value) {
                // Check if the value doesn't match and the corresponding value in $apiKeys is an empty string
                if ($value !== $apiKeys[$key] && $apiKeys[$key] === "") {
                    // Remove the element from $exisingApiKeys
                    unset($exisingApiKeys[$key]);
                }
            }
            auth()->user()->update(['youtube_api_key' => $exisingApiKeys]);
        }
    }

    /**
     * @param  string|null  $channelId
     * @param  mixed  $nextPageToken
     * @return array[]
     * @throws GuzzleException
     */
    private function getChannelVideosInReverseOrder(?string $channelId, mixed $nextPageToken): array
    {
        $desiredTotalResults = 500;
        $responseArray = ['items' => []];

        while (count($responseArray['items']) < $desiredTotalResults) {
            $response = $this->client->request('GET', 'search', [
                'query' => [
                    'key' => $this->user?->getYoutubeApiKey() ?: config('services.youtube.api_key'),
                    'part' => 'snippet',
                    'maxResults' => 50,
                    'channelId' => $channelId,
                    'type' => 'video',
                    'order' => 'date',
                    'pageToken' => $nextPageToken,
                ]
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            $responseArray['items'] = array_merge($responseArray['items'], $responseData['items']);

            if (isset($responseData['nextPageToken'])) {
                $nextPageToken = $responseData['nextPageToken'];
            } else {
                break; // No more pages to fetch
            }
        }

        // Reverse the order of videos
        $responseArray['items'] = array_reverse($responseArray['items']);
        $responseArray['nextPageToken'] = $nextPageToken;

        return $responseArray;
    }
}
