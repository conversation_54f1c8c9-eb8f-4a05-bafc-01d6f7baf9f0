<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Symfony\Component\HttpFoundation\Response;

class InstantEbookBlueprintService
{
    protected $aiClient;

    public function generate(ResearchEbookCampaign $campaign)
    {
        try {
            $blueprintResponse = $this->generateBlueprint($campaign);
            $campaign->log("Generate blueprint response: " . formatLogMessage($blueprintResponse));

            if(!$blueprintResponse){
                $campaign->update([
                    'status' => CampaignStatusEnum::FAILED,
                ]);
                $campaign->addErrorToMeta('generate_blueprint', "Generate blueprint response: " . formatLogMessage($blueprintResponse));

                throw new \Exception("Unable to generate blueprint");
            }

            $blueprintData = formatAIResponse($blueprintResponse);
            $campaign->log("Formatted Generated blueprint response: " . formatLogMessage($blueprintData));

            if (is_array($blueprintData)) {
                // Store the entire blueprint data
                ResearchCampaignEntry::create([
                    'entry' => 'Instant eBook Blueprint for: ' . $campaign->getForm("topic"),
                    "entry_content" => $blueprintData,
                    "research_ebook_campaign_id" => $campaign->id
                ]);
                
                return true;
            } else {
                $campaign->log("Blueprint data is not in expected format. " . formatLogMessage($blueprintData));
                return false;
            }
        } catch (\Exception $e) {
            $campaign->log("Failed to generate blueprint. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    public function generateBlueprint(ResearchEbookCampaign $campaign): string
    {
        try {
            $topic = $campaign->getForm("topic");
            
            if (empty($topic)) {
                throw new \Exception("Topic is required for instant ebook blueprint generation");
            }

            $campaignData = [
                'topic' => $topic,
            ];

            $campaign->log("Campaign data for instant ebook blueprint prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['instant_ebook_blueprint'], $campaignData);

            $campaign->log("Prompt for instant ebook blueprint " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate instant ebook blueprint from AI model. " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating instant ebook blueprint.' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}