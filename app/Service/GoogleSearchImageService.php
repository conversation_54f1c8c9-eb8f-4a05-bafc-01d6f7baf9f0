<?php

namespace App\Service;

use App\Models\Campaign;
use App\Models\Section;
use App\Service\AIModel\Contracts\AIClientInterface;
use App\Service\Contracts\ImageGenerateInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class GoogleSearchImageService implements ImageGenerateInterface
{
    protected $aiClient;

//    public function __construct(AIClientInterface $aiClient)
//    {
//        $this->aiClient = $aiClient;
//    }
    public function generateImage(Campaign $campaign, Section $section): string
    {
        $keyWord = strip_tags($section->title);
        try {
            $html = Http::scrapeowl()->post('/', [
                'url' => 'https://www.google.com/search?' . http_build_query(['hl' => 'en', 'tbm' => 'isch', 'q' => $keyWord]),
                'api_key' => config('services.scrapeowl.api_key'),
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Linux; Android 4.4.2; Nexus 4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36',
                ],
            ])->throw()->json('html');
            preg_match_all('/s=\'(data:image\/[a-zA-Z]+;base64,[^\']+)\';/', $html, $matches);

            $base64Images = $matches[1] ?? [];
            $images = array_map(function ($image) {
                return str_replace('\\x3d', '=', $image);
            }, $base64Images);

            // Sort images by size (high to low)
            usort($images, function ($a, $b) {
                // Calculate the size of the base64 string in bytes
                $sizeA = (int) ((strlen($a) - strpos($a, ',')) * 0.75); // Skip the "data:image/...;base64," prefix
                $sizeB = (int) ((strlen($b) - strpos($b, ',')) * 0.75);
                return $sizeB - $sizeA; // Sort from high to low
            });

            // Display extracted base64 strings
            // foreach ($images as $base64Image) {
            //     echo "<img src='" . $base64Image . "'/>\n\n";
            // }
            // $dom = HtmlDomParser::str_get_html($html);
            // $imgs = $dom->getElementsByTagName('img');
            // foreach ($imgs as $div) {
            //     echo $div->getAttribute('src') . " <br/>";
            //     $images[] = $div->getAttribute('src');
            // }

            // $images = array_filter($images, function ($value) {

            //     return $value !== "";
            // });
            // die();

            if (!empty($images)) {
                // return $images[array_rand($images)];
                return $images[0];
            } else {
                throw new \Exception("Image not found");
            }

        } catch (\Exception $e) {
            Log::error('Failed to generate image from Google search.', [
                'campaign_id' => $campaign->id,
                'section_id' => $section->id,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Error in generating image. Error: ' . formatLogMessage($e->getMessage()), Response::HTTP_BAD_REQUEST);
        }
    }
}
