<?php

namespace App\Service;

use App\Actions\StoreEbookPathAction;
use App\Models\Campaign;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Throwable;

class GenerateEbookAsEPUB
{
    protected StoreEbookPathAction $storeEbookPathAction;

    public function __construct(StoreEbookPathAction $storeEbookPathAction)
    {
        $this->storeEbookPathAction = $storeEbookPathAction;
    }

    public function generateEbook(int $ebookId): void
    {
        try {
            // Fetch the ebook with chapters and sections
            //$ebook = Campaign::with(['chapters.sections', 'user'])->findOrFail($ebookId);

            $ebook = Campaign::with(['chapters' => function ($query) {
                $query->orderBy('chapter_number');  // Order chapters by chapter_number
            }, 'chapters.sections' => function ($query) {
                $query->orderBy('order', "asc");  // Order sections by order
            }, 'user'])->findOrFail($ebookId);

            $ebook->log("Rendering ebook view");
            $html = $this->renderEbookView($ebook);

            $ebook->log("Generating ebook file path");
            $filePath = $this->generateFilePath($ebook);

            $ebook->log("Ensuring directory 'ebooks/epub' exists");
            $this->ensureDirectoryExists('ebooks/epub');

            $ebook->log("Generating ebook as EPUB");
            $this->generateEPUB($html, $filePath, $ebook);

            $ebook->log("Storing ebook path in database");
            $this->storeEbookPathAction->store($ebookId, $filePath, "epub");
        } catch (Throwable $e) {
            $ebook = Campaign::findOrFail($ebookId);
            $ebook->log("EPUB Generation Failed: " . $e->getMessage());
            throw new \RuntimeException('Failed to generate the EPUB. Please try again. ' . $e->getMessage());
        }
    }

    /**
     * Download the cover image from the URL and save it locally.
     */
    private function downloadCoverImage(string $imagePathOrUrl, int $ebookId): string
    {
        // Determine if the image path is an S3 path or a URL
        if (filter_var($imagePathOrUrl, FILTER_VALIDATE_URL)) {
            // It's a URL, so download the image
            return $this->downloadImageFromUrl($imagePathOrUrl, $ebookId);
        } else {
            // It's an S3 path, generate a temporary URL
            return $this->downloadImageFromS3($imagePathOrUrl, $ebookId);
        }
    }

    /**
     * Download the cover image from a URL.
     */
    private function downloadImageFromUrl(string $imageUrl, int $ebookId): string
    {
        $localPath = storage_path('app/ebooks/cover_' . $ebookId . '.jpg'); // Save it with ebook ID

        $imageData = file_get_contents($imageUrl);

        if ($imageData === false) {
            throw new \RuntimeException('Failed to download the cover image from the provided URL.');
        }

        file_put_contents($localPath, $imageData);

        return $localPath;
    }

    /**
     * Download the cover image from an S3 path.
     */
    private function downloadImageFromS3(string $s3Path, int $ebookId): string
    {
        // Generate a temporary URL for the image in S3
        $temporaryUrl = Storage::disk('s3')->temporaryUrl($s3Path, now()->addMinutes(5));

        // Now download the image from the generated URL
        return $this->downloadImageFromUrl($temporaryUrl, $ebookId);
    }


    /**
     * Render the Blade view to generate the EPUB's HTML.
     */
    private function renderEbookView(Campaign $ebook): string
    {
        $format = $ebook->ebookFormat;
        $bgImage = $format->background_image ? Storage::disk('s3')->temporaryUrl($format->background_image, now()->addMinutes(30)) : "";
        return view('ebook.ebook-epub', compact('ebook', 'format','bgImage'))->render();
    }

    /**
     * Generate a file path for storing the EPUB.
     */
    private function generateFilePath(Campaign $ebook): string
    {
        $fileName = Str::slug(strip_tags($ebook->title))  . "_" . time() . '.epub';
        return 'ebooks/epub/' . $fileName;
    }

    /**
     * Ensure the specified directory exists.
     */
    private function ensureDirectoryExists(string $directory): void
    {
        if (!Storage::disk('s3')->exists($directory)) {
            Storage::disk('s3')->makeDirectory($directory);
        }
    }

    /**
     * Delete any existing ebook file if it exists.
     */
    private function deleteExistingEbook(Campaign $ebook): void
    {
        if ($ebook->getEpub() && isS3FileExist($ebook->getEpub())) {
            Cache::forget($ebook->getEpub());
            Storage::disk('s3')->delete($ebook->getEpub());
        }
    }

    /**
     * Generate the EPUB file using Pandoc.
     */
    private function generateEPUB(string $html, string $filePath, Campaign $ebook): void
    {
        $localCoverImagePath = '';
        // Save the rendered HTML to a temporary file
        $tmpHtmlFile = storage_path('app/temp_' . Str::slug(strip_tags($ebook->title)) . '.html');
        file_put_contents($tmpHtmlFile, $html);

        // Define the full path for the EPUB file
        $epubFullPath = storage_path('app/' . $filePath);

        \Illuminate\Support\Facades\File::ensureDirectoryExists(dirname($epubFullPath));

        $title = strip_tags($ebook->title);
        $author = $ebook->author ?? $ebook->user->name ?? "Unknown Author";

        // Get the cover image URL or path (can be null)
        $coverImage = $ebook->cover_image;

        // Initialize the Pandoc command arguments
        $pandocArgs = [
            'pandoc',
            '--metadata', 'title=' . strip_tags($ebook->title),
            '--metadata', 'author=' . ($ebook->author ?? $ebook->user->name ?? 'Unknown Author'),
            $tmpHtmlFile,
            '-o', $epubFullPath,
        ];

        // If cover image exists, download it and add it to the command
        if ($coverImage) {
            // Download the cover image and get the local path
            $localCoverImagePath = $this->downloadCoverImage($coverImage, $ebook->id);
            // Add the cover image to the Pandoc arguments
            $pandocArgs[] = '--epub-cover-image';
            $pandocArgs[] = $localCoverImagePath;
        }

        // Run the Pandoc process
        $process = new Process($pandocArgs);
        $process->run();

        // If Pandoc fails, throw an exception
        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }

        // If Pandoc fails, throw an exception
        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }

        // Move the generated EPUB to the desired location
        $epubContent = file_get_contents($epubFullPath);
        Storage::disk('s3')->put($filePath, $epubContent, 'public'); // or 'private'

        // Clean up temporary files
        unlink($epubFullPath);
        unlink($tmpHtmlFile);

        // Delete the temporary cover image after the process

        if ($localCoverImagePath) {
            unlink($localCoverImagePath);
        }
    }


}

