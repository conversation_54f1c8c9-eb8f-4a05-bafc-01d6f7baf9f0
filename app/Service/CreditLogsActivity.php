<?php

namespace App\Service;

use App\Enum\CreditLogActionEnum;
use App\Enum\CreditLogActionTypeEnum;
use App\Models\CreditLog;
use App\Models\User;

class CreditLogsActivity
{
    protected ?float $credit = 0;
    protected ?float $previousCredit;
    protected ?float $currentCredit;
    protected ?CreditLogActionTypeEnum $actionType;
    protected bool $alreadyDeduct = false;

    public function __construct(
        protected ?User $user = null,
        protected ?CreditLogActionEnum $action,
        protected ?string $description = null,
        protected ?string $descPrefix = null,
        protected ?array $logMeta = [],
    ){
        $this->setUser($user);
        $this->setAction($action);
        $this->setDescription($description);
    }

    public function addCreditLog()
    {
        $this->previousCredit = $this->user->credits - $this->user->credits_used;

        if ($this->alreadyDeduct) {
            $this->previousCredit = ($this->user->credits + $this->credit) - $this->user->credits_used;
            $this->currentCredit = $this->user->credits - $this->user->credits_used;
        }

        if(isset($this->logMeta['current_credit']) && !empty($this->logMeta['current_credit'])){
            $this->currentCredit = $this->logMeta['current_credit'];
        }

        CreditLog::firstOrCreate(
            [
                'user_id' => $this->user->id,
                'action' => $this->action,
                'credit' => $this->credit,
                'current_credit' => $this->currentCredit,
            ],
            [
                'user_id' => $this->user->id,
                'action' => $this->action,
                'credit' => $this->credit,
                'previous_credit' => $this->previousCredit,
                'current_credit' => $this->currentCredit,
                'description' => $this->description,
                'action_type' => $this->actionType,
                'mw_id' => $this->logMeta['mw_id'] ?? '',
                'meta' => $this->logMeta,
            ]
        );
    }

    public function setUser(?User $user)
    {
        $this->user = $user ?: auth()->user()->getUser();
    }

    public function setDescription(?string $description)
    {
        $this->description = $description ?: $this->generateDescription();
    }

    public function setAction(?CreditLogActionEnum $action)
    {
        $this->action = $action ?: CreditLogActionEnum::CAMPAIGN;
    }

    public function addCredit(?float $credit, bool $alreadyDeduct = false)
    {
        $this->credit = $credit;
        $this->alreadyDeduct = $alreadyDeduct;
        $this->actionType = CreditLogActionTypeEnum::CREDIT_IN;
        $this->currentCredit = ($this->user->credits + $this->credit) - $this->user->credits_used;

        $this->addCreditLog();
    }

    public function deductCredit(?float $credit, bool $alreadyDeduct = false)
    {
        $this->credit = $credit;
        $this->alreadyDeduct = $alreadyDeduct;
        $this->actionType = CreditLogActionTypeEnum::CREDIT_OUT;
        $this->currentCredit = $this->user->credits  - ($this->credit + $this->user->credits_used);

        $this->addCreditLog();
    }

    public function generateDescription()
    {
        return match($this->action){
            CreditLogActionEnum::CAMPAIGN => 'Create campaign #'.$this->descPrefix,
            CreditLogActionEnum::BILLING => 'Add credit on purchased',
            CreditLogActionEnum::GATEWAY_REFUND => 'Deduct credit on gateway refund',
            CreditLogActionEnum::REFUND => 'Add credit for system refund',
            default => ''
        };
    }
}
