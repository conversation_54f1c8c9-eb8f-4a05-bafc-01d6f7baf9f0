<?php

namespace App\Service;

use Illuminate\Support\Facades\Http;
use Spatie\Image\Image;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FinalizeImageService
{
    /**
     * Handle an image input (URL or Base64 string), optimize it, and upload to S3.
     *
     * @param string $imageInput URL or Base64 image string
     * @param string $fileName Prefix for the optimized image file
     * @return string URL of the uploaded image on S3
     * @throws \Exception
     */
    public function handleImage(string $imageInput, string $fileName = "optimize_image_"): string
    {
        $imageContent = $this->resolveImageContent($imageInput);

        // Optimize the image content
        $optimizedImagePath = $this->imageOptimize($imageContent);

        // Store the optimized image to S3
        return $this->imageStoreToS3($optimizedImagePath, $fileName);
    }

    /**
     * Determine whether input is Base64 or URL and return image content.
     *
     * @param string $imageInput URL or Base64 string
     * @return string Image binary content
     * @throws \Exception
     */
    private function resolveImageContent(string $imageInput): string
    {
        if ($this->isBase64($imageInput)) {
            // Strip metadata and decode Base64 string
            $base64Data = $this->stripBase64Metadata($imageInput);
            $imageContent = base64_decode($base64Data, true);

            if ($imageContent === false) {
                throw new \Exception('Invalid Base64 image string provided.');
            }

            return $imageContent;
        }

        // Fetch image content if it's a URL
        $response = Http::get($imageInput);
        if (!$response->successful()) {
            throw new \Exception('Failed to download the image from the provided URL.');
        }

        return $response->body();
    }

    /**
     * Check if the given string is a valid Base64 encoded image.
     *
     * @param string $string Input string
     * @return bool True if valid Base64, otherwise false
     */
    private function isBase64(string $string): bool
    {
        $base64Pattern = '/^data:image\/[a-zA-Z]+;base64,/';
        return preg_match($base64Pattern, $string) === 1;
    }

    private function stripBase64Metadata(string $base64String): string
    {
        $base64Pattern = '/^data:image\/[a-zA-Z]+;base64,/';
        return preg_replace($base64Pattern, '', $base64String);
    }

    /**
     * Optimize the given image content and save it locally.
     *
     * @param string $imageContent Raw image binary content
     * @return string Path to the optimized image file
     * @throws \Exception
     */
    public function imageOptimize(string $imageContent): string
    {
        // Define the directory path
        $tempDir = storage_path('app/temp_images');

        // Ensure the directory exists
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true); // Create directory with permissions
        }

        // Generate a unique name for the temporary file
        $tempFileName = 'temp_image_' . Str::random(10) . '.jpg';
        $tempPath = $tempDir . '/' . $tempFileName;

        // Write the image content to the temporary file
        file_put_contents($tempPath, $imageContent);

        // Generate a unique name for the optimized image file
        $optimizedFileName = 'optimized_image_' . Str::random(10) . '.jpg';
        $optimizedImagePath = $tempDir . '/' . $optimizedFileName;

        // Optimize the image and save it
        Image::load($tempPath)
             ->optimize()
             ->save($optimizedImagePath);

        // Clean up the temporary file
        unlink($tempPath);

        return $optimizedImagePath;
    }


    /**
     * Store the optimized image to S3 and return its URL.
     *
     * @param string $optimizedImagePath Path to the optimized image file
     * @param string $fileName Prefix for the image file name
     * @return string S3 URL of the stored image
     * @throws \Exception
     */
    public function imageStoreToS3(string $optimizedImagePath, string $fileName): string
    {
        $imageContents = file_get_contents($optimizedImagePath);
        if ($imageContents === false) {
            throw new \Exception('Failed to read the optimized image file.');
        }

        $imageName = $fileName . time() . '.jpg';
        Storage::disk('s3')->put($imageName, $imageContents, 'public');

        $imageUrl = Storage::disk('s3')->url($imageName);
        unlink($optimizedImagePath);

        return $imageUrl;
    }
}
