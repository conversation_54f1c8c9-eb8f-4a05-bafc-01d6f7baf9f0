<?php

namespace App\Service;

use App\Enum\YouTubeDetectorType;
use App\Models\User;
use App\Service\YoutubeClient;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class YouTubeDetector
{
    public function __construct(public string $url)
    {

    }

    public function isSingleVideoUrl(): bool
    {
        if ($this->isPlayList()) {
            return false;
        }

        return (bool) YoutubeClient::getYoutubeIdFromUrl($this->url);
    }

    public function isPlayList(): bool|int
    {
        return (bool) $this->getPlayListId();
    }

    public function isSearchPage(): bool|int
    {
        return $this->isValidYouTubeUrl() && $this->getSearchKeyword();
    }

    public function isChannel(): bool
    {
        preg_match('/^(https?:\/\/)?((?:www|m)\.)?(youtube\.com\/channel\/)/', $this->url, $matches);

        preg_match('/^(https?:\/\/)?((?:www|m)\.)?(youtube\.com\/@)/', $this->url, $matches2);

        return !empty($matches) || !empty($matches2);
    }

    function getPlayListId(): ?string
    {
        $parsedUrl = parse_url($this->url);

        parse_str($parsedUrl['query'] ?? '', $args);

        if ($this->isValidYouTubeUrl() && ($args['list'] ?? null)) {
            return $args['list'];
        }

        return null;
    }

    function extractChannelID(): ?string
    {
        preg_match('/(?:\/channel\/|\/c\/)([a-zA-Z0-9-_]{24})/', $this->url, $matches);

        if (isset($matches[1])) {
            return $matches[1];
        }

        // https://www.youtube.com/@AIWiseMind-gz8zy/playlists
        preg_match('/(?<=youtube\.com\/@)([^\/]+)/', $this->url, $matches);

        if (isset($matches[1])) {
            return '@'.$matches[1];
        }

        return null; // Return null if channel ID is not found
    }

    function getYouTubeVideoId(): ?string
    {
        return YoutubeClient::getYoutubeIdFromUrl($this->url);
    }

    function getSearchKeyword(): ?string
    {
        $query = @parse_url($this->url, PHP_URL_QUERY);
        @parse_str($query, $params);
        return $params['search_query'] ?? null;
    }

    function isValidYouTubeUrl(): bool
    {
        $host = parse_url($this->url, PHP_URL_HOST);

        return Str::contains($host, ['youtube.com', 'youtu.be']);
    }
}

