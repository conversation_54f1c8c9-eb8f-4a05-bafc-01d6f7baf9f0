<?php

namespace App\Service;

use Monolog\Handler\SyslogUdpHandler;
use Monolog\LogRecord;

class CustomSyslogUdpHandler extends SyslogUdpHandler
{
    function write(LogRecord $record): void
    {
        // We don't want to log exceptions to Papertrail, because they are already logged to Flare.
        if ($record->context['exception'] ?? null) {
            return;
        }

        parent::write($record);
    }
}
