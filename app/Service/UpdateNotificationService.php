<?php

namespace App\Service;

use App\Jobs\SendUpdateNotificationJob;
use App\Models\AppUpdate;
use App\Models\User;
use App\Models\UserUpdateNotification;
use App\Models\UserUpdatePreference;
use Illuminate\Support\Facades\Log;

class UpdateNotificationService
{
    /**
     * Send notifications for a published update
     */
    public function sendNotificationsForUpdate(AppUpdate $update): void
    {
        if (!$update->shouldSendEmailNotification()) {
            Log::info("Update {$update->id} does not require email notifications");
            return;
        }

        // Create notifications for all users
        $this->createNotificationsForAllUsers($update);

        // Dispatch job to send emails
        SendUpdateNotificationJob::dispatch($update);

        Log::info("Dispatched email notifications for update {$update->id}");
    }

    /**
     * Create notification records for all active users
     */
    private function createNotificationsForAllUsers(AppUpdate $update): void
    {
        $createdCount = 0;

        User::query()
            ->chunk(500, function ($users) use ($update, &$createdCount) {
                foreach ($users as $user) {
                    UserUpdateNotification::createForUserAndUpdate($user, $update);
                    $createdCount++;
                }
            });

        Log::info("Created {$createdCount} notification records for update {$update->id}");
    }

    /**
     * Mark update as read for user
     */
    public function markAsReadForUser(AppUpdate $update, User $user): void
    {
        $notification = UserUpdateNotification::where('user_id', $user->id)
            ->where('app_update_id', $update->id)
            ->first();

        if ($notification) {
            $notification->markAsRead();
        } else {
            // Create notification record and mark as read
            UserUpdateNotification::createForUserAndUpdate($user, $update)->markAsRead();
        }
    }

    /**
     * Get unread updates for user
     */
    public function getUnreadUpdatesForUser(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return AppUpdate::published()
            ->where(function ($query) use ($user) {
                // Updates that have no notification record for this user (new updates)
                $query->whereDoesntHave('userNotifications', function ($subQuery) use ($user) {
                    $subQuery->where('user_id', $user->id);
                })
                // OR updates that have notification record but are marked as unread
                ->orWhereHas('userNotifications', function ($subQuery) use ($user) {
                    $subQuery->where('user_id', $user->id)
                        ->where('is_read', false);
                });
            })
            ->orderBy('released_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent updates for user (read and unread)
     */
    public function getRecentUpdatesForUser(User $user, int $limit = 20): \Illuminate\Database\Eloquent\Collection
    {
        return AppUpdate::published()
            ->with(['userNotifications' => function ($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->orderBy('released_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Update user preferences
     */
    public function updateUserPreferences(User $user, array $preferences): UserUpdatePreference
    {
        $userPreferences = UserUpdatePreference::getOrCreateForUser($user);
        
        $userPreferences->update([
            'email_major_updates' => $preferences['email_major_updates'] ?? $userPreferences->email_major_updates,
            'email_minor_updates' => $preferences['email_minor_updates'] ?? $userPreferences->email_minor_updates,
            'email_patch_updates' => $preferences['email_patch_updates'] ?? $userPreferences->email_patch_updates,
            'email_security_updates' => $preferences['email_security_updates'] ?? $userPreferences->email_security_updates,
            'categories' => $preferences['categories'] ?? $userPreferences->categories,
        ]);

        return $userPreferences;
    }

    /**
     * Unsubscribe user from all update notifications
     */
    public function unsubscribeUser(string $token): bool
    {
        $preferences = UserUpdatePreference::where('unsubscribe_token', $token)->first();

        if (!$preferences) {
            return false;
        }

        $preferences->update([
            'email_major_updates' => false,
            'email_minor_updates' => false,
            'email_patch_updates' => false,
            'email_security_updates' => false,
        ]);

        Log::info("User {$preferences->user_id} unsubscribed from update notifications");

        return true;
    }

    /**
     * Get statistics for an update
     */
    public function getUpdateStatistics(AppUpdate $update): array
    {
        $totalNotifications = $update->userNotifications()->count();
        $readNotifications = $update->userNotifications()->read()->count();
        $emailsSent = $update->userNotifications()->emailSent()->count();

        return [
            'total_notifications' => $totalNotifications,
            'read_notifications' => $readNotifications,
            'unread_notifications' => $totalNotifications - $readNotifications,
            'emails_sent' => $emailsSent,
            'read_percentage' => $totalNotifications > 0 ? round(($readNotifications / $totalNotifications) * 100, 2) : 0,
            'email_sent_percentage' => $totalNotifications > 0 ? round(($emailsSent / $totalNotifications) * 100, 2) : 0,
        ];
    }
}
