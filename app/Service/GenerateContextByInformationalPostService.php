<?php

namespace App\Service;

use App\Actions\SummarizeContentAction;
use App\Actions\WebBrowserAction;
use App\Models\Campaign;


class GenerateContextByInformationalPostService
{
    protected $campaign;
    protected WebBrowserAction $webBrowserAction;
    protected SummarizeContentAction $summarizeContentAction;

    protected ContentExtractorService $contentExtractorService;

    public function __construct(
        Campaign $campaign,
        WebBrowserAction $webBrowserAction,
        SummarizeContentAction $summarizeContentAction,
        ContentExtractorService $contentExtractorService
    )
    {
        $this->campaign = $campaign;
        $this->webBrowserAction = $webBrowserAction;
        $this->summarizeContentAction = $summarizeContentAction;
        $this->contentExtractorService = $contentExtractorService;
    }

    public function generateContext(): string
    {
        $contentUrl = $this->campaign->getForm("content_url");

        if ( ! $contentUrl) {
            $this->campaign->log("Content URL is missing.");
            return '';
        }

        $summary = "";
        $html = $this->webBrowserAction->execute($contentUrl, $this->campaign);

        if ($html && !empty($html)){
            $content = $this->contentExtractorService->extractContent($html);

            $summary = $this->summarizeContentAction->execute($content, $this->campaign) ?? "";
        }

        return !empty($summary) ? $summary : $this->campaign->topic;
    }



//    private function extractFormattedBodyText(string $html): string
//    {
//        libxml_use_internal_errors(true);
//        $dom = new \DOMDocument();
//        // Suppress warnings from malformed HTML
//        @$dom->loadHTML($html);
//        libxml_clear_errors();
//
//        $xpath = new \DOMXPath($dom);
//
//        // 1. Prefer an <article> tag.
//        $element = $dom->getElementsByTagName('article')->item(0);
//
//        // 2. If no <article>, try <main>.
//        if (!$element) {
//            $element = $dom->getElementsByTagName('main')->item(0);
//        }
//
//        // 3. Fallback to the entire <body> if nothing else is found.
//        if (!$element) {
//            $element = $dom->getElementsByTagName('body')->item(0);
//
//            if ($element) {
//                // Remove <script>, <style>, and JSON data elements.
//                $nodesToRemove = $xpath->query('//script | //style | //noscript | //meta | //link | //iframe');
//                foreach ($nodesToRemove as $node) {
//                    $node->parentNode->removeChild($node);
//                }
//            }
//        }
//
//        if (!$element) {
//            return '';
//        }
//
//        // Check if the chosen element seems substantive (at least 3 paragraphs).
//        $pCount = $xpath->query(".//p", $element)->length;
//        if ($pCount < 3) {
//            return '';
//        }
//
//        // Extract inner HTML of the chosen element.
//        $innerHTML = '';
//        foreach ($element->childNodes as $child) {
//            $innerHTML .= $dom->saveHTML($child);
//        }
//
//        // Insert newlines around common block-level elements to preserve breaks.
//        $blockTags = 'p|div|li|br|h[1-6]|ul|ol|blockquote';
/*        $innerHTML = preg_replace("/<\s*($blockTags)(\s[^>]*)?>/i", "\n<$1$2>", $innerHTML);*/
//        $innerHTML = preg_replace("/<\/\s*($blockTags)\s*>/i", "</$1>\n", $innerHTML);
//
//        // Remove any remaining HTML tags.
//        $plainText = strip_tags($innerHTML);
//
//        // Normalize whitespace: collapse multiple newlines and spaces.
//        $plainText = preg_replace('/\n\s*\n/', "\n\n", $plainText);
//        $plainText = preg_replace('/[ \t]+/', ' ', $plainText);
//        $plainText = trim($plainText);
//
//        // If the resulting text is too short, assume it's not valid.
//        if (strlen($plainText) < 100) {
//            return '';
//        }
//
//        return $plainText;
//    }


}
