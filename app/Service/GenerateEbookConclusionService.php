<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class GenerateEbookConclusionService
{
    protected $aiClient;

    /**
     * Execute the conclusion generation process
     *
     * @param Campaign $campaign
     * @return void
     * @throws \Exception
     */
    public function execute(Campaign $campaign): void
    {
        $campaign->log("Starting conclusion chapter generation");
        DB::beginTransaction();
        try {
            $campaign->log("Calling AI to generate conclusion content");
            $conclusionResponse = $this->generateConclusion($campaign);
            $campaign->log("Generate conclusion response: " . formatLogMessage($conclusionResponse));

            $campaign->log("Formatting AI response for conclusion");
            $conclusionData = formatAIResponse($conclusionResponse);
            $campaign->log("Formatted Generated conclusion response: " . formatLogMessage($conclusionData));

            if (!isset($conclusionData['chapter_title']) || !isset($conclusionData['chapter_intro'])) {
                throw new \Exception("Invalid conclusion response format. Missing required fields.");
            }

            $config = \HTMLPurifier_Config::createDefault();
            $purifier = new \HTMLPurifier($config);

            $cleanChapterTitle = $purifier->purify($conclusionData['chapter_title']);
            $cleanChapterIntro = $purifier->purify($conclusionData['chapter_intro']);

            // Get the next chapter number
            $nextChapterNumber = $campaign->chapters()->max('chapter_number') + 1;
            $campaign->log("Next chapter number for conclusion: {$nextChapterNumber}");

            $campaign->log("Creating conclusion chapter in database");

            $conclusionChapter = Chapter::create([
                'campaign_id' => $campaign->id,
                'title' => $cleanChapterTitle,
                'intro' => $cleanChapterIntro,
                'chapter_number' => $nextChapterNumber,
                'status' => CampaignStatusEnum::DONE,
                'meta' => ['is_conclusion' => true], // Use meta to identify conclusion chapters
            ]);

            $campaign->log("Conclusion chapter created successfully with ID: {$conclusionChapter->id}");
            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            $campaign->log("Failed to generate conclusion chapter: " . formatLogMessage($e->getMessage()));
            $campaign->addErrorToMeta('conclusion_generation_failed', "Failed to generate conclusion chapter: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    /**
     * Generate conclusion content using AI
     *
     * @param Campaign $campaign
     * @return string
     * @throws \Exception
     */
    private function generateConclusion(Campaign $campaign): string
    {
        try {
            $campaign->log("Collecting chapter titles for conclusion");
            $chapterTitles = $this->getChapterTitles($campaign);
            $campaign->log("Collecting section details for conclusion");
            $sectionDetails = $this->getSectionDetails($campaign);

            $campaignData = [
                'title' => $campaign->title,
                'context' => $campaign->getForm("context"),
                'chapter_titles' => $chapterTitles,
                'section_details' => $sectionDetails,
                'topic' => $campaign->topic,
                'purpose' => $campaign->getForm("purpose"),
                'audience' => $campaign->getForm("audience"),
                'tone' => $campaign->getForm("tone"),
                'language' => $campaign->getForm("language"),
            ];

            $campaign->log("Campaign data for generate conclusion prompt: " . json_encode($campaignData));

            // Use specialized prompt for short reports
            $promptKey = $campaign->isShortReport() ? 'short_report_conclusion' : 'ebook_conclusion';
            $prompt = promptBuilder(prompts()[$promptKey], $campaignData);
            $prompt .= prompts()['ignore_terms'];

            $campaign->log("Prompt for generate conclusion: " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);

        } catch (\Exception $e) {
            $campaign->log("Failed to generate conclusion from AI model: " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating conclusion: ' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get all chapter titles for the campaign
     *
     * @param Campaign $campaign
     * @return string
     */
    private function getChapterTitles(Campaign $campaign): string
    {
        $chapters = $campaign->chapters()
            ->orderBy('chapter_number')
            ->get(['title', 'chapter_number']);

        $titles = $chapters->map(function ($chapter) {
            return "Chapter {$chapter->chapter_number}: " . strip_tags($chapter->title);
        })->implode("\n");

        return $titles;
    }

    /**
     * Get all section details including titles and key points
     *
     * @param Campaign $campaign
     * @return string
     */
    private function getSectionDetails(Campaign $campaign): string
    {
        $sections = $campaign->sections()
            ->with('chapter')
            ->orderBy('chapter_id')
            ->orderBy('order')
            ->get(['sections.title', 'sections.intro', 'sections.key_points', 'sections.chapter_id', 'sections.order']);

        $sectionDetails = [];

        foreach ($sections as $section) {
            $chapterNumber = $section->chapter->chapter_number;
            $sectionTitle = strip_tags($section->title);

            // Handle key_points which is stored as JSON string in database
            $keyPoints = '';
            if ($section->key_points) {
                try {
                    // Decode JSON string to array
                    $keyPointsArray = is_string($section->key_points)
                        ? json_decode($section->key_points, true)
                        : $section->key_points;

                    if (is_array($keyPointsArray)) {
                        // Extract only the text content from HTML, skip <h4> tags (titles) and get <p> content
                        $keyPointTexts = [];
                        foreach ($keyPointsArray as $keyPoint) {
                            $cleanText = strip_tags($keyPoint);
                            // Skip empty strings and very short content (likely just titles)
                            if (!empty($cleanText) && strlen($cleanText) > 10) {
                                $keyPointTexts[] = $cleanText;
                            }
                        }
                        $keyPoints = implode(' | ', $keyPointTexts);
                    }
                } catch (\Exception $e) {
                    // If JSON decode fails, treat as string
                    $keyPoints = strip_tags($section->key_points);
                }
            }

            $sectionDetails[] = "Chapter {$chapterNumber}, Section {$section->order}: {$sectionTitle}";
            if ($keyPoints) {
                $sectionDetails[] = "Key Points: {$keyPoints}";
            }
            $sectionDetails[] = ""; // Add empty line for readability
        }

        return implode("\n", $sectionDetails);
    }
}
