<?php

namespace App\Service;

use App\Mail\SubscriptionUpdateEmail;
use App\Mail\WelcomeEmail;
use App\Models\User;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Webhook;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class WarriorPlus
{
    function handleSale(array $data, Webhook $hook)
    {
        $password = Str::random(16);

        $user = User::firstOrCreate([
            'email' => $data['WP_BUYER_EMAIL'],
        ], [
            'name' => $data['WP_BUYER_NAME'] ?? str($data['WP_BUYER_EMAIL'])->explode('@')->first(),
            'is_active' => true,
            'password' => bcrypt($password),
            'slug' => generateNameFromEmail($data['WP_BUYER_EMAIL']),
        ]);

        $plan = Plan::firstOrCreate([
            'wplus_code' => $data['WP_ITEM_NUMBER'],
        ], [
            'name' => $data['WP_ITEM_NAME'],
            'amount' => $data['WP_SALE_AMOUNT'] ?? 0,
        ]);

        $createdSubscription = Subscription::firstOrCreate([
            'wplus_purchase_id' => $data['WP_SALE'],
        ], [
            'status' => 'active',
            'quantity' => 1,

            'wplus_txnid' => $data['WP_TXNID'],
            'wplus_sale_id' => $data['WP_SALEID'],
            'payment_method' => $data['WP_PAYMETHOD'] ?? 'WarriorPlus',

            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'subscribed_at' => now()->toDateTimeString(),
        ]);
        $user->credits += $plan->credits;
        $user->save();
        $newSubscription = $createdSubscription->wasRecentlyCreated;

        $createdSubscription->update([
            'status' => 'active',
            'wplus_txnid' => $data['WP_TXNID'],
            'wplus_sale_id' => $data['WP_SALEID'],
            'wplus_sale_amount' => $data['WP_SALE_AMOUNT'] ?? 0,
            'wplus_sale_earning' => $data['WP_SALE_EARNINGS_VENDOR'] ?? $data['WP_SUBSCR_EARNINGS_VENDOR'] ?? 0,
            'payment_method' => $createdSubscription->payment_method ?: $data['WP_PAYMETHOD'],
            'created_content_count' => 0, // reset created content count
            'cancelled_at' => null,
            'subscription_updated_at' => now()->toDateTimeString(),
        ]);

        $user->log("WarriorPlus: Subscription #{$createdSubscription->id} reset to 0 : webhook_data: " . json_encode($data, JSON_PRETTY_PRINT));

        if ($user->wasRecentlyCreated) {
            $user->log("WarriorPlus: New User #{$user->id} - {$user->email} created");
        }

        // if ($createdSubscription->is_active) {
        $user->wasRecentlyCreated
            ? Mail::to($user)->send(new WelcomeEmail($createdSubscription, $user, $password))
            : Mail::to($user)->send(new SubscriptionUpdateEmail($createdSubscription));

        if (!$user->is_active) {
            $user->log('User activated by WarriorPlus webhook');
            $user->update(['is_active' => true]);
        }
        // }

        // when subscription is created/updated, need to reset content quota check so that it can check again
        $user->update(['meta->content_quota' => []]);

        $hook->update([
            'user_id' => $user->id,
            'subscription_id' => $createdSubscription->id,
            'plan_id' => $plan->id,
            'action' => ($newSubscription ? "Created new" : "Updated existing") . " subscription #{$createdSubscription->id}"
        ]);

        return $hook->action;
    }

    function handleRefund(array $data, Webhook $hook)
    {
        $subscription = Subscription::where('wplus_purchase_id', $data['WP_SALE'])->first();

        if (!$subscription) {
            $subscription = Subscription::where('wplus_sale_id', $data['WP_SALEID'])->first();
        }

        if (($data['WP_ACTION'] ?? null) === 'subscr_cancelled') {
            $subscription?->update([
                'cancelled_at' => now()->toDateTimeString(),
            ]);
        } else {
            $subscription?->update([
                'status' => $data['WP_ACTION'] ?? 'cancelled',
            ]);
        }

        $hook->update([
            'user_id' => $subscription?->user_id,
            'subscription_id' => $subscription?->id,
            'plan_id' => $subscription?->plan_id,
            'action' => "Update Subscription Status to {$subscription->status} for #{$subscription->id}" . ($subscription->cancelled_at ? " - cancelled_at {$subscription->cancelled_at}" : ''),
        ]);

        return $hook->action;
    }
}
