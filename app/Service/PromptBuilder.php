<?php

namespace App\Service;

use App\Models\Option;

class PromptBuilder
{
    private array $prompts;

    function all(): array
    {
        $option = Option::where('key', 'prompts')->first();

        if ($option && $option->value) {
            $optionPrompt = json_decode($option->value, true);

            foreach (__('prompts') as $key => $prompt) {
                // $this->prompts[$section_key] = [];

                // foreach ($sections as $key => $prompt) {
                    $this->prompts[$key] = $optionPrompt[$key] ?? $prompt;
                // }
            }
        } else {
            $this->prompts = __('prompts');
        }

        return $this->prompts;
    }

    function getValidationRules(): array
    {
        $rules = [];

        foreach (__('prompts') as $section_key => $sections) {
            foreach ($sections as $key => $prompt) {
                $variables = $this->getVariables($prompt);

                $rules['prompts.'.$section_key.'.'.$key] = [
                    'required',
                    function ($attribute, $value, $fail) use ($variables) {
                        foreach ($variables as $variable) {
                            if (!str_contains($value, $variable)) {
                                $fail($attribute.' is missing the required variable '.$variable);
                            }
                        }
                    }
                ];
            }
        }

        return $rules;
    }

    function getVariables(string $prompt): array
    {
        preg_match_all('/:\w+/', $prompt, $variables);

        return collect($variables[0] ?? [])->map(fn($string) => $string)->unique()->filter()->toArray();
    }
}
