<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Symfony\Component\HttpFoundation\Response;

class EbookBestKeywordCategoryIdeaGenerationService
{
    protected $aiClient;
    public function generate(ResearchEbookCampaign $campaign)
    {
        try {
            $response = $this->generateBestKeywordCategories($campaign);
            $campaign->log("Generate best keyword and categories response: " . formatLogMessage($response));

            if(!$response){
                $campaign->update([
                    'status' => CampaignStatusEnum::FAILED,
                ]);
                $campaign->addErrorToMeta('best_keyword_failed', "Generate best keyword and categories response: " . formatLogMessage($response));

                throw new \Exception("Unable to generate keyword categories" . formatLogMessage($response));
            }

            $keywordCategories = formatAIResponse($response);
            $campaign->log("Formatted Generated best keyword categories response: " . formatLogMessage($keywordCategories));

            if (isset($keywordCategories['best_keyword_categories'])) {
                foreach ($keywordCategories['best_keyword_categories'] as $keywordCategory) {
                    ResearchCampaignEntry::create([
                        'entry' => $keywordCategory,
                        "research_ebook_campaign_id" => $campaign->id
                    ]);
                }
                return true;
            } else {
                $campaign->log("best_keyword_categories key not found in the AI response. " . formatLogMessage($keywordCategories));
                return false;
            }
        } catch (\Exception $e) {
            $campaign->log("Failed to update best keyword categories. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    /**
     * @param  Campaign  $campaign
     *
     * @return string
     */
    public function generateBestKeywordCategories(ResearchEbookCampaign $campaign): string
    {
        try {
            $campaignData = [
                'topic' => $campaign->getForm("topic"),
                'audience' => $campaign->getForm("audience"),
                'ebook_publishing_platform' => $campaign->getForm("ebook_publishing_platform"),
                'limit' => $campaign->getForm("limit")
            ];

            $campaign->log("Campaign data for generate best keyword categories prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['best_keyword_categories'], $campaignData);

            $campaign->log("Prompt for generate ebook best keyword categories " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate ebook best keyword categories from AI model. " . formatLogMessage($e->getMessage()));

            $campaign->addErrorToMeta('ai_model_error', "Failed to generate ebook best keyword categories from AI model. " . formatLogMessage($e->getMessage()));

            throw new \Exception('Error in generating ebook best keyword categories.' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
