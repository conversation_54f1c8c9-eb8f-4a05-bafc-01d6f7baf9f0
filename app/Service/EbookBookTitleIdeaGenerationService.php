<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Symfony\Component\HttpFoundation\Response;

class EbookBookTitleIdeaGenerationService
{
    protected $aiClient;
    public function generate(ResearchEbookCampaign $campaign)
    {
        try {
            $titleResponse = $this->generateTitles($campaign);
            $campaign->log("Generate titles response: " . formatLogMessage($titleResponse));

            if(!$titleResponse){
                $campaign->update([
                    'status' => CampaignStatusEnum::FAILED,
                ]);
                $campaign->addErrorToMeta('generate_title_failed', "Generate titles response: " . formatLogMessage($titleResponse));

                throw new \Exception("Unable to generate titles. Error: " . formatLogMessage($titleResponse));
            }

            $titleData = formatAIResponse($titleResponse);
            $campaign->log("Formatted Generated title response: " . formatLogMessage($titleData));

            if (isset($titleData['book_titles'])) {
                foreach ($titleData['book_titles'] as $title) {
                    ResearchCampaignEntry::create([
                        'entry' => $title,
                        "research_ebook_campaign_id" => $campaign->id
                    ]);
                }
                return true;
            } else {
                $campaign->log("book_titles key not found in the AI response. " . formatLogMessage($titleData));
                return false;
            }
        } catch (\Exception $e) {
            $campaign->log("Failed to update title. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    /**
     * @param  Campaign  $campaign
     *
     * @return string
     */
    public function generateTitles(ResearchEbookCampaign $campaign): string
    {
        try {
            $campaignData = [
                'topic' => $campaign->getForm("topic"),
                'audience' => $campaign->getForm("audience"),
                'genre' => $campaign->getForm("genre"),
                'tone' => $campaign->getForm("tone"),
                'key_aspect' => $campaign->getForm("key_aspect") ?? '',
                'limit' => $campaign->getForm("limit")
            ];

            $campaign->log("Campaign data for generate ebook titles prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['book_title_generation'], $campaignData);

            $campaign->log("Prompt for generate ebook titles " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate ebook titles from AI model. " . formatLogMessage($e->getMessage()));

            $campaign->addErrorToMeta('ai_model_error', "Failed to generate ebook titles from AI model. " . formatLogMessage($e->getMessage()));

            throw new \Exception('Error in generating ebook titles.' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
