<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Jobs\ImageProcessingJob;
use App\Models\Campaign;
use App\Service\Contracts\CampaignImageProcessingServiceInterface;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class CampaignImageProcessingJobService implements CampaignImageProcessingServiceInterface
{
    /**
     * @param  Campaign  $campaign
     *
     * @return void
     * @throws \Throwable
     */
    public function processImagesForCampaign(Campaign $campaign): void
    {
        $campaign->log("Randomly select sections to generate image.");
        $sections = $this->getSectionsToProcess($campaign);
        $campaign->log("Sections to generate images" . json_encode($sections));

        Bus::batch(
            $this->createImageProcessingJobs($campaign, $sections)
        )->then(function (Batch $batch) use ($campaign) {
            $campaign->log("All images have been processed.");
        })->finally(function (Batch $batch) use ($campaign) {
            $campaign->log("Updating campaign status to done");
            $campaign->update([
                'status' => CampaignStatusEnum::DONE->value,
            ]);
            //session()->forget("campaign_ebook_plan_" . $campaign->id);
        })->catch(function (Batch $batch, \Throwable $e) use ($campaign) {
            $campaign->log("Image processing batch failed. Error: " . formatLogMessage($e->getMessage()));
        })->onQueue($campaign->getImageQueueName())->dispatch();
    }

    /**
     * @param  Campaign  $campaign
     *
     * @return mixed
     */
    protected function getSectionsToProcess(Campaign $campaign)
    {
        $campaign->log("Total image count: " . $campaign->imageCount());
        return $campaign->sections->random(
            min($campaign->imageCount(), $campaign->sections->count())
        );
    }

    /**
     * @param  Campaign  $campaign
     * @param $sections
     *
     * @return mixed
     */
    protected function createImageProcessingJobs(Campaign $campaign, $sections)
    {
        return $sections->map(function ($section) use ($campaign) {
            return (new ImageProcessingJob($campaign, $section))->onQueue($section->getSectionQueueName());
        })->toArray();
    }
}
