<?php

namespace App\Service;

use App\Models\Campaign;
use App\Models\Chapter;
use App\Models\ResearchEbookCampaign;
use App\Models\Section;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class Logger
{
    private string $logDriver = 'daily';

    public function __construct(public string $requestId, public ResearchEbookCampaign|Campaign|Chapter|Section|User|null $model = null, public string $level = 'debug')
    {
        if ($model) {
            $modelKey = str(get_class($model))->explode('\\')->last().':'.$model?->getKey();
            if (str($requestId)->explode('\\')->last() == $modelKey) {
                $this->requestId = $modelKey;
            } else {
                $this->requestId = str($requestId)->explode('\\')->last().':'.$modelKey;
            }
        } else {
            $this->requestId = str($requestId)->explode('\\')->last();
        }
    }

    function log($message)
    {
        if (is_array($message)) {
            $message = json_encode($message);
        }

        if (app()->runningInConsole()) {
            echo $this->requestId.': '.$message.PHP_EOL;
        }

        $message = str_replace(PHP_EOL, '  ', $message);

        if (app()->isProduction() && $this->level === 'debug') {
            Log::log($this->level, $this->requestId.': '.str($message)->limit(500)->__toString());
        } else {
            Log::log($this->level, $this->requestId.': '.$message);
        }
    }
}
