<?php

namespace App\Service;

use App\Enum\AIModelEnum;
use App\Service\AIModel\Contracts\AIClientInterface;
use App\Service\AIModel\OpenAIClient;
use App\Service\AIModel\OpenRouterClient;

class AIClientFactory
{
    public function getClient(string $aiModel): AIClientInterface
    {
        $aiModel = strtolower($aiModel);
        return match ($aiModel) {
            //openai
            AIModelEnum::OPEN_AI->value,
            AIModelEnum::GPT_3_5_TURBO->value,
            //AIModelEnum::GPT_4->value,
            AIModelEnum::GPT_4_TURBO->value,
            AIModelEnum::GPT_4O_MINI->value,
            AIModelEnum::GPT_4O->value => new OpenAIClient(),
            //openrouter
            AIModelEnum::OPEN_ROUTER->value . "-" . AIModelEnum::OPENROUTER_DEEPSEEK_R1->value,
            AIModelEnum::OPEN_ROUTER->value . "-" . AIModelEnum::OPENROUTER_OPENAI_GPT_45->value,
            AIModelEnum::OPEN_ROUTER->value . "-" . AIModelEnum::OPENROUTER_ANTHROPIC_CLAUDE_3_7_SONNET->value,
            AIModelEnum::OPEN_ROUTER->value . "-" . AIModelEnum::OPENROUTER_META_LLAMA_33_70B_INSTRUCT->value,
            AIModelEnum::OPEN_ROUTER->value . "-" . AIModelEnum::OPENROUTER_GOOGLE_GEMINI_2_FLASH_001->value,
            AIModelEnum::OPEN_ROUTER->value . "-" . AIModelEnum::OPENROUTER_XAI_GROK_2->value => new OpenRouterClient(),
            default => throw new \Exception("Unsupported AI model: $aiModel"),
        };
    }
}
