<?php

namespace App\Service;

use App\Actions\StoreEbookPathAction;
use App\Models\Campaign;
use App\Service\Contracts\GenerateEbookInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Throwable;

class GenerateEbookAsPDF implements GenerateEbookInterface
{
    protected StoreEbookPathAction $storeEbookPathAction;

    public function __construct(StoreEbookPathAction $storeEbookPathAction)
    {
        $this->storeEbookPathAction = $storeEbookPathAction;
    }

    public function generateEbook(int $ebookId): void
    {
        try {
            // Fetch the ebook with chapters and sections
            //$ebook = Campaign::with(['chapters.sections', "user"])->findOrFail($ebookId);
            $ebook = Campaign::with(['chapters' => function ($query) {
                $query->orderBy('chapter_number');  // Order chapters by chapter_number
            }, 'chapters.sections' => function ($query) {
                $query->orderBy('order', 'asc');  // Order sections by order
            }, 'user'])->findOrFail($ebookId);
            // Check and delete the previous file if it exists
//            $ebook->log("Delete if exist ebook");
//            $this->deleteExistingEbook($ebook);

            // Render the Blade view as HTML
            $ebook->log("rendering ebook view");
            $html = $this->renderEbookView($ebook);

            $ebook->log("Generating PDF with HTML length: " . strlen($html));
            // Generate a valid file path
            $ebook->log("Generating ebook file path");
            $filePath = $this->generateFilePath($ebook);

            // Ensure the directory exists
            $ebook->log("Ensuring directory exist ebooks/pdf");
            $this->ensureDirectoryExists('ebooks/pdf');


            // Generate and store the PDF
            $ebook->log("Generating ebook as PDF");
            $bg_image = "";
            $bg_opacity = 1;
            $format = $ebook->ebookFormat;
            if ($format->background_image) {
                $bg_opacity = $format->background_opacity ?? 1;
                $bg_image = Storage::disk('s3')->temporaryUrl($format->background_image, now()->addMinutes(30));
            }
            $this->generatePDF($html, $filePath, $ebook, $ebook->ebookFormat->page_size, $bg_image, $bg_opacity);

            // Save the new path in the database
            $ebook->log("Storing ebook path to db");
            $this->storeEbookPathAction->store($ebookId, $filePath, "pdf");
        } catch (MpdfException $e) {
            $ebook = Campaign::findOrFail($ebookId);
            $ebook->log("PDF Generation Failed: " . formatLogMessage($e->getMessage()));
            throw new \RuntimeException('Failed to generate the PDF. Please try again.');
        } catch (Throwable $e) {
            $ebook = Campaign::findOrFail($ebookId);
            $ebook->log("An error occurred: " . formatLogMessage($e->getMessage()));
            throw new \RuntimeException('An unexpected error occurred. Please contact support.' . $e->getMessage());
        }
    }

    private function renderEbookView(Campaign $ebook): string
    {
        $format = $ebook->ebookFormat;
        return view('ebook.ebook-pdf', compact('ebook', 'format'))->render();
    }

    private function generateFilePath(Campaign $ebook): string
    {
        $fileName = Str::slug(strip_tags($ebook->title)) . "_" . time() . '.pdf';
        return 'ebooks/pdf/' . $fileName;
    }

    private function ensureDirectoryExists(string $directory): void
    {
        if (!Storage::disk("s3")->exists($directory)) {
            Storage::disk("s3")->makeDirectory($directory);
        }
    }

    private function deleteExistingEbook(Campaign $ebook): void
    {
        // Check if the campaign has an existing file path
        if ($ebook->getPdf() && isS3FileExist($ebook->getPdf())) {
            Cache::forget($ebook->getPdf());
            Storage::disk("s3")->delete($ebook->getPdf());
        }
    }

    private function generatePDF(string $html, string $filePath, $ebook, string $pageSize = 'A4', string $bg_image = "", $bg_opacity = 1): void
    {
//        $config = \HTMLPurifier_Config::createDefault();
//        $purifier = new \HTMLPurifier($config);
//        $cleanHtml = $purifier->purify($html);

        $coverImage = $ebook->cover_image ?? null;

        try {
            $mpdf = $this->createMpdfInstance($pageSize);

            if ($coverImage && $ebook->getForm("generate_cover_image") == 'ai_generate') {
                $coverHtml = $this->generateCoverHtmlOverlayText($ebook, $coverImage);
                $this->addCoverPage($mpdf, $coverHtml, $ebook);
            }

            if ($coverImage && $ebook->getForm("generate_cover_image") == 'custom_upload' && Storage::disk('s3')->exists($coverImage)) {
                $coverHtml = $this->generateCoverHtmlImageOnly($ebook, $coverImage);
                $this->addCoverPage($mpdf, $coverHtml, $ebook);
            }

            $this->addContentPageSetup($mpdf, $bg_image,$bg_opacity, $ebook);
            $this->setAffiliateFooter($mpdf, $ebook);
            $mpdf->WriteHTML($html);

            $pdfContent = $mpdf->Output('', \Mpdf\Output\Destination::STRING_RETURN);

            Storage::disk('s3')->put($filePath, $pdfContent, 'public');
        } catch (\Mpdf\MpdfException $e) {
            logger()->error("PDF Writing Error: {$e->getMessage()}");
            throw new \RuntimeException('Failed to write the PDF file.');
        }
    }


    private function createMpdfInstance(string $pageSize): \Mpdf\Mpdf
    {
        return new \Mpdf\Mpdf([
            'format' => ucfirst($pageSize),
            'margin-left' => 20,
            'margin-right' => 20,
            'margin-top' => 20,
            'margin-bottom' => 20,
        ]);
    }

    private function generateCoverHtmlOverlayText($ebook, string $coverImage): string
    {
        return '
        <div style="
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background-image: url(\'' . $coverImage . '\');
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            margin: 0;
            padding: 0;">
            <div style="
                position: relative;
                text-align: center;
                padding-top: 100px;
                font-size: 28px;
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);">
                ' . '' . '
            </div>
        </div>';
    }

    private function generateCoverHtmlImageOnly($ebook, string $coverImagePath): string
    {
        //$coverImagePath = storage_path('app/public/' . $coverImage);
        $coverImagePath = Storage::disk('s3')->url($coverImagePath);

        //                $coverHtml = '
        //                <div style="
        //                    position: absolute;
        //                    top: 0;
        //                    left: 0;
        //                    right: 0;
        //                    bottom: 0;
        //                    width: 100%;
        //                    height: 100%;
        //                    background-image: url(\'' . $coverImagePath . '\');
        //                    background-size: 100% 100%;
        //                    background-position: center;
        //                    background-repeat: no-repeat;
        //                    margin: 0;
        //                    padding: 0;">
        //                    <div style="
        //                        position: relative;
        //                        text-align: center;
        //                        padding-top: 100px;
        //                        font-size: 28px;
        //                        color: #ffffff;
        //                        font-weight: bold;
        //                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);">
        //                        ' . $ebook->title . '
        //                    </div>
        //                </div>';

        return '
        <div style="
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background-image: url(\'' . $coverImagePath . '\');
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            margin: 0;
            padding: 0;">
        </div>';
    }

    private function addCoverPage(\Mpdf\Mpdf $mpdf, string $coverHtml, $ebook): void
    {
        $mpdf->AddPageByArray([
            'margin-left' => 0,
            'margin-right' => 0,
            'margin-top' => 0,
            'margin-bottom' => 0,
        ]);

        $this->setAffiliateFooter($mpdf, $ebook);
        $mpdf->WriteHTML($coverHtml);
    }

    private function setAffiliateFooter(\Mpdf\Mpdf $mpdf, $ebook): void
    {
        $footerHtml = '
        <table style="width: 100%; font-size: 14px; color: #000; padding: 0 20px;">
            <tr>
                <td style="text-align: left;">
                    <a href="' . $ebook->getForm("affiliate_link_url") . '" target="_blank" style="color: #000; text-decoration: none; font-size: 14px;">
                        ' . $ebook->getForm("affiliate_link_keyword") . '
                    </a>
                </td>
                <td style="text-align: right;">
                    Page {PAGENO} of {nb}
                </td>
            </tr>
        </table>';

        $mpdf->SetHTMLFooter($footerHtml);
    }

    private function addContentPageSetup(\Mpdf\Mpdf $mpdf, string $bg_image, $bg_opacity, $ebook = null): void
    {
        // Get custom margin settings if available
        $margins = [
            'margin-left' => 20,
            'margin-right' => 20,
            'margin-top' => 20,
            'margin-bottom' => 20,
        ];

        if ($ebook && $ebook->ebookFormat && $ebook->ebookFormat->margins) {
            $customMargins = json_decode($ebook->ebookFormat->margins, true);
            if (is_array($customMargins)) {
                if (isset($customMargins['left'])) {
                    $margins['margin-left'] = (int)$customMargins['left'];
                }
                if (isset($customMargins['right'])) {
                    $margins['margin-right'] = (int)$customMargins['right'];
                }
                if (isset($customMargins['top'])) {
                    $margins['margin-top'] = (int)$customMargins['top'];
                }
                if (isset($customMargins['bottom'])) {
                    $margins['margin-bottom'] = (int)$customMargins['bottom'];
                }
            }
        }

        $mpdf->AddPageByArray($margins);

        if ($bg_image != "") {
            $mpdf->SetDefaultBodyCSS('background', "url('$bg_image')");
            $mpdf->SetDefaultBodyCSS('background-image-resize', 6);
            $mpdf->SetDefaultBodyCSS('background-image-opacity', $bg_opacity);
            //log the background image and opacity for debugging
            Log::info("Setting background image with opacity: $bg_opacity");
        }
    }
}
