<?php

namespace App\Service;

use App\Enum\SiteFeaturesEnum;
use Exception;
use Illuminate\Support\Facades\Http;
use Throwable;

class OpenAIService
{
    /**
     * @throws \Exception
     */
    public static function callApi($endPoint, $data, $openaiApiKey)
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $openaiApiKey,
            ])
                ->timeout(300)
                ->post($endPoint, $data);

            if ($response->failed()) {
                throw new \Exception($response->json()['error']['message'] ?? 'An error occurred while calling the OpenAI API.');
            }

            return $response;
        } catch (Exception | Throwable $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function generateComment($title, $limit, $model = 'gpt-3.5-turbo')
    {
        $openaiApiKey = config('services.openai.api_key');
        $prompt = "generate" . $limit . " responses or comments for ".platformName()." ".(isFeatureEnabled(SiteFeaturesEnum::YOUTUBE->value)?' video':' post')." with title" . $title . " separate each comment with new line. Dont start line with numbers or letters.";

        try {
            $response = self::callApi('https://api.openai.com/v1/chat/completions', [
                'model' => $model ?: 'gpt-3.5-turbo',
                'messages' => [
                    ['role' => 'user', 'content' => $prompt],
                ],
            ], $openaiApiKey);

            $content = null;

            try {
                $content = $response->json('choices.0.message.content');
            } catch (Exception | Throwable $e) {
                throw new Exception($e->getMessage());
            }

            if ($content) {
                // $content is now an array containing your list of titles
                return $content;
            } else {
                // Handle the case where decoding the JSON content failed
                throw new Exception('Failed to decode JSON content');
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}
