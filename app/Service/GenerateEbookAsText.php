<?php

namespace App\Service;

use App\Actions\StoreEbookPathAction;
use App\Models\Campaign;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GenerateEbookAsText
{
    public function __construct(
        private StoreEbookPathAction $storeEbookPathAction
    ) {}

    /**
     * Generate a plain text version of the ebook
     */
    public function generateEbook(int $ebookId): void
    {
        try {
            // Fetch the ebook with chapters and sections
            $ebook = Campaign::with(['chapters' => function ($query) {
                $query->orderBy('chapter_number');
            }, 'chapters.sections' => function ($query) {
                $query->orderBy('order');
            }, 'user'])->findOrFail($ebookId);

            $ebook->log("Generating text version of ebook");

            // Extract plain text content
            $textContent = $this->extractTextContent($ebook);

            // Generate file path
            $filePath = $this->generateFilePath($ebook);

            // Save the text file to S3
            $this->saveTextFile($textContent, $filePath);

            // Store the path in database
            $ebook->log("Storing text ebook path to db");
            $this->storeEbookPathAction->store($ebookId, $filePath, "txt");

        } catch (\Exception $e) {
            $ebook = Campaign::findOrFail($ebookId);
            $ebook->log("Text Generation Failed: " . formatLogMessage($e->getMessage()));
            throw new \RuntimeException('Failed to generate the text file. Please try again.');
        }
    }

    /**
     * Extract plain text content from campaign
     */
    private function extractTextContent(Campaign $ebook): string
    {
        $content = [];

        // Add title and author
        $content[] = strtoupper(strip_tags($ebook->title));
        $content[] = str_repeat('=', strlen(strip_tags($ebook->title)));
        $content[] = '';
        $content[] = 'Author: ' . ($ebook->author ?? $ebook->user->name);
        $content[] = '';
        $content[] = str_repeat('-', 50);
        $content[] = '';

        // Process each chapter
        foreach ($ebook->chapters as $chapter) {
            // Chapter title
            $chapterTitle = $this->cleanText($chapter->title);
            $content[] = strtoupper($chapterTitle);
            $content[] = str_repeat('=', strlen($chapterTitle));
            $content[] = '';

            // Chapter intro
            if (!empty($chapter->intro)) {
                $chapterIntro = $this->cleanText($chapter->intro);
                $content[] = $chapterIntro;
                $content[] = '';
            }

            // Process sections
            foreach ($chapter->sections as $section) {
                // Section title
                $sectionTitle = $this->cleanText($section->title);
                // Remove numbering from section title
                $sectionTitle = preg_replace('/^\d+(\.\d+)*\s*/', '', $sectionTitle);

                $content[] = $sectionTitle;
                $content[] = str_repeat('-', strlen($sectionTitle));
                $content[] = '';

                // Section intro
                if (!empty($section->intro)) {
                    $sectionIntro = $this->cleanText($section->intro);
                    $content[] = $sectionIntro;
                    $content[] = '';
                }

                // Section body
                if (!empty($section->body)) {
                    $sectionBody = $this->cleanText($section->body);
                    $content[] = $sectionBody;
                    $content[] = '';
                }
            }

            // Add separator between chapters
            $content[] = str_repeat('*', 50);
            $content[] = '';
        }

        return implode("\n", $content);
    }

    /**
     * Clean HTML content and convert to plain text
     */
    private function cleanText(string $html): string
    {
        if (empty($html)) {
            return '';
        }

        // Remove script and style elements completely
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Convert common HTML entities
        $html = html_entity_decode($html, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Handle line break elements first - use a unique placeholder
        $html = preg_replace('/<br\s*\/?>/i', '###LINEBREAK###', $html);

        // Add line breaks around block elements to ensure proper separation
        $blockElements = ['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'section', 'article', 'header', 'footer', 'main', 'aside'];
        foreach ($blockElements as $element) {
            // Add line breaks before opening tags
            $html = preg_replace("/<\s*{$element}(\s[^>]*)?>/i", "\n###BLOCKSTART###<{$element}$1>", $html);
            // Add line breaks after closing tags
            $html = preg_replace("/<\/\s*{$element}\s*>/i", "</{$element}>###BLOCKEND###\n", $html);
        }

        // Handle list containers with proper spacing
        $html = preg_replace('/<ul(\s[^>]*)?>/i', "\n###BLOCKSTART###<ul$1>", $html);
        $html = preg_replace('/<\/ul>/i', "</ul>###BLOCKEND###\n", $html);
        $html = preg_replace('/<ol(\s[^>]*)?>/i', "\n###BLOCKSTART###<ol$1>", $html);
        $html = preg_replace('/<\/ol>/i', "</ol>###BLOCKEND###\n", $html);

        // Handle ordered lists with numbers
        $html = preg_replace_callback('/<ol(\s[^>]*)?>(.*?)<\/ol>/is', function($matches) {
            $content = $matches[2];
            $items = preg_split('/<li(\s[^>]*)?>/i', $content);
            $result = "\n";
            $counter = 1;
            foreach ($items as $item) {
                $item = trim(preg_replace('/<\/li>/i', '', $item));
                $item = strip_tags($item);
                if (!empty($item)) {
                    $result .= "{$counter}. " . trim($item) . "\n";
                    $counter++;
                }
            }
            return $result;
        }, $html);

        // Handle unordered lists with bullets
        $html = preg_replace_callback('/<ul(\s[^>]*)?>(.*?)<\/ul>/is', function($matches) {
            $content = $matches[2];
            $items = preg_split('/<li(\s[^>]*)?>/i', $content);
            $result = "\n";
            foreach ($items as $item) {
                $item = trim(preg_replace('/<\/li>/i', '', $item));
                $item = strip_tags($item);
                if (!empty($item)) {
                    $result .= "• " . trim($item) . "\n";
                }
            }
            return $result;
        }, $html);

        // Remove all HTML tags first
        $text = strip_tags($html);

        // Now restore all placeholders to proper line breaks
        $text = str_replace('###LINEBREAK###', "\n", $text);
        $text = str_replace('###BLOCKSTART###', '', $text);
        $text = str_replace('###BLOCKEND###', "\n", $text);

        // Clean up excessive whitespace while preserving intentional line breaks
        $text = preg_replace('/[ \t]+/', ' ', $text); // Multiple spaces/tabs to single space
        $text = preg_replace('/^[ \t]+|[ \t]+$/m', '', $text); // Trim spaces/tabs from each line but preserve newlines
        $text = preg_replace('/\n{3,}/', "\n\n", $text); // 3+ line breaks to double

        return trim($text);
    }

    /**
     * Generate file path for the text file
     */
    private function generateFilePath(Campaign $ebook): string
    {
        $fileName = Str::slug(strip_tags($ebook->title)) . "_" . now()->format('Y-m-d') . '.txt';
        return 'ebooks/txt/' . $fileName;
    }

    /**
     * Save text content to S3
     */
    private function saveTextFile(string $content, string $filePath): void
    {
        try {
            Storage::disk('s3')->put($filePath, $content, 'public');
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed to save the text file to storage.');
        }
    }
}
