<?php

namespace App\Service;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class ChangelogJsonService
{
    private const CHANGELOG_FILE = 'changelog.json';
    
    /**
     * Get all updates from JSON file
     */
    public function getAllUpdates(): array
    {
        $data = $this->loadChangelogData();
        return $data['updates'] ?? [];
    }

    /**
     * Get a specific update by ID
     */
    public function getUpdate(string $id): ?array
    {
        $updates = $this->getAllUpdates();
        
        foreach ($updates as $update) {
            if ($update['id'] === $id) {
                return $update;
            }
        }
        
        return null;
    }

    /**
     * Add a new update to the changelog
     */
    public function addUpdate(array $updateData, bool $syncToDatabase = true): void
    {
        $this->validateUpdateData($updateData);

        $data = $this->loadChangelogData();

        // Generate ID if not provided
        if (!isset($updateData['id'])) {
            $updateData['id'] = 'update-' . str_pad((count($data['updates']) + 1), 3, '0', STR_PAD_LEFT);
        }

        // Set released_at if not provided
        if (!isset($updateData['released_at'])) {
            $updateData['released_at'] = now()->toISOString();
        }

        // Add to beginning of updates array (newest first)
        array_unshift($data['updates'], $updateData);

        // Update metadata
        $data['last_updated'] = now()->toISOString();
        $data['metadata']['total_updates'] = count($data['updates']);
        $this->updateVersionMetadata($data, $updateData);

        $this->saveChangelogData($data);

        // Sync to database unless explicitly disabled
        if ($syncToDatabase) {
            $this->syncToDatabase();
        }
    }

    /**
     * Update an existing update
     */
    public function updateUpdate(string $id, array $updateData, bool $syncToDatabase = true): bool
    {
        $this->validateUpdateData($updateData);

        $data = $this->loadChangelogData();

        foreach ($data['updates'] as $index => $update) {
            if ($update['id'] === $id) {
                $data['updates'][$index] = array_merge($update, $updateData);
                $data['last_updated'] = now()->toISOString();
                $this->updateVersionMetadata($data, $data['updates'][$index]);
                $this->saveChangelogData($data);

                // Sync to database unless explicitly disabled
                if ($syncToDatabase) {
                    $this->syncToDatabase();
                }

                return true;
            }
        }

        return false;
    }

    /**
     * Delete an update
     */
    public function deleteUpdate(string $id, bool $syncToDatabase = true): bool
    {
        $data = $this->loadChangelogData();

        foreach ($data['updates'] as $index => $update) {
            if ($update['id'] === $id) {
                // If this was a database-synced update, also delete from database
                if ($syncToDatabase && str_starts_with($update['id'], 'db-update-')) {
                    $dbId = str_replace('db-update-', '', $update['id']);
                    \App\Models\AppUpdate::where('id', $dbId)->delete();
                } elseif ($syncToDatabase && isset($update['version'])) {
                    // For non-db updates, delete by version
                    \App\Models\AppUpdate::where('version', $update['version'])->delete();
                }

                unset($data['updates'][$index]);
                $data['updates'] = array_values($data['updates']); // Re-index array
                $data['last_updated'] = now()->toISOString();
                $data['metadata']['total_updates'] = count($data['updates']);
                $this->saveChangelogData($data);
                return true;
            }
        }

        return false;
    }

    /**
     * Import updates from JSON data
     */
    public function importFromJson(string $jsonData, bool $syncToDatabase = true): void
    {
        $importData = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON data: ' . json_last_error_msg());
        }

        if (!isset($importData['updates']) || !is_array($importData['updates'])) {
            throw new \InvalidArgumentException('JSON must contain an "updates" array');
        }

        foreach ($importData['updates'] as $update) {
            $this->validateUpdateData($update);
        }

        $this->saveChangelogData($importData);

        // Automatically sync to database unless explicitly disabled
        if ($syncToDatabase) {
            $this->syncToDatabase();
        }
    }

    /**
     * Export changelog as JSON
     */
    public function exportToJson(): string
    {
        $data = $this->loadChangelogData();
        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Sync database updates to JSON file
     */
    public function syncFromDatabase(): void
    {
        $updates = \App\Models\AppUpdate::published()
            ->orderBy('released_at', 'desc')
            ->get();

        $jsonUpdates = [];

        foreach ($updates as $update) {
            $jsonUpdates[] = [
                'id' => 'db-update-' . $update->id,
                'version' => $update->version,
                'title' => $update->title,
                'summary' => $update->summary,
                'content' => $update->content,
                'type' => $update->type,
                'category' => $update->category,
                'released_at' => $update->released_at->toISOString(),
                'metadata' => $update->metadata ?? []
            ];
        }

        $data = [
            'version' => '1.0.0',
            'last_updated' => now()->toISOString(),
            'updates' => $jsonUpdates,
            'schema_version' => '1.0',
            'metadata' => [
                'total_updates' => count($jsonUpdates),
                'synced_from_database' => true,
                'sync_date' => now()->toISOString()
            ]
        ];

        $this->saveChangelogData($data);
    }

    /**
     * Sync JSON updates to database
     */
    public function syncToDatabase(): void
    {
        $data = $this->loadChangelogData();
        $updates = $data['updates'] ?? [];
        foreach ($updates as $updateData) {
            // Check if update already exists in database by version
            $existingUpdate = \App\Models\AppUpdate::where('version', $updateData['version'])->first();

            $updateFields = [
                'title' => $updateData['title'],
                'summary' => $updateData['summary'] ?? '',
                'content' => $updateData['content'] ?? '',
                'type' => $updateData['type'] ?? 'minor',
                'category' => $updateData['category'] ?? 'features',
                'released_at' => isset($updateData['released_at']) ?
                    \Carbon\Carbon::parse($updateData['released_at']) : now(),
                'metadata' => $updateData['metadata'] ?? null,
                'is_published' => true,
            ];

            // Include images if they exist in the JSON data
            if (isset($updateData['images']) && is_array($updateData['images'])) {
                $updateFields['images'] = $updateData['images'];
            }

            if ($existingUpdate) {
                // Update existing record
                $existingUpdate->update($updateFields);
            } else {
                // Create new record
                $createFields = array_merge($updateFields, [
                    'version' => $updateData['version'],
                    'send_email_notification' => false, // Don't auto-send emails for imported updates
                ]);

                \App\Models\AppUpdate::create($createFields);
            }
        }
    }

    /**
     * Load changelog data from JSON file
     */
    private function loadChangelogData(): array
    {
        if (!Storage::exists(self::CHANGELOG_FILE)) {
            return $this->getDefaultChangelogStructure();
        }
        
        $content = Storage::get(self::CHANGELOG_FILE);
        $data = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException('Failed to parse changelog JSON: ' . json_last_error_msg());
        }
        
        return $data;
    }

    /**
     * Save changelog data to JSON file
     */
    private function saveChangelogData(array $data): void
    {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException('Failed to encode changelog JSON: ' . json_last_error_msg());
        }
        
        Storage::put(self::CHANGELOG_FILE, $json);
    }

    /**
     * Validate update data structure
     */
    private function validateUpdateData(array $data): void
    {
        $validator = Validator::make($data, [
            'version' => 'required|string',
            'title' => 'required|string|max:255',
            'summary' => 'required|string|max:500',
            'content' => 'required|string',
            'type' => 'required|in:major,minor,patch',
            'category' => 'required|in:features,fixes,improvements,security',
            'released_at' => 'sometimes|date',
            'metadata' => 'sometimes|array'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Update version metadata
     */
    private function updateVersionMetadata(array &$data, array $update): void
    {
        if (!isset($data['metadata'])) {
            $data['metadata'] = [];
        }
        
        switch ($update['type']) {
            case 'major':
                $data['metadata']['last_major_version'] = $update['version'];
                break;
            case 'minor':
                $data['metadata']['last_minor_version'] = $update['version'];
                break;
            case 'patch':
                $data['metadata']['last_patch_version'] = $update['version'];
                break;
        }
    }

    /**
     * Get default changelog structure
     */
    private function getDefaultChangelogStructure(): array
    {
        return [
            'version' => '1.0.0',
            'last_updated' => now()->toISOString(),
            'updates' => [],
            'schema_version' => '1.0',
            'metadata' => [
                'total_updates' => 0
            ]
        ];
    }
}
