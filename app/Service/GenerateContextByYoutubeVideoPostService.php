<?php

namespace App\Service;

use App\Actions\SummarizeContentAction;
use App\Models\Campaign;

class GenerateContextByYoutubeVideoPostService
{
    protected $campaign;

    protected SummarizeContentAction $summarizeContentAction;

    public function __construct(
        Campaign $campaign,
        SummarizeContentAction $summarizeContentAction
    )
    {
        $this->campaign = $campaign;
        $this->summarizeContentAction = $summarizeContentAction;
    }

    public function generateContext()
    {
        $youtubeUrl = $this->campaign->getForm("youtube_url");

        if ( ! $youtubeUrl || !YoutubeVideo::fromUrl($youtubeUrl)) {
            $this->campaign->log("Youtube URL is missing or invalid.");
            return $this->campaign->topic;
        }

        $youtubeVideo = YoutubeVideo::fromUrl($youtubeUrl);
        $youtubeId = $youtubeVideo->id;
        $language = $this->campaign->getForm("language");

//        $transcript = app(\App\Service\YTSubtitleByDataForSeo::class)
//            ->fetchMergedSubtitles($this->campaign, $youtubeId, $language, 2840);

        $transcript = YTSubtitleByDataForSeo::fetchMergedSubtitles($this->campaign, $youtubeId, $language, 2840);

        $summary = "";

        if( !empty($transcript) || $transcript == null ){
            $summary = $this->summarizeContentAction->execute($transcript, $this->campaign) ?? "";
        }

        return !empty($summary) ? $summary : $this->campaign->topic;



    }
}
