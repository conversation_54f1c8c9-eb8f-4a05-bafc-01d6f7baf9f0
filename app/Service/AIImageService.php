<?php

namespace App\Service;

use App\Enum\AIModelEnum;
use App\Enum\ImageSources;
use App\Models\Campaign;
use App\Models\Section;
use App\Service\AIModel\Contracts\AIClientInterface;
use App\Service\Contracts\ImageGenerateInterface;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class AIImageService implements ImageGenerateInterface
{
    protected $aiClient;

//    public function __construct(AIClientInterface $aiClient)
//    {
//        $this->aiClient = $aiClient;
//    }
    public function generateImage(Campaign $campaign, Section $section): string
    {
        try {
            $campaignData = [
                'topic' => $campaign->topic,
                'section_title' => strip_tags($section->title),
                'chapter_title' => strip_tags($section->chapter->title),
                'image_style' => $campaign->getForm("image_style"),
            ];

            $campaign->log("Campaign data for generate ebook section's image prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['image_generation'], $campaignData);

            $campaign->log("Prompt for generate ebook section's image " . formatLogMessage($prompt));
            $this->aiClient = app(AIClientInterface::class, ['aiModel' => AIModelEnum::OPEN_AI->value]);
            return $this->aiClient->generateImage($prompt, $campaign->getOpenAiApiKey(), $campaign->getForm("ai_image_model"), $campaign->getForm("ai_image_model") == ImageSources::GPT_IMAGE_1 ? "1536x1024" : "1792x1024");

        } catch (\Exception $e) {
            $campaign->log("Failed to generate image from AI model. For section {$section->id} Error: " . formatLogMessage($e->getMessage()));
            throw new \Exception("Error in generating image. Error: " . formatLogMessage($e->getMessage()), Response::HTTP_BAD_REQUEST);
        }
    }
}
