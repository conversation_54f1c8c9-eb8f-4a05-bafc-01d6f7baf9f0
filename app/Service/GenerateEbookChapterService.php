<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class GenerateEbookChapterService
{
    protected $aiClient;

//    public function __construct(AIClientInterface $aiClient)
//    {
//        $this->aiClient = $aiClient;
//    }

    /**
     * @param  Campaign  $campaign
     *
     * @return void
     * @throws \Exception
     */
    public function execute(Campaign $campaign)
    {
        DB::beginTransaction();
        try {
            $chapterResponse = $this->generateChapters($campaign);
            $campaign->log("Generate chapters response: " . formatLogMessage($chapterResponse));
            $chapters = formatAIResponse($chapterResponse);
            $campaign->log("Formatted Generated chapters response: " . formatLogMessage($chapters));

            $config = \HTMLPurifier_Config::createDefault();
            $purifier = new \HTMLPurifier($config);

            foreach ($chapters as $key => $value) {

                $campaign->log("Creating chapters to db");
                $titlePlainText = strip_tags($value['chapter_title']);
                $introPlainText = strip_tags($value['chapter_intro']);
                $totalWords = str_word_count($titlePlainText) + str_word_count($introPlainText);

                $cleanChapterTitle = $purifier->purify($value['chapter_title']);
                $cleanChapterIntro = $purifier->purify($value['chapter_intro']);

                $chapter = Chapter::create([
                    'campaign_id' => $campaign->id,
                    'title' => $cleanChapterTitle,
                    'intro' => $cleanChapterIntro,
                    'status' => CampaignStatusEnum::IN_PROGRESS,
                    'chapter_number' => $key + 1,
                    'chapter_total_words' => $totalWords,
                ]);

                $campaign = Campaign::find($campaign->id);

                $campaign->log("Updating campaign total word length: GenerateEbookChapterService: {$campaign->total_word_length} + {$chapter->chapter_total_words}");

                $campaign->update([
                    "total_word_length" => $campaign->total_word_length + $chapter->chapter_total_words
                ]);
                $campaign->log("Creating chapters to db finished");
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            $campaign->log("Failed to generate chapters. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    /**
     * @param  Campaign  $campaign
     *
     * @return string
     */
    private function generateChapters(Campaign $campaign): string
    {
        $requiredWords = $campaign->required_word_length;
        $plan = $campaign->ebook_plan;
        try {
            $campaignData = [
                'topic' => $campaign->topic,
                'title' => $campaign->title,
                'context' => $campaign->getForm("context"),
                'purpose' => $campaign->getForm("purpose"),
                'audience' => $campaign->getForm("audience"),
                'tone' => $campaign->getForm("tone"),
                'language' => $campaign->getForm("language"),
                'required_words' => $requiredWords,
            ];
            if (is_array($plan)) {
                $campaignData = array_merge($campaignData, $plan);
            }


            $campaign->log("Campaign data for generate ebook chapters prompt " . json_encode($campaignData));

            // Use specialized prompt for short reports
            $promptKey = $campaign->isShortReport() ? 'short_report_chapters' : 'ebook_chapters';
            $prompt = promptBuilder(prompts()[$promptKey], $campaignData);
            $prompt .= prompts()['ignore_terms'];

            $campaign->log("Prompt for generate ebook chapters " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);

        } catch (\Exception $e) {
            $campaign->log("Failed to generate chapters from AI model. Error: " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error: ' . formatLogMessage($e->getMessage()), Response::HTTP_BAD_REQUEST);
        }
    }

}
