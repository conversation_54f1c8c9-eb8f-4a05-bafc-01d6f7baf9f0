<?php

namespace App\Service;

use App\Models\Campaign;
use Illuminate\Support\Facades\Http;

class YTSubtitleByDataForSeo
{
    protected string $baseUrl;
    protected string $email;
    protected string $password;

    public static function fetchMergedSubtitles(
        Campaign $campaign,
        string $videoId,
        string $languageName = 'English',
        int $locationCode = 2840
    ): ?string
    {
        try {
            $email = config('services.dataforseo.email');
            $password = config('services.dataforseo.password');
            $baseUrl = rtrim(config('services.dataforseo.base_url'), '/') . '/';
            $auth = base64_encode("{$email}:{$password}");

            $response = Http::withHeaders([
                'Authorization' => "Basic {$auth}",
                'Content-Type'  => 'application/json',
            ])->post("{$baseUrl}serp/youtube/video_subtitles/live/advanced", [
                [
                    'language_name' => $languageName,
                    'location_code' => $locationCode,
                    'video_id'      => $videoId,
                ]
            ]);

            if ($response->failed()) {
                $campaign->log("DataForSEO API failed. video_id {$videoId} language_name {$languageName} location_code {$locationCode}. Error: " . formatLogMessage($response->body()));
                return null;
            }

            $data = $response->json();

            $items = $data['tasks'][0]['result'][0]['items'] ?? [];

            if (empty($items)) {
                $campaign->log("No subtitles returned for video_id {$videoId} language_name {$languageName} location_code {$locationCode}");
                return '';
            }

            return collect($items)->pluck('text')->implode(' ');
        } catch (\Throwable $e) {
            $campaign->log("Exception while fetching subtitles from DataForSEO. video_id {$videoId} language_name {$languageName} location_code {$locationCode} Error: " . formatLogMessage($e->getMessage()));
            return null;
        }
    }

}
