<?php

namespace App\Utility;
use App\Enum\CampaignStatusEnum;

class CampaignStatusMapper
{
    private static array $statusMap = [
        'DRAFT' => CampaignStatusEnum::DRAFT,
        'NEW' => CampaignStatusEnum::NEW,
        'PENDING_REVIEW' => CampaignStatusEnum::PENDING,
        'PENDING_RESTART' => CampaignStatusEnum::PENDING,
        'RUNNING' => CampaignStatusEnum::ACTIVE,
        'PAUSED' => CampaignStatusEnum::PAUSED,
        'PAUSED_ADMIN' => CampaignStatusEnum::PAUSED,
        'PAUSED_SYSTEM' => CampaignStatusEnum::DONE,
        'FINISHED' => CampaignStatusEnum::DONE,
        'TERMINATED' => CampaignStatusEnum::ERROR,
    ];

    public static function getCampaignStatus(string $status): CampaignStatusEnum
    {
        $normalizedStatus = strtoupper($status);
        if (array_key_exists($normalizedStatus, self::$statusMap)) {
            return self::$statusMap[$normalizedStatus];
        }

        throw new \InvalidArgumentException("Unknown status: {$normalizedStatus}");
    }
}
