<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Enum\ImageSources;
use App\Models\Chapter;
use App\Service\FinalizeImageService;
use App\Service\ImageGenerateFactory;
use http\Exception\InvalidArgumentException;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class OptimizeContentImagesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds


    protected $chapter;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3; // Maximum 3 attempts

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int|array
     */
    public $backoff = [30, 60, 90];

    public function __construct(Chapter $chapter)
    {
        $this->chapter = $chapter;


    }
    function fixBrokenHtmlEntities(string $text): string
    {
        // First, fix cases like &amp: &lt: &gt: etc.
        $text = preg_replace('/&([a-z]+):/i', '&$1;', $text);

        // Now decode all HTML entities properly
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $text;
    }

    /**
     * Execute the job.
     */

    public function handle(FinalizeImageService $optimizedImage): void
    {
        try {
            $intro = $this->chapter->intro;

            if (!$intro) {
                return;
            }

            // Match all img tags with src ending in optimized=false
            $pattern = '/<img[^>]+src=["\']([^"\']+optimized=false)["\'][^>]*>/i';

            // Find all matches
            preg_match_all($pattern, $intro, $matches);

            if (!empty($matches[1])) {
                foreach ($matches[1] as $oldSrc) {
                    // Optimize the image
                    // $newSrc = $optimizedImage->optimize_image($oldSrc);
                    $newSrc = $optimizedImage->handleImage($this->fixBrokenHtmlEntities($oldSrc), imageFileName($this->chapter->title));
                    if ($newSrc) {
                        // Replace the old src with new optimized src
                        $intro = str_replace($oldSrc, $newSrc, $intro);
                    }
                }

                // Update the chapter's intro and save
                $this->chapter->intro = $intro;
                $this->chapter->save();
            }

        } catch (Throwable $e) {
            Log::error('OptimizeContentImagesJob failed: ' . $e->getMessage(), [
                'chapter_id' => $this->chapter->id ?? null,
                'exception' => $e,
            ]);

            throw $e; // rethrow to allow Laravel's retry mechanism
        }
    }

    public function failed(?Throwable $exception)
    {
    }
}
