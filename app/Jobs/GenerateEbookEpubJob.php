<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Service\GenerateEbookAsEPUB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateEbookEpubJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $ebookId;

    public function __construct(int $ebookId)
    {
        $this->ebookId = $ebookId;
    }

    public function handle(GenerateEbookAsEPUB $generateEbookAsEPUB): void
    {
        $campaign = Campaign::find($this->ebookId);
        $campaign->update(['status' => CampaignStatusEnum::GENERATE_EPUB]);

        $generateEbookAsEPUB->generateEbook($this->ebookId);

        $campaign->update(['status' => CampaignStatusEnum::DONE]);
    }
}
