<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Service\GenerateEbookAsText;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateEbookTextJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (30 minutes).
     *
     * @var int
     */
    public $timeout = 1800;

    protected int $ebookId;

    public function __construct(int $ebookId)
    {
        $this->ebookId = $ebookId;
    }

    public function handle(GenerateEbookAsText $generateEbookAsText): void
    {
        $campaign = Campaign::find($this->ebookId);
        $campaign->log("Starting text generation job");
        
        $generateEbookAsText->generateEbook($this->ebookId);
        
        $campaign->log("Text generation completed successfully");
    }

    public function failed(?\Throwable $exception)
    {
        $campaign = Campaign::find($this->ebookId);
        $campaign->log("Failed to generate text: " . formatLogMessage($exception->getMessage()));
    }
}
