<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Service\GenerateEbookConclusionService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class GenerateEbookConclusionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds

    public $tries = 3;

    protected Campaign $campaign;

    /**
     * Create a new job instance.
     */
    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(GenerateEbookConclusionService $conclusionService): void
    {
        $this->campaign->log("Generating ebook conclusion chapter");

        if ($this->campaign->status == CampaignStatusEnum::FAILED) {
            $this->campaign->log("Campaign is failed. Skipping conclusion generation.");
            return;
        }

        // Check if conclusion chapter already exists
        $conclusionExists = $this->campaign->chapters()
            ->get()
            ->contains(function ($chapter) {
                return $chapter->getMeta('is_conclusion', false) === true;
            });

        if ($conclusionExists) {
            $this->campaign->log("Conclusion chapter already exists. Skipping conclusion generation.");
            return;
        }

        // Check if all required data is available
        if (!$this->hasRequiredData()) {
            $this->campaign->log("Required data not available for conclusion generation. Skipping.");
            return;
        }

        $this->campaign->log("Generating conclusion chapter");
        $conclusionService->execute($this->campaign);
    }

    /**
     * Check if all required data is available for conclusion generation
     *
     * @return bool
     */
    private function hasRequiredData(): bool
    {
        // Check if campaign has title
        if (empty($this->campaign->title)) {
            $this->campaign->log("Campaign title is missing for conclusion generation.");
            return false;
        }

        // Check if campaign has context
        if (empty($this->campaign->getForm("context"))) {
            $this->campaign->log("Campaign context is missing for conclusion generation.");
            return false;
        }

        // Check if campaign has chapters
        if ($this->campaign->chapters()->count() === 0) {
            $this->campaign->log("No chapters found for conclusion generation.");
            return false;
        }

        // Check if campaign has sections
        if ($this->campaign->sections()->count() === 0) {
            $this->campaign->log("No sections found for conclusion generation.");
            return false;
        }

        return true;
    }

    /**
     * Handle job failure
     */
    public function failed(?Throwable $exception): void
    {
        $this->campaign->log("Failed generating Ebook conclusion chapter. " . formatLogMessage($exception->getMessage()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $this->campaign->addErrorToMeta('conclusion_generation_failed', "Failed generating Ebook conclusion chapter. " . formatLogMessage($exception->getMessage()));

        throw new \Exception("Job conclusion generation failed! " . $exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
