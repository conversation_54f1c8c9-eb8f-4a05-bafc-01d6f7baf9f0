<?php

namespace App\Jobs;

use App\Mail\UpdateNotificationMail;
use App\Models\AppUpdate;
use App\Models\User;
use App\Models\UserUpdateNotification;
use App\Models\UserUpdatePreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendUpdateNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public AppUpdate $update;
    public int $batchSize;

    /**
     * Create a new job instance.
     */
    public function __construct(AppUpdate $update, int $batchSize = 100)
    {
        $this->update = $update;
        $this->batchSize = $batchSize;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!$this->update->shouldSendEmailNotification()) {
            Log::info("Update {$this->update->id} should not send email notifications");
            return;
        }

        Log::info("Starting email notifications for update {$this->update->id}");

        $sentCount = 0;
        $errorCount = 0;

        // Get all active users in batches
        User::query()
            ->chunk($this->batchSize, function ($users) use (&$sentCount, &$errorCount) {
                foreach ($users as $user) {
                    try {
                        $this->sendNotificationToUser($user);
                        $sentCount++;
                    } catch (\Exception $e) {
                        $errorCount++;
                        Log::error("Failed to send update notification to user {$user->id}: " . $e->getMessage());
                    }
                }
            });

        // Mark the update as email sent
        $this->update->markEmailAsSent();

        Log::info("Completed email notifications for update {$this->update->id}. Sent: {$sentCount}, Errors: {$errorCount}");
    }

    /**
     * Send notification to a specific user
     */
    private function sendNotificationToUser(User $user): void
    {
        // Get or create user preferences
        $preferences = UserUpdatePreference::getOrCreateForUser($user);

        // Check if user wants this type of notification
        if (!$preferences->shouldReceiveEmailFor($this->update)) {
            return;
        }

        // Create or get notification record
        $notification = UserUpdateNotification::createForUserAndUpdate($user, $this->update);

        // Skip if email already sent
        if ($notification->email_sent) {
            return;
        }

        // Send the email
        Mail::to($user)->send(new UpdateNotificationMail($this->update, $user, $preferences));

        // Mark as sent
        $notification->markEmailAsSent();
        $preferences->updateLastEmailSent();
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("SendUpdateNotificationJob failed for update {$this->update->id}: " . $exception->getMessage());
    }
}
