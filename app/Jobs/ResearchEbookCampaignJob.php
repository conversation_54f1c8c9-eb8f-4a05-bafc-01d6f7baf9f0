<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\ResearchEbookCampaign;
use App\Service\EbookBookTitleIdeaGenerationService;
use App\Service\ResearchEbookCampaignServiceFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;
use Symfony\Component\HttpFoundation\Response;

class ResearchEbookCampaignJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $campaignId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $campaignId)
    {
        $this->campaignId = $campaignId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $campaign = ResearchEbookCampaign::find($this->campaignId);

        if (!$campaign) {
            throw new \Exception("Campaign not found");
        }

        $service = ResearchEbookCampaignServiceFactory::create($campaign->type);
        $researchCampaign = $service->generate($campaign);

        if ($researchCampaign){
            $campaign->update([
                'status' => CampaignStatusEnum::DONE,
            ]);
        }else{
            $campaign->update([
                'status' => CampaignStatusEnum::FAILED,
            ]);
        }


    }

    public function failed(?Throwable $exception)
    {
        $campaign = ResearchEbookCampaign::find($this->campaignId);
        $campaign->log("Failed Research Ebook Campaign. " . formatLogMessage($exception->getMessage()));
        $campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $campaign->addErrorToMeta('research_ebook_failed', "Failed Research Ebook Campaign. " . formatLogMessage($exception->getMessage()));

        throw new \Exception("Job research campaign failed! " . $exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
