<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Service\GenerateEbookAsPDF;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response;

class GenerateEbookPdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds


    protected int $ebookId;

    public function __construct(int $ebookId)
    {
        $this->ebookId = $ebookId;
    }

    public function handle(GenerateEbookAsPDF $generateEbookAsPDF): void
    {
        $campaign = Campaign::find($this->ebookId);
        $campaign->update([
            'status' => CampaignStatusEnum::GENERATE_PDF,
        ]);
        $generateEbookAsPDF->generateEbook($this->ebookId);

        $campaign->update([
            'status' => CampaignStatusEnum::DONE,
        ]);
    }

    public function failed(?\Throwable $exception)
    {
        $campaign = Campaign::find($this->ebookId);
        $campaign->log("Failed to generate PDF: " . formatLogMessage($exception->getMessage()));
        $campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $campaign->addErrorToMeta('pdf_generate_failed', "Failed to generate PDF: " . formatLogMessage($exception->getMessage()));
    }

}
