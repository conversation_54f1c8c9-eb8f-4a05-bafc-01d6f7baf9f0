<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Service\CampaignContextGeneratorFactory;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class GenerateCampaignContextJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    protected Campaign $campaign;

    /**
     * Create a new job instance.
     */
    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->campaign->log("Generating context inside GenerateCampaignContextJob");

        if ($this->campaign->getForm("context")){
            $this->campaign->log("Context already exists. Skipping context generation.");
            return;
        }

        try {
            // Get the appropriate context generation service based on the campaign type.
            $generator = CampaignContextGeneratorFactory::make($this->campaign);

            // Generate the context.
            $context = $generator->generateContext();

            // Check word count
            $wordCount = str_word_count(strip_tags($context));

            if ($wordCount < 50) {
                $this->campaign->update(['status' => CampaignStatusEnum::FAILED->value]);
                $message = "Generated context is too short, ({$wordCount} words). Which is less than 50 word";
                $this->campaign->log($message);
                throw new \Exception($message);
            }

            // Update the campaign
            $form = $this->campaign->form;
            $form['context'] = $context;
            $this->campaign->update(['form' => $form]);
            $this->campaign->log("Context generated and saved successfully.");
        } catch (\Exception $e) {
            $this->campaign->log("Failed to generate context: " . $e->getMessage());

            $this->campaign->addErrorToMeta('context_error', "Failed to generate context: " . $e->getMessage());
        }
    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("Failed to generate context: " . formatLogMessage($exception->getMessage()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $this->campaign->addErrorToMeta('context_error', "Failed to generate context: " . formatLogMessage($exception->getMessage()));
    }
}
