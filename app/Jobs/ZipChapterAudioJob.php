<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use ZipArchive;
use Throwable;

class ZipChapterAudioJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (30 minutes).
     *
     * @var int
     */
    public $timeout = 1800; // 30 minutes

    protected Campaign $campaign;
    protected ?string $voiceModel;

    public function __construct(Campaign $campaign, ?string $voiceModel = null)
    {
        $this->campaign = $campaign;
        $this->voiceModel = $voiceModel;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->campaign->log("Starting chapter audio zip creation");

            // Get all chapters and filter for audio availability
            $allChapters = $this->campaign->chapters()->get();

            if ($this->voiceModel) {
                $chaptersWithAudio = $allChapters->filter(function ($chapter) {
                    return $chapter->hasAudio($this->voiceModel);
                });
                $this->campaign->log("Creating ZIP for voice model: {$this->voiceModel}. Found {$chaptersWithAudio->count()} chapters with audio.");
            } else {
                // If no model specified, get chapters with any audio (backward compatibility)
                $chaptersWithAudio = $allChapters->filter(function ($chapter) {
                    return $chapter->hasAudio();
                });
                $this->campaign->log("Creating ZIP for default/first available audio model. Found {$chaptersWithAudio->count()} chapters with audio.");
            }

            if ($chaptersWithAudio->isEmpty()) {
                $this->campaign->log("No chapters with audio found");
                return;
            }

            // Create temporary directory for downloads
            $tempDir = storage_path('app/temp/chapter_audio_' . $this->campaign->id . '_' . time());
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Download all chapter audio files
            $downloadedFiles = [];
            foreach ($chaptersWithAudio as $chapter) {
                $s3Path = $chapter->getAudioS3Path($this->voiceModel);

                if ($s3Path && Storage::disk('s3')->exists($s3Path)) {
                    $fileName = sprintf(
                        'Chapter_%02d_%s.mp3',
                        $chapter->chapter_number,
                        $this->sanitizeFileName($chapter->plain_title)
                    );
                    
                    $localPath = $tempDir . '/' . $fileName;
                    $audioContent = Storage::disk('s3')->get($s3Path);
                    file_put_contents($localPath, $audioContent);
                    
                    $downloadedFiles[] = $localPath;
                    $this->campaign->log("Downloaded chapter {$chapter->chapter_number}: {$fileName}");
                }
            }

            if (empty($downloadedFiles)) {
                $this->campaign->log("No audio files could be downloaded");
                $this->cleanup($tempDir);
                return;
            }

            // Create ZIP file with model name
            $modelSuffix = $this->voiceModel ? "_{$this->voiceModel}" : '';
            $zipFileName = sprintf(
                'chapter_audio_%s%s_%s.zip',
                $this->sanitizeFileName($this->campaign->topic),
                $modelSuffix,
                date('Y-m-d_H-i-s')
            );
            $zipPath = $tempDir . '/' . $zipFileName;

            $zip = new ZipArchive();
            if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
                throw new \Exception("Cannot create zip file: {$zipPath}");
            }

            foreach ($downloadedFiles as $file) {
                $zip->addFile($file, basename($file));
            }
            $zip->close();

            $this->campaign->log("Created zip file: {$zipFileName}");

            // Upload ZIP to S3
            $s3ZipPath = "chapter_audio_zips/campaign_{$this->campaign->id}/{$zipFileName}";
            $zipContent = file_get_contents($zipPath);
            Storage::disk('s3')->put($s3ZipPath, $zipContent, 'private');

            // Generate temporary download URL (valid for 7 days)
            $downloadUrl = config('app.url') . '/download-chapter-audio/' . $this->campaign->id;

            $this->campaign->log("Uploaded zip to S3: {$s3ZipPath}");

            // Generate model-specific download URL
            $baseUrl = config('app.url');
            $modelDownloadUrl = $this->voiceModel
                ? "{$baseUrl}/download-chapter-audio/{$this->campaign->id}/{$this->voiceModel}"
                : "{$baseUrl}/download-chapter-audio/{$this->campaign->id}";

            // Save download info to campaign meta (with model-specific key)
            $metaKey = $this->voiceModel ? "chapter_audio_zip_{$this->voiceModel}" : 'chapter_audio_zip';
            $this->campaign->saveMeta($metaKey, [
                'file_name' => $zipFileName,
                's3_path' => $s3ZipPath,
                'download_url' => $modelDownloadUrl,
                'voice_model' => $this->voiceModel,
                'created_at' => now()->toISOString(),
                'expires_at' => now()->addDays(7)->toISOString(),
                'chapter_count' => count($downloadedFiles)
            ]);

            // Also update the main download URLs list
            $this->updateDownloadUrlsList();

            // Send email to user with model-specific URL
            $this->sendDownloadEmail($modelDownloadUrl, $zipFileName, count($downloadedFiles));

            // Cleanup temporary files
            $this->cleanup($tempDir);

            $this->campaign->log("Chapter audio zip process completed successfully");

        } catch (Throwable $e) {
            $this->campaign->log("Failed to create chapter audio zip: " . formatLogMessage($e->getMessage()));
            $this->campaign->log(formatLogMessage($e->getFile()) . " " . formatLogMessage($e->getLine()));
            
            // Cleanup on error
            if (isset($tempDir)) {
                $this->cleanup($tempDir);
            }
        }
    }

    private function sanitizeFileName(string $name): string
    {
        // Remove HTML tags and decode entities
        $name = strip_tags(html_entity_decode($name));
        
        // Replace problematic characters
        $name = preg_replace('/[^a-zA-Z0-9\-_\s]/', '', $name);
        
        // Replace spaces with underscores and limit length
        $name = str_replace(' ', '_', trim($name));
        
        return substr($name, 0, 50);
    }

    private function sendDownloadEmail(string $downloadUrl, string $fileName, int $chapterCount): void
    {
        try {
            $user = $this->campaign->user;
            
            Mail::send('emails.chapter-audio-ready', [
                'user' => $user,
                'campaign' => $this->campaign,
                'downloadUrl' => $downloadUrl,
                'fileName' => $fileName,
                'chapterCount' => $chapterCount,
                'expiresAt' => now()->addDays(7)->format('M j, Y \a\t g:i A')
            ], function ($message) use ($user, $fileName) {
                $message->to($user->email, $user->name)
                        ->subject("Your Chapter Audio Files are Ready - {$this->campaign->topic}")
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            $this->campaign->log("Download email sent to: {$user->email}");

        } catch (Throwable $e) {
            $this->campaign->log("Failed to send download email: " . formatLogMessage($e->getMessage()));
        }
    }

    private function cleanup(string $tempDir): void
    {
        try {
            if (file_exists($tempDir)) {
                $files = glob($tempDir . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
                rmdir($tempDir);
                $this->campaign->log("Cleaned up temporary directory: {$tempDir}");
            }
        } catch (Throwable $e) {
            $this->campaign->log("Failed to cleanup temporary directory: " . formatLogMessage($e->getMessage()));
        }
    }

    /**
     * Update the main download URLs list for the campaign
     */
    private function updateDownloadUrlsList(): void
    {
        try {
            // Get existing download URLs
            $downloadUrls = $this->campaign->getMeta('chapter_audio_download_urls', []);

            // Generate URL manually to avoid route() issues in job context
            $baseUrl = config('app.url');
            $url = $this->voiceModel
                ? "{$baseUrl}/download-chapter-audio/{$this->campaign->id}/{$this->voiceModel}"
                : "{$baseUrl}/download-chapter-audio/{$this->campaign->id}";

            // Add/update this model's download URL
            $downloadUrls[$this->voiceModel ?: 'default'] = [
                'model' => $this->voiceModel,
                'url' => $url,
                'created_at' => now()->toISOString(),
                'expires_at' => now()->addDays(7)->toISOString()
            ];

            // Save updated list
            $this->campaign->saveMeta('chapter_audio_download_urls', $downloadUrls);

            $this->campaign->log("Updated download URLs list. Available models: " . implode(', ', array_keys($downloadUrls)));
        } catch (\Exception $e) {
            $this->campaign->log("Failed to update download URLs list: " . $e->getMessage());
        }
    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("Failed to create chapter audio zip: " . formatLogMessage($exception->getMessage()));
        $this->campaign->log(formatLogMessage($exception->getFile()) . " " . formatLogMessage($exception->getLine()));
    }
}
