<?php

namespace App\Jobs;

use App\Actions\HighestRequiredCreditCalculator;
use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Models\Campaign;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;

class GenerateEbookJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds


    protected int $ebookId;

    public function __construct(int $ebookId)
    {
        $this->ebookId = $ebookId;
    }

    public function handle(): void
    {
        $campaign = Campaign::find($this->ebookId);

        if ($campaign->status == CampaignStatusEnum::FAILED) {
            return;
        }

        if (
            CreditDeductEnum::EBOOK_GENERATION_CREDIT->value > $campaign->user->remainCredit()
        ){
            $campaign->log("Ebook generation is failed. Not enough credit to generate ebook.");
            $campaign->addErrorToMeta('insufficient_credit', "Ebook generation failed. Not enough credit.");
            return;
        }

        Bus::chain([
            new GenerateEbookPdfJob($this->ebookId),
            new GenerateEbookEpubJob($this->ebookId),
        ])->dispatch();
    }
}
