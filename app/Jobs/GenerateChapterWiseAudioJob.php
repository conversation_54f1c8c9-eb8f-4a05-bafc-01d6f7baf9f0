<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Services\AudioIntroOutroService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Throwable;
use Filament\Notifications\Notification;

class GenerateChapterWiseAudioJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds

    protected Campaign $campaign;
    protected string $voiceModel;

    public function __construct(Campaign $campaign, string $voiceModel)
    {
        $this->campaign = $campaign;
        $this->voiceModel = $voiceModel;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $requiredCredits = CreditDeductEnum::AUDIO_GENERATION_CREDIT->value;
            if ($requiredCredits > $this->campaign->user->remainCredit()) {
                Notification::make()
                    ->title("You do not have enough credits. (Required: {$requiredCredits})")
                    ->danger()
                    ->send();
                $this->campaign->log("Insufficient credits: Required {$requiredCredits}, Available: " . $this->campaign->user->remainCredit());
                return;
            }

            $this->campaign->update(['status' => CampaignStatusEnum::AUDIO_PROCESSING]);
            $this->campaign->log("Starting chapter-wise audio generation with voice: {$this->voiceModel}");

            $chapters = $this->campaign->chapters;
            $totalChapters = $chapters->count();

            // Check if this is a retry - get chapters that don't have audio for this specific model
            $chaptersToProcess = $chapters->filter(function ($chapter) {
                return !$chapter->hasAudio($this->voiceModel);
            });

            $isRetry = $chaptersToProcess->count() < $totalChapters;
            $completedChapters = $totalChapters - $chaptersToProcess->count();

            if ($isRetry) {
                $this->campaign->log("Retrying chapter-wise audio generation. {$completedChapters} chapters already completed, {$chaptersToProcess->count()} remaining.");
            } else {
                $this->campaign->log("Processing {$totalChapters} chapters for audio generation");
            }

            // Initialize credit tracking and progress (accounting for already completed chapters)
            $this->initializeCreditTracking($totalChapters, $completedChapters);
            $this->campaign->saveMeta('chapter_audio_progress', [
                'total_chapters' => $totalChapters,
                'completed_chapters' => $completedChapters,
                'current_chapter' => $completedChapters + 1,
                'percentage' => round(($completedChapters / $totalChapters) * 100)
            ]);

            foreach ($chaptersToProcess as $chapter) {
                $chapterNumber = $chapter->chapter_number;
                $this->processChapter($chapter, $chapterNumber, $totalChapters);

                // Update progress after each chapter
                $completedChapters++;
                $this->updateProgress($completedChapters, $totalChapters);
            }

            $this->campaign->update(['status' => CampaignStatusEnum::DONE]);
            $this->campaign->saveMeta('chapter_audio_progress', null); // Clear progress tracking
            $this->campaign->log("Chapter-wise audio generation completed successfully");

            // Check if there are any remaining credits to adjust
            $this->adjustRemainingCredits();

            // Dispatch zip creation job
            \App\Jobs\ZipChapterAudioJob::dispatch($this->campaign, $this->voiceModel)->onQueue($this->campaign->getChapterAudioQueueName());
            $this->campaign->log("Dispatched zip creation job for chapter audio files");

            Notification::make()
                ->title("Chapter-wise audio generation completed! Creating download package...")
                ->success()
                ->send();

        } catch (Throwable $e) {
            $this->campaign->update(['status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED]);
            $this->campaign->log("Failed to generate chapter-wise audio. " . formatLogMessage($e->getMessage()));
            $this->campaign->log(formatLogMessage($e->getFile()) . " " . formatLogMessage($e->getLine()));
        }
    }

    private function processChapter(Chapter $chapter, int $chapterNumber, int $totalChapters): void
    {
        try {
           
            $this->campaign->log("Processing chapter {$chapterNumber}/{$totalChapters}: {$chapter->title}");

            // Extract text from chapter
            $text = $this->extractTextFromChapter($chapter);
            
            if (empty(trim($text))) {
                $this->campaign->log("Skipping chapter {$chapterNumber} - no content found");
                return;
            }

            // Split into chunks if needed (OpenAI TTS limit is 4096 characters)
            $chunks = $this->splitTextIntoChunks($text);
            $this->campaign->log("Chapter {$chapterNumber} split into " . count($chunks) . " chunks");

            // Generate audio for each chunk
            $audioFiles = [];
            foreach ($chunks as $chunkIndex => $chunk) {
                $audioFile = $this->generateAudioForChunk($chunk, $chapter, $chunkIndex);
                if ($audioFile) {
                    $audioFiles[] = $audioFile;
                }
            }

            if (!empty($audioFiles)) {
                // Merge audio files if multiple chunks
                if (count($audioFiles) > 1) {
                    $mergedFile = $this->mergeAudioFiles($audioFiles, $chapter);
                    $finalAudioFile = $mergedFile;
                    
                    // Clean up individual chunk files
                    foreach ($audioFiles as $file) {
                        if (Storage::exists($file)) {
                            Storage::delete($file);
                        }
                    }
                } else {
                    $finalAudioFile = $audioFiles[0];
                }

                // Upload to S3 and save path to chapter meta by model
                $s3Path = $this->uploadToS3($finalAudioFile, $chapter);

                // Save audio info for this specific model
                $audioModels = $chapter->getMeta('audio_models', []);
                $audioModels[$this->voiceModel] = [
                    's3_path' => $s3Path,
                    'generated_at' => now()->toISOString(),
                    'file_size' => null, // Will be calculated if needed
                    'has_intro' => $this->campaign->getMeta('include_chapter_intro', false),
                    'intro_text' => $this->campaign->getMeta('include_chapter_intro', false)
                        ? AudioIntroOutroService::generateChapterIntro($chapter, $this->campaign)
                        : null,
                ];
                $chapter->saveMeta('audio_models', $audioModels);

                // Keep backward compatibility
                if (!$chapter->getMeta('audio_s3_path')) {
                    $chapter->saveMeta('audio_s3_path', $s3Path);
                    $chapter->saveMeta('audio_voice', $this->voiceModel);
                    $chapter->saveMeta('audio_generated_at', now()->toISOString());
                }

                // Clean up local file
                if (Storage::exists($finalAudioFile)) {
                    Storage::delete($finalAudioFile);
                }

                $this->campaign->log("Chapter {$chapterNumber} audio generated and uploaded to S3: {$s3Path}");

                // Deduct credits for this chapter
                $this->deductChapterCredit($chapterNumber);
            }

        } catch (Throwable $e) {
            $this->campaign->log("Failed to process chapter {$chapterNumber}: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    private function extractTextFromChapter(Chapter $chapter): string
    {
        $text = "";

        // Add chapter intro if enabled
        if ($this->campaign->getMeta('include_chapter_intro', false)) {
            $chapterIntro = AudioIntroOutroService::generateChapterIntro($chapter, $this->campaign);
            $text .= $this->sanitizeText($chapterIntro) . "\n\n";
        }

        $text .= $this->sanitizeText($chapter->title) . "\n";
        $text .= $this->sanitizeText($chapter->intro) . "\n";

        foreach ($chapter->sections as $section) {
            $text .= $this->sanitizeText($section->title) . "\n";
            $text .= $this->sanitizeText($section->intro) . "\n";
            $text .= $this->sanitizeText($section->body) . "\n";
        }

        return strip_tags($text);
    }

    private function sanitizeText(?string $text): string
    {
        if (!$text) {
            return '';
        }

        // Remove any null bytes and control characters
        $text = str_replace("\0", '', $text);

        // Remove or replace problematic characters
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Convert to UTF-8 and remove invalid sequences
        $text = mb_convert_encoding($text, 'UTF-8', 'UTF-8');

        // Remove any remaining invalid UTF-8 sequences using regex
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F\xFF]/', '', $text);

        // Ensure it's valid UTF-8
        if (!mb_check_encoding($text, 'UTF-8')) {
            // If still invalid, convert from ISO-8859-1 to UTF-8
            $text = mb_convert_encoding($text, 'UTF-8', 'ISO-8859-1');
        }

        return trim($text);
    }

    private function splitTextIntoChunks(string $text): array
    {
        $maxInputSize = 4096; // OpenAI TTS limit
        
        if (strlen($text) <= $maxInputSize) {
            return [$text];
        }

        $chunks = [];
        $words = explode(' ', $text);
        $currentChunk = '';

        foreach ($words as $word) {
            $testChunk = $currentChunk . ($currentChunk ? ' ' : '') . $word;
            
            if (strlen($testChunk) > $maxInputSize) {
                if (!empty($currentChunk)) {
                    $chunks[] = trim($currentChunk);
                    $currentChunk = $word;
                } else {
                    // Single word is too long, split it
                    $chunks[] = substr($word, 0, $maxInputSize);
                    $currentChunk = substr($word, $maxInputSize);
                }
            } else {
                $currentChunk = $testChunk;
            }
        }

        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        return array_filter($chunks, fn($chunk) => !empty(trim($chunk)));
    }

    private function generateAudioForChunk(string $text, Chapter $chapter, int $chunkIndex): ?string
    {
        try {
            $apiKey = $this->campaign->user->getOpenAiApiKey();
            if (!$apiKey) {
                throw new \Exception("OpenAI API key not found");
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(300)->post('https://api.openai.com/v1/audio/speech', [
                'model' => 'tts-1',
                'input' => $text,
                'voice' => $this->voiceModel,
                'response_format' => 'mp3',
            ]);

            if (!$response->successful()) {
                throw new \Exception("OpenAI API error: " . $response->body());
            }

            $fileName = "chapter_{$chapter->id}_chunk_{$chunkIndex}_" . time() . ".mp3";
            $filePath = "temp/audio/{$fileName}";
            
            Storage::put($filePath, $response->body());
            
            return $filePath;

        } catch (Throwable $e) {
            $this->campaign->log("Failed to generate audio for chunk {$chunkIndex}: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    private function mergeAudioFiles(array $audioFiles, Chapter $chapter): string
    {
        try {
            // Create a temporary merged file name
            $mergedFileName = "chapter_{$chapter->id}_merged_" . time() . ".mp3";
            $mergedFilePath = "temp/audio/{$mergedFileName}";

            // For now, we'll concatenate the files by reading and writing them
            // In a production environment, you might want to use FFmpeg for proper audio merging
            $mergedContent = '';
            foreach ($audioFiles as $audioFile) {
                if (Storage::exists($audioFile)) {
                    $mergedContent .= Storage::get($audioFile);
                }
            }

            Storage::put($mergedFilePath, $mergedContent);
            return $mergedFilePath;

        } catch (Throwable $e) {
            $this->campaign->log("Failed to merge audio files, using first file: " . formatLogMessage($e->getMessage()));
            // Fallback to first file if merging fails
            return $audioFiles[0];
        }
    }

    private function uploadToS3(string $localFilePath, Chapter $chapter): string
    {
        try {
            $fileName = "chapter_audio/campaign_{$this->campaign->id}/{$this->voiceModel}/chapter_{$chapter->id}_" . time() . ".mp3";
            $s3Path = "audio/{$fileName}";

            $fileContent = Storage::get($localFilePath);
            Storage::disk('s3')->put($s3Path, $fileContent, 'public');

            return $s3Path;

        } catch (Throwable $e) {
            $this->campaign->log("Failed to upload to S3: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    /**
     * Initialize credit tracking for progressive deduction
     *
     * Example: 100 credits for 11 chapters
     * - Base: 100 / 11 = 9 credits per chapter
     * - Remaining: 100 % 11 = 1 credit
     * - Distribution: 10 chapters get 9 credits, 1 chapter gets 10 credits
     * - Total: (10 × 9) + (1 × 10) = 100 credits
     */
    private function initializeCreditTracking(int $totalChapters, int $completedChapters = 0): void
    {
        $totalCredits = CreditDeductEnum::AUDIO_GENERATION_CREDIT->value; // 100 credits
        $baseCreditsPerChapter = intval($totalCredits / $totalChapters);
        $remainingCredits = $totalCredits % $totalChapters;

        // Calculate credits already deducted for completed chapters
        $creditsAlreadyDeducted = 0;
        for ($i = 1; $i <= $completedChapters; $i++) {
            $creditsForChapter = $baseCreditsPerChapter;
            if ($i > ($totalChapters - $remainingCredits)) {
                $creditsForChapter += 1; // Add 1 extra credit to last chapters
            }
            $creditsAlreadyDeducted += $creditsForChapter;
        }

        // Save credit tracking to campaign meta
        $this->campaign->saveMeta('chapter_audio_credits', [
            'total_credits' => $totalCredits,
            'total_chapters' => $totalChapters,
            'base_credits_per_chapter' => $baseCreditsPerChapter,
            'remaining_credits' => $remainingCredits,
            'credits_deducted' => $creditsAlreadyDeducted,
            'chapters_processed' => $completedChapters
        ]);

        if ($completedChapters > 0) {
            $this->campaign->log("Credit tracking initialized for retry: {$baseCreditsPerChapter} credits per chapter, {$remainingCredits} remaining to distribute. {$creditsAlreadyDeducted} credits already accounted for {$completedChapters} completed chapters.");
        } else {
            $this->campaign->log("Credit tracking initialized: {$baseCreditsPerChapter} credits per chapter, {$remainingCredits} remaining to distribute");
        }
    }

    /**
     * Deduct credits for a completed chapter
     */
    private function deductChapterCredit(int $chapterNumber): void
    {
        $creditInfo = $this->campaign->getMeta('chapter_audio_credits', []);

        if (empty($creditInfo)) {
            $this->campaign->log("No credit tracking found for chapter {$chapterNumber}");
            return;
        }

        $baseCredits = $creditInfo['base_credits_per_chapter'];
        $remainingCredits = $creditInfo['remaining_credits'];
        $chaptersProcessed = $creditInfo['chapters_processed'];
        $totalChapters = $creditInfo['total_chapters'];

        // Calculate credits for this chapter
        // Distribute remaining credits to the last chapters
        $creditsForThisChapter = $baseCredits;
        if ($chapterNumber > ($totalChapters - $remainingCredits)) {
            $creditsForThisChapter += 1; // Add 1 extra credit to last chapters
        }

        // Deduct credits from user
        if ($creditsForThisChapter > 0) {
            $this->campaign->user->logCreditActivity(
                user: $this->campaign->user,
                action: \App\Enum\CreditLogActionEnum::CAMPAIGN,
                credit: $creditsForThisChapter,
                description: "{$creditsForThisChapter} credits deducted for chapter {$chapterNumber} audio generation (campaign: {$this->campaign->id})",
            );

            $this->campaign->user->useCredit($creditsForThisChapter);

            // Update tracking
            $creditInfo['credits_deducted'] += $creditsForThisChapter;
            $creditInfo['chapters_processed'] = $chaptersProcessed + 1;
            $this->campaign->saveMeta('chapter_audio_credits', $creditInfo);

            $this->campaign->log("Deducted {$creditsForThisChapter} credits for chapter {$chapterNumber}. Total deducted: {$creditInfo['credits_deducted']}/{$creditInfo['total_credits']}");
        }
    }

    /**
     * Update progress tracking for the campaign
     */
    private function updateProgress(int $completedChapters, int $totalChapters): void
    {
        $percentage = round(($completedChapters / $totalChapters) * 100);

        $this->campaign->saveMeta('chapter_audio_progress', [
            'total_chapters' => $totalChapters,
            'completed_chapters' => $completedChapters,
            'current_chapter' => $completedChapters + 1,
            'percentage' => $percentage
        ]);

        $this->campaign->log("Chapter audio progress: {$completedChapters}/{$totalChapters} chapters completed ({$percentage}%)");
    }

    /**
     * Adjust any remaining credits at the end
     */
    private function adjustRemainingCredits(): void
    {
        $creditInfo = $this->campaign->getMeta('chapter_audio_credits', []);

        if (empty($creditInfo)) {
            return;
        }

        $totalCredits = $creditInfo['total_credits'];
        $creditsDeducted = $creditInfo['credits_deducted'];
        $remainingToDeduct = $totalCredits - $creditsDeducted;

        if ($remainingToDeduct > 0) {
            $this->campaign->user->logCreditActivity(
                user: $this->campaign->user,
                action: \App\Enum\CreditLogActionEnum::CAMPAIGN,
                credit: $remainingToDeduct,
                description: "{$remainingToDeduct} credits adjustment for chapter audio generation completion (campaign: {$this->campaign->id})",
            );

            $this->campaign->user->useCredit($remainingToDeduct);

            $this->campaign->log("Adjusted remaining {$remainingToDeduct} credits. Total deducted: {$totalCredits} credits");
        } elseif ($remainingToDeduct < 0) {
            // This shouldn't happen, but log it if it does
            $this->campaign->log("Warning: Over-deducted credits by " . abs($remainingToDeduct));
        } else {
            $this->campaign->log("Credit deduction completed perfectly: {$totalCredits} credits total");
        }

        // Clean up tracking meta
        $this->campaign->saveMeta('chapter_audio_credits', null);
    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("Failed to generate chapter-wise audio. " . formatLogMessage($exception->getMessage()));
        $this->campaign->log(formatLogMessage($exception->getFile()) . " " . formatLogMessage($exception->getLine()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
        ]);

        // Clean up progress tracking but keep credit tracking for retry
        $this->campaign->saveMeta('chapter_audio_progress', null);

        // Keep credit tracking for potential retry, but log the failure
        $creditInfo = $this->campaign->getMeta('chapter_audio_credits', []);
        if (!empty($creditInfo)) {
            $this->campaign->log("Chapter audio generation failed. Credits deducted so far: {$creditInfo['credits_deducted']}/{$creditInfo['total_credits']}. Credit tracking preserved for retry.");
        }
    }
}
