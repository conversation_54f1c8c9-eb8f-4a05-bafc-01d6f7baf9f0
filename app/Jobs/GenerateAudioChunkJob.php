<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Jobs\MergeAudioChunksJob;
use App\Models\Campaign;
use App\Service\AIModel\OpenAIClient;
use App\Services\AudioIntroOutroService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Throwable;

class GenerateAudioChunkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;


    protected $campaign;
    protected $textChunk;
    protected $chunkIndex;

    public function __construct(Campaign $campaign, $textChunk, $chunkIndex)
    {
        $this->campaign = $campaign;
        // Clean and ensure proper UTF-8 encoding before serialization
        $this->textChunk = $this->cleanUtf8Text($textChunk);
        $this->chunkIndex = $chunkIndex;
    }

    /**
     * Clean and ensure proper UTF-8 encoding
     */
    private function cleanUtf8Text($text)
    {
        // Remove any null bytes and control characters
        $text = str_replace("\0", '', $text);

        // Remove or replace problematic characters
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Convert to UTF-8 and remove invalid sequences
        $text = mb_convert_encoding($text, 'UTF-8', 'UTF-8');

        // Remove any remaining invalid UTF-8 sequences using regex
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F\xFF]/', '', $text);

        // Ensure it's valid UTF-8
        if (!mb_check_encoding($text, 'UTF-8')) {
            // If still invalid, convert from ISO-8859-1 to UTF-8
            $text = mb_convert_encoding($text, 'UTF-8', 'ISO-8859-1');
        }

        return trim($text);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        try {
            $this->campaign->log("Started generating audio chunk " . $this->chunkIndex);
            $this->campaign->log("Text chunk " . $this->textChunk);

            if ($this->campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED) {
                return;
            }
            $openAIClient = new OpenAIClient();
            $this->campaign->log("Generating audio chunk " . $this->chunkIndex . " (attempt {$this->attempts()}/{$this->tries})");

            // Add retry logic specifically for API calls
            $maxApiRetries = 3;
            $apiRetryDelay = 5; // seconds
            $audioData = null;

            for ($apiAttempt = 1; $apiAttempt <= $maxApiRetries; $apiAttempt++) {
                try {
                    // Text is already cleaned and UTF-8 encoded in constructor
                    $audioData = $openAIClient->generateAudio($this->textChunk, $this->campaign->getOpenAiApiKey(), $this->campaign->getMeta("voice_model"));
                    break; // Success, exit retry loop
                } catch (\Exception $apiException) {
                    $this->campaign->log("API attempt {$apiAttempt}/{$maxApiRetries} failed for chunk {$this->chunkIndex}: " . $apiException->getMessage());

                    if ($apiAttempt < $maxApiRetries) {
                        $this->campaign->log("Retrying API call in {$apiRetryDelay} seconds...");
                        sleep($apiRetryDelay);
                        $apiRetryDelay *= 2; // Exponential backoff
                    } else {
                        throw $apiException; // Re-throw after all API retries exhausted
                    }
                }
            }

            if (empty($audioData)) {
                throw new \Exception("Generated audio data is empty for chunk " . $this->chunkIndex);
            }

            $fileName = "chunk-" . $this->campaign->id . "-" . $this->chunkIndex . ".mp3";
            Storage::put('public/audio/' . $fileName, $audioData);

            // Use database transaction to prevent race conditions
            DB::transaction(function () use ($fileName) {
                // Refresh campaign to get latest data
                $this->campaign->refresh();
                $audioChunks = $this->campaign->getMeta('chunks', []);

                // Check if this chunk already exists (prevent duplicates)
                $chunkExists = false;
                foreach ($audioChunks as $existingChunk) {
                    if ($existingChunk['chunk_index'] == $this->chunkIndex) {
                        $chunkExists = true;
                        break;
                    }
                }

                // Only add if chunk doesn't already exist
                if (!$chunkExists) {
                    $audioChunks[] = [
                        "chunk_index" => $this->chunkIndex,
                        'file_name' => $fileName
                    ];

                    // Sort chunks by index to ensure proper order
                    usort($audioChunks, function($a, $b) {
                        return $a['chunk_index'] <=> $b['chunk_index'];
                    });

                    try {
                        $this->campaign->saveMeta('chunks', $audioChunks);
                    } catch (\Exception $e) {
                        $this->campaign->log("Failed to save audio chunk metadata. " . formatLogMessage($e->getMessage()));
                        throw $e;
                    }
                }
            });

            // Refresh again to get the latest chunk count
            $this->campaign->refresh();
            $audioChunks = $this->campaign->getMeta('chunks', []);
            $totalChunks = $this->campaign->getMeta("total_chunk");

            $this->campaign->log("Generated " . count($audioChunks) . " of " . $totalChunks);

            //check if all chunks completed
            if (count($audioChunks) >= $totalChunks) {
                $this->campaign->log("All chunks completed. Dispatching merge job.");

                // Dispatch the merge job to handle audio merging
                MergeAudioChunksJob::dispatch($this->campaign);
            }


        } catch (\Exception $e) {
            $this->campaign->log("Failed to generate audio chunk {$this->chunkIndex}. Attempt {$this->attempts()}/{$this->tries}. Error: " . formatLogMessage($e->getMessage()));

            // Only mark as failed if this is the final attempt
            if ($this->attempts() >= $this->tries) {
                $this->campaign->log("All retry attempts exhausted for chunk {$this->chunkIndex}. Marking campaign as failed.");
                $this->campaign->update([
                    'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
                ]);
            } else {
                $this->campaign->log("Will retry chunk {$this->chunkIndex} in {$this->backoff} seconds.");
                // Re-throw the exception to trigger retry
                throw $e;
            }
        }
    }
    public function failed(?Throwable $exception)
    {
        $this->campaign->log("FINAL FAILURE: Audio chunk {$this->chunkIndex} failed after {$this->tries} attempts. Error: " . formatLogMessage($exception->getMessage()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
        ]);
    }

}
