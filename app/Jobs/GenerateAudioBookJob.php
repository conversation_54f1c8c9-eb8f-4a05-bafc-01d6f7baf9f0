<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Models\Campaign;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

use Throwable;
use Filament\Notifications\Notification;

class GenerateAudioBookJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds

    protected Campaign $campaign;

    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (
                CreditDeductEnum::AUDIO_GENERATION_CREDIT->value > $this->campaign->user->remainCredit()
            ){
                $required = CreditDeductEnum::AUDIO_GENERATION_CREDIT->value;
                Notification::make()
                            ->title("You do not have enough credits. (Required: {$required})")
                            ->danger()
                            ->send();
                return;
            }
            
            $audioChunks = $this->campaign->getMeta('chunks', []);
            if (count($audioChunks) > 0) {
                foreach ($audioChunks as $chunk) {
                    //delete if exist
                    if (Storage::exists('public/audio/' . $chunk['file_name'])) {
                        Storage::delete('public/audio/' . $chunk['file_name']);
                    }
                }
            }
            $this->campaign->update(['status' => CampaignStatusEnum::AUDIO_PROCESSING]);
            $text = $this->extractTextFromCampaign();
            $textChunks = $this->splitTextIntoChunks($text);
            $this->dispatchChunkJobs($textChunks);
        } catch (Throwable $e) {
            $this->campaign->update(['status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED]);
            $this->campaign->log("Failed to generate audio book-. " . formatLogMessage($e->getMessage()));
            $this->campaign->log( formatLogMessage($e->getFile()) . " " . formatLogMessage($e->getLine()));
        }
    }

    private function extractTextFromCampaign(): string
    {
        $text = "";
        foreach ($this->campaign->chapters as $chapter) {
            $text .= $this->sanitizeText($chapter->title) . "\n";
            $text .= $this->sanitizeText($chapter->intro) . "\n";
            foreach ($chapter->sections as $section) {
                $text .= $this->sanitizeText($section->title) . "\n";
                $text .= $this->sanitizeText($section->intro) . "\n";
                $text .= $this->sanitizeText($section->body) . "\n";
            }
        }
        return strip_tags($text);
    }

    private function sanitizeText(?string $text): string
    {
        if (!$text) {
            return '';
        }

        // Remove any null bytes and control characters
        $text = str_replace("\0", '', $text);

        // Remove or replace problematic characters
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Convert to UTF-8 and remove invalid sequences
        $text = mb_convert_encoding($text, 'UTF-8', 'UTF-8');

        // Remove any remaining invalid UTF-8 sequences using regex
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F\xFF]/', '', $text);

        // Ensure it's valid UTF-8
        if (!mb_check_encoding($text, 'UTF-8')) {
            // If still invalid, convert from ISO-8859-1 to UTF-8
            $text = mb_convert_encoding($text, 'UTF-8', 'ISO-8859-1');
        }

        return trim($text);
    }

    private function splitTextIntoChunks(string $text): array
    {
        $maxInputSize = 4096; // OpenAI TTS limit 4096
        $textChunks = str_split($text, $maxInputSize);

        // Validate each chunk for UTF-8 encoding
        $validChunks = [];
        foreach ($textChunks as $index => $chunk) {
            if (!mb_check_encoding($chunk, 'UTF-8')) {
                $this->campaign->log("Invalid UTF-8 encoding detected in chunk {$index}, attempting to fix");
                $chunk = mb_convert_encoding($chunk, 'UTF-8', 'UTF-8');
            }

            // Only add non-empty chunks
            if (!empty(trim($chunk))) {
                $validChunks[] = $chunk;
            }
        }

        $this->campaign->log("Total chunks: " . count($validChunks));
        $this->campaign->saveMeta('total_chunk', count($validChunks));
        return $validChunks;
    }

    private function dispatchChunkJobs(array $textChunks): void
    {
        foreach ($textChunks as $index => $chunk) {
            GenerateAudioChunkJob::dispatch($this->campaign, $chunk, $index)->onQueue($this->campaign->getAudioBookQueueName());
        }
    }
    public function failed(?Throwable $exception)
    {
        $this->campaign->log("Failed to generate audio books. " . formatLogMessage($exception->getMessage()));
        $this->campaign->log( formatLogMessage($exception->getFile()) . " " . formatLogMessage($exception->getLine()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
        ]);
    }
}
