<?php

namespace App\Listeners;

use App\Mail\TicketNewResponseEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use Sgcomptech\FilamentTicketing\Events\NewResponse;

class SendTicketNewResponseEmail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NewResponse $event): void
    {
        Mail::to($event->response->ticket->user->email)->send(new TicketNewResponseEmail($event->response));
    }
}
