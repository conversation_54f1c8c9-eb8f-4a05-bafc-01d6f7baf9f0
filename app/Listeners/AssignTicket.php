<?php

namespace App\Listeners;

use App\Enum\UserRoleEnum;
use App\Mail\TicketAssignedMail;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use Sgcomptech\FilamentTicketing\Events\NewAssignment;
use Sgcomptech\FilamentTicketing\Events\NewTicket;
use App\Models\Ticket;

class AssignTicket
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NewTicket $event): void
    {
        if (is_null($event->ticket->assigned_to_id)) {
            $user_id = User::where('email', '<EMAIL>')->first()?->id;

            if (!$user_id) {
                $user_id = User::whereIn('role', UserRoleEnum::adminAccessibleRoles())->first()->id;
            }

            $event->ticket->update([
                'assigned_to_id' => $user_id,
            ]);

            NewAssignment::dispatch($event->ticket);
        }
    }
}
