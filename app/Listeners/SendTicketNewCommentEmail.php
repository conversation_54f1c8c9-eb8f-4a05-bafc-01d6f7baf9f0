<?php

namespace App\Listeners;

use App\Mail\TicketNewCommentEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use Sgcomptech\FilamentTicketing\Events\NewComment;

class SendTicketNewCommentEmail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NewComment $event): void
    {
        Mail::to($event->comment->ticket->assigned_to->email)->send(new TicketNewCommentEmail($event->comment));
    }
}
