<?php

namespace App\Listeners;

use App\Mail\TicketAssignedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use Sgcomptech\FilamentTicketing\Events\NewAssignment;

class SendTicketAssignmentNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NewAssignment $event): void
    {
        Mail::to($event->ticket->assigned_to->email)->send(new TicketAssignedMail($event->ticket));
    }
}
