<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use Illuminate\Console\Command;
use Carbon\Carbon;

class ClearStuckMergeFlags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaigns:clear-stuck-merge-flags {--dry-run : Show what would be cleared without actually clearing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear stuck merge flags for campaigns that have been merging for too long';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('Checking for campaigns with stuck merge flags...');
        
        // Find campaigns with merging_in_progress = true
        $campaigns = Campaign::whereJsonContains('meta->merging_in_progress', true)->get();
        
        $clearedCount = 0;
        $staleThresholdMinutes = 30;
        
        foreach ($campaigns as $campaign) {
            $mergingStartTime = $campaign->getMeta('merging_started_at', null);
            $isStale = false;
            $minutesElapsed = 0;
            
            if ($mergingStartTime) {
                $startTime = Carbon::parse($mergingStartTime);
                $minutesElapsed = $startTime->diffInMinutes(now());
                $isStale = $minutesElapsed > $staleThresholdMinutes;
            } else {
                // No start time recorded, consider it stale
                $isStale = true;
                $minutesElapsed = 'unknown';
            }
            
            if ($isStale) {
                $this->warn("Campaign {$campaign->id}: Stuck merge flag detected (started {$minutesElapsed} minutes ago)");
                
                if (!$dryRun) {
                    $campaign->saveMeta('merging_in_progress', false);
                    $campaign->saveMeta('merging_started_at', null);
                    $campaign->log("Merge flag cleared by admin command due to stale state");
                    $this->info("  ✓ Cleared merge flag for campaign {$campaign->id}");
                } else {
                    $this->info("  → Would clear merge flag for campaign {$campaign->id}");
                }
                
                $clearedCount++;
            } else {
                $this->info("Campaign {$campaign->id}: Merge in progress ({$minutesElapsed} minutes) - within threshold");
            }
        }
        
        if ($clearedCount === 0) {
            $this->info('No stuck merge flags found.');
        } else {
            if ($dryRun) {
                $this->info("Found {$clearedCount} campaigns with stuck merge flags (use without --dry-run to clear them)");
            } else {
                $this->info("Cleared {$clearedCount} stuck merge flags.");
            }
        }
        
        return 0;
    }
}
