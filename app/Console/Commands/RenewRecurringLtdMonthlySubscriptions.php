<?php

namespace App\Console\Commands;

use App\Enum\PlanTypeEnum;
use App\Models\Subscription;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class RenewRecurringLtdMonthlySubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:renew-recurring-ltd-monthly-subscriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Renew recurring LTD Monthly subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $cutoffDate = now()->subDays(29);

        Subscription::query()
                    ->where('status', 'active')
                    ->whereHas('plan', fn(Builder $query) => $query->whereIn('type', [
                        PlanTypeEnum::LTD_MONTHLY->value,
                    ])
                    )
                    ->where(fn(Builder $query) => $query->whereNull('subscription_updated_at')
                                                        ->orWhere('subscription_updated_at', '<', $cutoffDate)
                    )
                    ->with(['plan', 'user'])
                    ->cursor()
                    ->each(function (Subscription $subscription) {
                        $this->info("Subscription Update: {$subscription->id}");

                        if ($subscription->user && $subscription->plan) {
                            $subscription->user->update([
                                'credits' => $subscription->plan->credits,
                                'credits_used' => 0,
                            ]);
                        }

                        $subscription->update([
                            'subscription_updated_at' => now(),
                        ]);
                    });
    }
}
