<?php

namespace App\Console\Commands;

use App\Service\AudioCleanupService;
use Illuminate\Console\Command;

class ClearAudioBooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:audio-books';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired audio files from S3 and downloads table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting audio cleanup process...');

        $result = (new AudioCleanupService())->cleanupExpiredAudioCampaigns();

        if (isset($result['error'])) {
            $this->error('Cleanup failed: ' . $result['error']);
            return 1;
        }

        $this->info('Audio cleanup completed successfully!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Expired downloads found', $result['expired_downloads']],
                ['S3 files deleted', $result['deleted_files']],
                ['Campaigns updated', $result['updated_campaigns']],
                ['Download records deleted', $result['deleted_downloads']],
                ['Failed deletions', $result['failed_deletions']],
            ]
        );

        if ($result['failed_deletions'] > 0) {
            $this->warn("Some files failed to delete. Check logs for details.");
        }

        return 0;
    }
}
