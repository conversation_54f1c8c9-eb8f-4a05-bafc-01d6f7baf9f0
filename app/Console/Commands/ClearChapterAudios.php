<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use App\Models\Chapter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ClearChapterAudios extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'audio:clear-expired {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear expired chapter audio files and ZIPs from S3 and update metadata';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $expirationDays = (int) config('app.audio_expiration_days', env('AUDIO_EXPIRATION_DAYS', 10));
        
        $this->info("Starting chapter audio cleanup...");
        $this->info("Expiration days: {$expirationDays}");
        $this->info("Dry run: " . ($dryRun ? 'YES' : 'NO'));
        $this->newLine();

        $cutoffDate = Carbon::now()->subDays($expirationDays);
        $this->info("Cutoff date: {$cutoffDate->format('Y-m-d H:i:s')}");
        $this->newLine();

        $stats = [
            'campaigns_processed' => 0,
            'chapters_processed' => 0,
            'audio_files_deleted' => 0,
            'zip_files_deleted' => 0,
            'campaigns_updated' => 0,
            'chapters_updated' => 0,
            'errors' => 0
        ];

        // Process campaigns for ZIP files
        $this->info("🗂️  Processing campaign ZIP files...");
        $this->processCampaignZips($cutoffDate, $dryRun, $stats);
        $this->newLine();

        // Process chapters for individual audio files
        $this->info("🎵 Processing chapter audio files...");
        $this->processChapterAudios($cutoffDate, $dryRun, $stats);
        $this->newLine();

        // Display summary
        $this->displaySummary($stats, $dryRun);

        return Command::SUCCESS;
    }

    /**
     * Process campaign ZIP files
     */
    private function processCampaignZips(Carbon $cutoffDate, bool $dryRun, array &$stats): void
    {
        // Only fetch campaigns that were updated within the expiration window
        // If a campaign was last updated before the cutoff date, it might have expired content
        $campaigns = Campaign::whereNotNull('meta')
            ->where('updated_at', '>=', $cutoffDate->copy()->subDays(1)) // Add 1 day buffer for safety
            ->get()
            ->filter(function($campaign) {
                $meta = $campaign->meta ?? [];

                // Check if has download URLs or any ZIP metadata
                if (isset($meta['chapter_audio_download_urls'])) {
                    return true;
                }

                // Check for any chapter_audio_zip_ keys
                foreach (array_keys($meta) as $key) {
                    if (str_starts_with($key, 'chapter_audio_zip_')) {
                        return true;
                    }
                }

                return false;
            });

        $totalCampaigns = Campaign::count();
        $candidateCampaigns = Campaign::whereNotNull('meta')
            ->where('updated_at', '>=', $cutoffDate->copy()->subDays(1))
            ->count();

        $this->info("Total campaigns: {$totalCampaigns}, Candidates: {$candidateCampaigns}, Processing: {$campaigns->count()}");

        foreach ($campaigns as $campaign) {
            $stats['campaigns_processed']++;
            $this->line("Processing campaign {$campaign->id}: {$campaign->topic}");

            $updated = false;
            $allMeta = $campaign->meta ?? [];

            // Check main download URLs
            $downloadUrls = $campaign->getMeta('chapter_audio_download_urls', []);
            if (!empty($downloadUrls)) {
                $updatedUrls = $this->processDownloadUrls($downloadUrls, $cutoffDate, $stats);
                if ($updatedUrls !== $downloadUrls) {
                    if (!$dryRun) {
                        $campaign->saveMeta('chapter_audio_download_urls', $updatedUrls);
                    }
                    $updated = true;
                }
            }

            // Check individual model ZIP metadata
            $modelKeys = array_filter(array_keys($allMeta), function($key) {
                return str_starts_with($key, 'chapter_audio_zip_');
            });

            foreach ($modelKeys as $metaKey) {
                $zipInfo = $campaign->getMeta($metaKey);
                if ($this->isZipExpired($zipInfo, $cutoffDate)) {
                    $this->info("  ❌ Expired ZIP: {$metaKey}");

                    if (isset($zipInfo['s3_path']) && !$dryRun) {
                        $this->deleteS3File($zipInfo['s3_path'], $stats);
                        // Remove only the specific ZIP metadata key, keep other meta
                        $this->removeMetaKey($campaign, $metaKey);
                    }
                    $stats['zip_files_deleted']++;
                    $updated = true;
                }
            }

            if ($updated) {
                $stats['campaigns_updated']++;
                if (!$dryRun) {
                    $campaign->log("Expired audio files cleaned up by ClearChapterAudios command");
                }
            }
        }
    }

    /**
     * Process chapter audio files
     */
    private function processChapterAudios(Carbon $cutoffDate, bool $dryRun, array &$stats): void
    {
        // Only fetch chapters that were updated within the expiration window
        // If a chapter was last updated before the cutoff date, it might have expired content
        $chapters = Chapter::whereNotNull('meta')
            ->where('updated_at', '>=', $cutoffDate->copy()->subDays(1)) // Add 1 day buffer for safety
            ->whereRaw("JSON_EXTRACT(meta, '$.audio_models') IS NOT NULL")
            ->get();

        $totalChapters = Chapter::count();
        $candidateChapters = Chapter::whereNotNull('meta')
            ->where('updated_at', '>=', $cutoffDate->copy()->subDays(1))
            ->count();

        $this->info("Total chapters: {$totalChapters}, Candidates: {$candidateChapters}, Processing: {$chapters->count()}");

        foreach ($chapters as $chapter) {
            $stats['chapters_processed']++;
            $this->line("Processing chapter {$chapter->id}: {$chapter->plain_title}");

            $audioModels = $chapter->getMeta('audio_models', []);
            $updated = false;
            $originalCount = count($audioModels);

            foreach ($audioModels as $model => $audioInfo) {
                if ($this->isAudioExpired($audioInfo, $cutoffDate)) {
                    $this->info("  ❌ Expired audio: {$model}");

                    if (isset($audioInfo['s3_path']) && !$dryRun) {
                        $this->deleteS3File($audioInfo['s3_path'], $stats);
                        unset($audioModels[$model]);
                    }
                    $stats['audio_files_deleted']++;
                    $updated = true;
                }
            }

            if ($updated && !$dryRun) {
                $stats['chapters_updated']++;

                // Update audio_models
                $chapter->saveMeta('audio_models', $audioModels);

                // If all audio models are removed, clean up related metadata
                if (empty($audioModels) && $originalCount > 0) {
                    $this->cleanupChapterAudioMeta($chapter);
                }

                $chapter->campaign->log("Chapter {$chapter->id} expired audio files cleaned up");
            } elseif ($updated) {
                $stats['chapters_updated']++;
            }
        }
    }

    /**
     * Process download URLs and remove expired ones
     */
    private function processDownloadUrls(array $downloadUrls, Carbon $cutoffDate, array &$stats): array
    {
        $updatedUrls = $downloadUrls;

        foreach ($downloadUrls as $model => $urlInfo) {
            if ($this->isUrlExpired($urlInfo, $cutoffDate)) {
                $this->info("  ❌ Expired download URL: {$model}");
                unset($updatedUrls[$model]);
                $stats['zip_files_deleted']++;
            }
        }

        return $updatedUrls;
    }

    /**
     * Check if ZIP is expired
     */
    private function isZipExpired(array $zipInfo, Carbon $cutoffDate): bool
    {
        if (!isset($zipInfo['created_at'])) {
            return false;
        }

        try {
            $createdAt = Carbon::parse($zipInfo['created_at']);
            return $createdAt->lt($cutoffDate);
        } catch (\Exception $e) {
            $this->error("Error parsing ZIP creation date: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Check if audio is expired
     */
    private function isAudioExpired(array $audioInfo, Carbon $cutoffDate): bool
    {
        // Check for both 'created_at' and 'generated_at' fields
        $dateField = $audioInfo['created_at'] ?? $audioInfo['generated_at'] ?? null;

        if (!$dateField) {
            return false;
        }

        try {
            $createdAt = Carbon::parse($dateField);
            return $createdAt->lt($cutoffDate);
        } catch (\Exception $e) {
            $this->error("Error parsing audio creation date: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Check if download URL is expired
     */
    private function isUrlExpired(array $urlInfo, Carbon $cutoffDate): bool
    {
        if (!isset($urlInfo['created_at'])) {
            return false;
        }

        try {
            $createdAt = Carbon::parse($urlInfo['created_at']);
            return $createdAt->lt($cutoffDate);
        } catch (\Exception $e) {
            $this->error("Error parsing URL creation date: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Delete file from S3
     */
    private function deleteS3File(string $s3Path, array &$stats): void
    {
        try {
            if (Storage::disk('s3')->exists($s3Path)) {
                Storage::disk('s3')->delete($s3Path);
                $this->info("    🗑️  Deleted: {$s3Path}");
            } else {
                $this->warn("    ⚠️  File not found: {$s3Path}");
            }
        } catch (\Exception $e) {
            $this->error("    ❌ Failed to delete {$s3Path}: {$e->getMessage()}");
            $stats['errors']++;
        }
    }

    /**
     * Display cleanup summary
     */
    private function displaySummary(array $stats, bool $dryRun): void
    {
        $this->info("📊 Cleanup Summary:");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Campaigns Processed', $stats['campaigns_processed']],
                ['Chapters Processed', $stats['chapters_processed']],
                ['Audio Files ' . ($dryRun ? 'Would Be ' : '') . 'Deleted', $stats['audio_files_deleted']],
                ['ZIP Files ' . ($dryRun ? 'Would Be ' : '') . 'Deleted', $stats['zip_files_deleted']],
                ['Campaigns ' . ($dryRun ? 'Would Be ' : '') . 'Updated', $stats['campaigns_updated']],
                ['Chapters ' . ($dryRun ? 'Would Be ' : '') . 'Updated', $stats['chapters_updated']],
                ['Errors', $stats['errors']],
            ]
        );

        if ($dryRun) {
            $this->warn("🔍 This was a dry run. No files were actually deleted.");
            $this->info("💡 Run without --dry-run to perform actual cleanup.");
        } else {
            $this->info("✅ Cleanup completed successfully!");
        }
    }

    /**
     * Remove a specific meta key from campaign while preserving other metadata
     */
    private function removeMetaKey(Campaign $campaign, string $key): void
    {
        $meta = $campaign->meta ?? [];
        unset($meta[$key]);
        $campaign->meta = $meta;
        $campaign->save();
    }

    /**
     * Clean up chapter audio metadata when all audio models are removed
     */
    private function cleanupChapterAudioMeta(Chapter $chapter): void
    {
        // Remove audio-related metadata when no audio models remain
        $chapter->saveMeta('audio_voice', null);
        $chapter->saveMeta('audio_s3_path', null);
        $chapter->saveMeta('audio_generated_at', null);

        $this->info("    🧹 Cleaned up all audio metadata for chapter {$chapter->id}");
    }
}
