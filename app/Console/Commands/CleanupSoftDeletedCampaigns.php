<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use App\Models\Chapter;
use App\Models\Download;
use App\Models\Section;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class CleanupSoftDeletedCampaigns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaigns:cleanup-soft-deleted {--days=15 : Number of days after soft delete to permanently delete}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Permanently delete campaigns that have been soft deleted for more than specified days and cleanup their S3 files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Starting cleanup of campaigns soft deleted before: {$cutoffDate->format('Y-m-d H:i:s')}");

        // Find campaigns that have been soft deleted for more than specified days
        // Use chunk for better memory management with large datasets
        $campaignCount = Campaign::onlyTrashed()
            ->where('deleted_at', '<=', $cutoffDate)
            ->count();

        if ($campaignCount === 0) {
            $this->info('No campaigns found for cleanup.');
            return;
        }

        $this->info("Found {$campaignCount} campaigns to permanently delete.");

        // Process campaigns in chunks to avoid memory issues
        Campaign::onlyTrashed()
            ->where('deleted_at', '<=', $cutoffDate)
            ->chunk(10, function ($campaigns) {
                foreach ($campaigns as $campaign) {
                    $this->cleanupCampaign($campaign);
                }
            });

        $this->info('Cleanup completed successfully.');
    }

    /**
     * Clean up a single campaign and all its related data
     */
    private function cleanupCampaign(Campaign $campaign): void
    {
        $this->info("Cleaning up campaign ID: {$campaign->id}");

        try {
            // 1. Delete S3 files from downloads
            $this->cleanupDownloadFiles($campaign);

            // 2. Extract and delete images from chapters and sections HTML content
            $this->cleanupHtmlImages($campaign);

            // 3. Force delete all related records
            $this->forceDeleteRelatedRecords($campaign);

            // 4. Force delete the campaign itself
            $campaign->forceDelete();

            $this->info("Successfully cleaned up campaign ID: {$campaign->id}");

        } catch (\Exception $e) {
            $this->error("Failed to cleanup campaign ID: {$campaign->id} - Error: {$e->getMessage()}");
        }
    }

    /**
     * Delete S3 files from downloads table
     */
    private function cleanupDownloadFiles(Campaign $campaign): void
    {
        // Get only paths that are not empty for better performance
        $downloadPaths = Download::onlyTrashed()
            ->where('campaign_id', $campaign->id)
            ->whereNotNull('path')
            ->where('path', '!=', '')
            ->pluck('path')
            ->toArray();

        if (empty($downloadPaths)) {
            $this->info("  Downloads cleanup: 0 files to delete");
            return;
        }

        $deletedCount = 0;
        $errorCount = 0;

        // Batch delete for better performance
        foreach ($downloadPaths as $path) {
            try {
                if (Storage::disk('s3')->exists($path)) {
                    Storage::disk('s3')->delete($path);
                    $deletedCount++;
                }
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("  Failed to delete S3 file: {$path} - {$e->getMessage()}");
            }
        }

        $this->info("  Downloads cleanup: {$deletedCount} files deleted, {$errorCount} errors");
    }

    /**
     * Extract and delete images from chapters and sections HTML content
     */
    private function cleanupHtmlImages(Campaign $campaign): void
    {
        $imagePaths = [];
        $s3Domain = 'aiebook.nyc3.digitaloceanspaces.com';

        // Get chapters with only needed fields for better performance
        $chapters = Chapter::onlyTrashed()
            ->where('campaign_id', $campaign->id)
            ->select('id', 'title', 'intro')
            ->get();

        $this->info("  Found {$chapters->count()} chapters to process");

        // Get sections with only needed fields for better performance
        $chapterIds = $chapters->pluck('id')->toArray();

        if (empty($chapterIds)) {
            $this->info("  No chapters found, skipping sections");
            return;
        }

        $sections = Section::onlyTrashed()
            ->whereIn('chapter_id', $chapterIds)
            ->select('id', 'title', 'intro', 'body')
            ->get();

        $this->info("  Found {$sections->count()} sections to process");

        // Extract image paths from chapters
        foreach ($chapters as $chapter) {
            $imagePaths = array_merge($imagePaths, $this->extractImagePaths($chapter->title, $s3Domain));
            $imagePaths = array_merge($imagePaths, $this->extractImagePaths($chapter->intro, $s3Domain));
        }

        // Extract image paths from sections
        foreach ($sections as $section) {
            $imagePaths = array_merge($imagePaths, $this->extractImagePaths($section->title, $s3Domain));
            $imagePaths = array_merge($imagePaths, $this->extractImagePaths($section->intro, $s3Domain));
            $imagePaths = array_merge($imagePaths, $this->extractImagePaths($section->body, $s3Domain));
        }

        // Remove duplicates
        $imagePaths = array_unique($imagePaths);

        // Delete images from S3
        $deletedCount = 0;
        $errorCount = 0;

        foreach ($imagePaths as $imagePath) {
            try {
                if (Storage::disk('s3')->exists($imagePath)) {
                    Storage::disk('s3')->delete($imagePath);
                    $deletedCount++;
                }
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("  Failed to delete HTML image: {$imagePath} - {$e->getMessage()}");
            }
        }

        $this->info("  HTML images cleanup: {$deletedCount} files deleted, {$errorCount} errors");
    }

    /**
     * Extract S3 image paths from HTML content
     */
    private function extractImagePaths(?string $htmlContent, string $s3Domain): array
    {
        if (empty($htmlContent)) {
            return [];
        }

        $imagePaths = [];

        // Pattern to match S3 URLs in HTML content
        $pattern = '/https?:\/\/' . preg_quote($s3Domain, '/') . '\/([^"\s<>]+)/i';

        if (preg_match_all($pattern, $htmlContent, $matches)) {
            foreach ($matches[1] as $path) {
                // Clean up the path (remove any trailing characters that might not be part of the filename)
                $cleanPath = preg_replace('/[^a-zA-Z0-9\/_.-].*$/', '', $path);
                if (!empty($cleanPath)) {
                    $imagePaths[] = $cleanPath;
                }
            }
        }

        return $imagePaths;
    }

    /**
     * Force delete all related records
     */
    private function forceDeleteRelatedRecords(Campaign $campaign): void
    {
        // Force delete in optimal order to avoid foreign key issues

        // 1. Force delete sections first (child records)
        $chapterIds = Chapter::onlyTrashed()
            ->where('campaign_id', $campaign->id)
            ->pluck('id')
            ->toArray();

        if (!empty($chapterIds)) {
            Section::onlyTrashed()
                ->whereIn('chapter_id', $chapterIds)
                ->forceDelete();
        }

        // 2. Force delete chapters
        Chapter::onlyTrashed()
            ->where('campaign_id', $campaign->id)
            ->forceDelete();

        // 3. Force delete downloads
        Download::onlyTrashed()
            ->where('campaign_id', $campaign->id)
            ->forceDelete();

        // 4. Force delete reviews if they exist
        if (class_exists('App\Models\CampaignReview')) {
            \App\Models\CampaignReview::onlyTrashed()
                ->where('campaign_id', $campaign->id)
                ->forceDelete();
        }

        $this->info("  All related records force deleted");
    }
}
