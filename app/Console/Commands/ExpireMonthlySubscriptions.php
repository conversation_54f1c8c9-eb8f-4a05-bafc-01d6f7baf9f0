<?php

namespace App\Console\Commands;

use App\Enum\PlanTypeEnum as EnumPlanTypeEnum;
use App\Models\Subscription;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class ExpireMonthlySubscriptions extends Command
{
    protected $signature = 'app:subscription-monthly-expired';

    protected $description = 'Expire monthly subscriptions and refund subscriptions that are older than 30 days';

    protected $subscriptionTypes;

    public function __construct()
    {
        parent::__construct();

        $this->subscriptionTypes = [
            'Monthly' => ['value' => EnumPlanTypeEnum::MONTHLY->value, 'days' => 30],
            'Refund' => ['value' => EnumPlanTypeEnum::REFUND->value, 'days' => 30],
        ];
    }

    public function handle()
    {
        $this->info('Starting monthly subscription expiration process...');

        foreach ($this->subscriptionTypes as $type => $config) {
            $this->expireSubscriptions($type, $config);
        }
        
        $this->info("Processed expired monthly subscriptions.");
    }

    protected function expireSubscriptions($type, $config)
    {
        Subscription::query()
            ->where('status', 'active')
            ->whereHas('plan', function ($query) use ($config) {
                $query->whereIn('type', [$config['value']]);
            })
            ->where(function (Builder $query) use ($config) {
                $query->whereNull('subscription_updated_at')
                    ->orWhere('subscription_updated_at', '<', now()->subDays($config['days'])->toDateTimeString());
            })
            ->cursor()
            ->each(function (Subscription $subscription) use ($type) {
                $this->info("{$type} Subscription Expire: {$subscription->id}");
                $subscription->update(['status' => 'expired']);
            });
    }
}
