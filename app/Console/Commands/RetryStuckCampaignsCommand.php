<?php

namespace App\Console\Commands;

use App\Actions\RunCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use Illuminate\Console\Command;
use Carbon\Carbon;

class RetryStuckCampaignsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaigns:retry-stuck {--dry-run : Show what would be retried without actually retrying} {--max-retry=4 : Maximum number of retry attempts (default: 4)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check campaigns that are in IN_PROGRESS status for more than 30 minutes and auto retry them (configurable max retries)';

    /**
     * Execute the console command.
     */
    public function handle(RunCampaignAction $runCampaignAction)
    {
        $maxRetry = (int) $this->option('max-retry');

        $this->info("Starting stuck campaigns retry process (max retry: {$maxRetry})...");

        $cutoffTime = Carbon::now()->subMinute(30);

        // Find campaigns that are stuck in IN_PROGRESS status for more than 1 hour
        // and have not exceeded the maximum retry limit
        $stuckCampaigns = Campaign::query()
            ->where('status', CampaignStatusEnum::IN_PROGRESS->value)
            ->where('updated_at', '<', $cutoffTime)
            ->where('scheduler_retry', '<', $maxRetry)
            ->get();

        // Also check for campaigns that have reached the maximum retry limit
        $maxRetriedCampaigns = Campaign::query()
            ->where('status', CampaignStatusEnum::IN_PROGRESS->value)
            ->where('updated_at', '<', $cutoffTime)
            ->where('scheduler_retry', '>=', $maxRetry)
            ->count();

        if ($stuckCampaigns->isEmpty()) {
            $this->info('No stuck campaigns found that can be retried.');
            if ($maxRetriedCampaigns > 0) {
                $this->warn("Found {$maxRetriedCampaigns} campaigns that have reached the maximum retry limit ({$maxRetry}) and need manual intervention.");
            }
            return 0;
        }

        $this->info("Found {$stuckCampaigns->count()} stuck campaigns that can be retried:");
        if ($maxRetriedCampaigns > 0) {
            $this->warn("Also found {$maxRetriedCampaigns} campaigns that have reached the maximum retry limit.");
        }

        $retriedCount = 0;
        $failedCount = 0;
        $maxRetriesReachedCount = 0;

        foreach ($stuckCampaigns as $campaign) {
            $timeStuck = Carbon::parse($campaign->updated_at)->diffForHumans();
            $retryCount = $campaign->scheduler_retry ?? 0;

            $this->line("Campaign ID: {$campaign->id} | Stuck for: {$timeStuck} | Retry count: {$retryCount}/{$maxRetry}");

            if ($this->option('dry-run')) {
                $this->line("[DRY RUN] Would retry this campaign");
                continue;
            }

            try {
                // Increment the retry counter before attempting retry
                $campaign->increment('scheduler_retry');

                $campaign->log("Auto-retry initiated by RetryStuckCampaignsCommand - stuck for {$timeStuck} - retry attempt #{$campaign->scheduler_retry}");
                $runCampaignAction->execute($campaign, true);

                $this->line("Successfully retried campaign {$campaign->id} (attempt #{$campaign->scheduler_retry})");
                $retriedCount++;

            } catch (\Exception $e) {
                $this->error("Failed to retry campaign {$campaign->id}: {$e->getMessage()}");
                $campaign->log("Auto-retry failed (attempt #{$campaign->scheduler_retry}): {$e->getMessage()}");
                $failedCount++;

                // If this was the maximum retry attempt, mark campaign as failed
                if ($campaign->scheduler_retry >= $maxRetry) {
                    $campaign->update(['status' => CampaignStatusEnum::FAILED->value]);
                    $campaign->log("Campaign marked as FAILED after reaching maximum retry attempts ({$maxRetry})");
                    $this->error("Campaign {$campaign->id} marked as FAILED after {$maxRetry} retry attempts");
                    $maxRetriesReachedCount++;
                }
            }
        }

        if ($this->option('dry-run')) {
            $this->info("Dry run completed. {$stuckCampaigns->count()} campaigns would be retried.");
        } else {
            $this->info("Retry process completed!");
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Total stuck campaigns found', $stuckCampaigns->count()],
                    ['Successfully retried', $retriedCount],
                    ['Failed to retry', $failedCount],
                    ['Campaigns marked as FAILED (max retries reached)', $maxRetriesReachedCount],
                ]
            );

            if ($failedCount > 0) {
                $this->warn("Some campaigns failed to retry. Check logs for details.");
            }
        }

        return 0;
    }
}
