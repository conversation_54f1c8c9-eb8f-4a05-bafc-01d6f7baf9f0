<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('clear:audio-books')->daily();
        $schedule->command('app:renew-recurring-ltd-monthly-subscriptions')->daily();
        $schedule->command('campaigns:retry-stuck')->everyTenMinutes();
        $schedule->command('campaigns:clear-stuck-merge-flags')->everyFifteenMinutes();
        $schedule->command('horizon:snapshot')->everyFiveMinutes();
        $schedule->command('horizon:terminate')->everyThreeHours(); // hotfix: restart horizon every 3 hours to avoid memory leaks
        $schedule->command('app:subscription-monthly-expired')->daily()->runInBackground();
        $schedule->command('queue:prune-failed --hours=168')->daily();
        $schedule->command('queue:prune-batches --hours=48 --unfinished=72')->daily();

        // Clean up expired chapter audio files and ZIPs
        $schedule->command('audio:clear-expired')->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
