<?php

namespace App\Livewire;

use Filament\Facades\Filament;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;

class UserProfileComponent extends Component implements HasForms
{
    use InteractsWithForms;

    protected string $view = "livewire.user-profile-component";

    public ?array $data = [];
    public $user;
    public $userClass;
    public array $only = ['name','email', 'billing_address'];

    public function mount()
    {
        $this->user = Filament::getCurrentPanel()->auth()->user();
        $this->userClass = get_class($this->user);
        $this->form->fill($this->user->only($this->only));
    }

    protected function getProfileFormSchema()
    {
        $name = TextInput::make('name')
                    ->required()
                    ->label(__('Name'));
        $email = TextInput::make('email')
                    ->required()
                    ->email()
                    ->unique($this->userClass, ignorable: $this->user)
                    ->label(__('Email'));

        $billing_address = Textarea::make('billing_address')
                           ->label('Billing Address')
                           ->rows(5);

        return [
            Group::make([
                $name,
                $email,
                $billing_address,
            ])->columnSpan(3)
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema($this->getProfileFormSchema())->columns(3)
            ->statePath('data');
    }

    public function submit()
    {
        $data = collect($this->form->getState())->only($this->only)->all();
        $this->user->update($data);
        Notification::make()
            ->success()
            ->title(__('Profile updated successfully!'))
            ->send();
    }
}
