<?php

namespace App\Livewire;

use Filament\Facades\Filament;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;
use Filament\Forms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Hash;
use Filament\Notifications\Notification;

class UpdatePasswordComponent extends Component implements HasForms
{
    use InteractsWithForms;

    protected string $view = "livewire.update-password-component";

    public ?array $data = [];
    public $user;

    public static $sort = 20;

    public function mount()
    {
        $this->user = Filament::getCurrentPanel()->auth()->user();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make("current_password")
                    ->required()
                    ->password()
                    ->rule("current_password"),
                Forms\Components\TextInput::make("new_password")
                    ->password()
                    ->rule("min:8")
                    ->required(),
                Forms\Components\TextInput::make("new_password_confirmation")
                    ->label(__('Confirm Password'))
                    ->password()
                    ->same("new_password")
                    ->required(),
            ])
            ->statePath('data');
    }

    public function submit()
    {
        $data = collect($this->form->getState())->only('new_password')->all();
        $this->user->update([
            'password' => Hash::make($data['new_password'])
        ]);
        session()->forget('password_hash_' . Filament::getCurrentPanel()->getAuthGuard());
        Filament::auth()->login($this->user);
        $this->reset(["data"]);
        Notification::make()
            ->success()
            ->title(__('Password updated successfully!'))
            ->send();
    }
}
