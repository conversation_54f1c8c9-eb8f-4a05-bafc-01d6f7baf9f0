<?php

namespace App\Livewire;
use Filament\Facades\Filament;

use Livewire\Component;
class SubscriptionProfileComponent extends Component
{
    protected string $view = "livewire.subscription-profile-component";
    public ?array $data = [];
    public $user;
    public $userClass;
    public array $only = ['name','email'];

    public function mount()
    {
        $this->user = Filament::getCurrentPanel()->auth()->user();
        $this->userClass = get_class($this->user);
    }

}
