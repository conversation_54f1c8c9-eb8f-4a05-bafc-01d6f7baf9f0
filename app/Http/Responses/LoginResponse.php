<?php

namespace App\Http\Responses;

use App\Filament\Resources\CampaignResource;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;

class LoginResponse extends \Filament\Http\Responses\Auth\LoginResponse
{
    /**
     * @param $request
     *
     * @return RedirectResponse|Redirector
     */
    public function toResponse($request): RedirectResponse|Redirector
    {
        return redirect()->to(CampaignResource::getUrl('index'));
    }
}