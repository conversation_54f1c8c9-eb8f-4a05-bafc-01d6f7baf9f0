<?php

namespace App\Http\Middleware;

use App\Filament\Resources\CampaignResource;
use Closure;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;

class CheckRemainCredit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth()->user()->remainCredit() <= 0) {
            Notification::make()
                ->warning()
                ->title("You don't have sufficient credit to create new campaign.")
                ->body('Please add credit to use this feature.')
                ->send();

            return redirect()->to(CampaignResource::getUrl('index'));
        }

        return $next($request);
    }
}
