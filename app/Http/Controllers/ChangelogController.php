<?php

namespace App\Http\Controllers;

use App\Models\AppUpdate;
use App\Service\UpdateNotificationService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class ChangelogController extends Controller
{
    public function __construct(
        private UpdateNotificationService $updateNotificationService
    ) {}

    /**
     * Display the changelog index page
     */
    public function index(Request $request): View
    {
        $query = AppUpdate::published()->orderBy('released_at', 'desc');

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }

        if ($request->filled('category')) {
            $query->where('category', $request->input('category'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('summary', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $updates = $query->paginate(10);

        // Get unread updates for authenticated users
        $unreadUpdates = [];
        if (auth()->check()) {
            $unreadUpdates = $this->updateNotificationService
                ->getUnreadUpdatesForUser(auth()->user())
                ->pluck('id')
                ->toArray();
        }

        return view('changelog.index', compact('updates', 'unreadUpdates'));
    }

    /**
     * Display a specific update
     */
    public function show(AppUpdate $update): View
    {
        // Mark as read for authenticated users
        if (auth()->check()) {
            $this->updateNotificationService->markAsReadForUser($update, auth()->user());
        }

        return view('changelog.show', compact('update'));
    }

    /**
     * Mark an update as read (AJAX)
     */
    public function markAsRead(Request $request, AppUpdate $update): JsonResponse
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $this->updateNotificationService->markAsReadForUser($update, auth()->user());

        return response()->json(['success' => true]);
    }
}
