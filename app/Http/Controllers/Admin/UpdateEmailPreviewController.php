<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\TestUpdateNotificationMail;
use App\Models\AppUpdate;
use App\Models\User;
use App\Models\UserUpdatePreference;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class UpdateEmailPreviewController extends Controller
{
    /**
     * Preview the email for an update
     */
    public function preview(AppUpdate $update): View
    {
        try {
            // Use the current authenticated user for preview, or create a sample user
            $sampleUser = auth()->user();
            if (!$sampleUser) {
                // Create a sample user for preview (not saved to database)
                $sampleUser = new User();
                $sampleUser->name = '<PERSON>';
                $sampleUser->email = '<EMAIL>';
                $sampleUser->first_name = 'John';
            }

            // Create sample preferences (not saved to database)
            $samplePreferences = new UserUpdatePreference();
            $samplePreferences->email_major_updates = true;
            $samplePreferences->email_minor_updates = true;
            $samplePreferences->email_patch_updates = true;
            $samplePreferences->email_security_updates = true;
            $samplePreferences->unsubscribe_token = 'sample-token-for-preview';

            // Validate that we have all required data
            if (!$update || !$update->id) {
                throw new \InvalidArgumentException('Invalid update provided');
            }

            // Create the mailable - use test mailable to avoid queue serialization issues
            $mailable = new TestUpdateNotificationMail($update, $sampleUser->email, $sampleUser->name);

            // Render the email content
            $emailContent = $mailable->render();

            return view('admin.email-preview', [
                'update' => $update,
                'emailContent' => $emailContent,
                'mailable' => $mailable,
                'sampleUser' => $sampleUser,
                'samplePreferences' => $samplePreferences
            ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Email preview error: ' . $e->getMessage(), [
                'update_id' => $update->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            // Return error view or redirect with error
            return view('admin.email-preview-error', [
                'error' => $e->getMessage(),
                'update' => $update
            ]);
        }
    }
}
