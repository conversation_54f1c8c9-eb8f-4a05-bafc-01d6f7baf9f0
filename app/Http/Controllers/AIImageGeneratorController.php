<?php

namespace App\Http\Controllers;

use App\Service\AIModel\OpenAIClient;
use App\Service\FinalizeImageService;
use App\Enum\ImageSources;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AIImageGeneratorController extends Controller
{
    public function getAvailableModels()
    {
        try {
            $models = ImageSources::aiImageModels();

            // Set default model based on user's access
            $defaultModel = null;
            if (auth()->user()->hasGptImage1Access()) {
                $defaultModel = ImageSources::GPT_IMAGE_1;
            } elseif (auth()->user()->hasGpt4Access()) {
                $defaultModel = ImageSources::DALL_E_3;
            }

            return response()->json([
                'models' => $models,
                'default' => $defaultModel
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get available AI image models', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'error' => 'Failed to get available models'
            ], 500);
        }
    }

    public function generateImage(Request $request)
    {
        $text = $request->input('text');
        $model = $request->input('model', ImageSources::DALL_E_3); // Default to DALL-E 3

        Log::info('Image generation request', [
            'user_id' => auth()->id(),
            'text_length' => strlen($text),
            'model' => $model
        ]);

        // Check if user has OpenAI API key
        $apiKey = auth()->user()->getOpenAiApiKey();
        if (!$apiKey) {
            Log::warning('User attempted image generation without OpenAI API key', [
                'user_id' => auth()->id()
            ]);
            return response()->json([
                'error' => 'OpenAI API key not configured. Please add your OpenAI API key in your account settings.'
            ], 400);
        }

        try {
            $openAIClient = new OpenAIClient();
            $optimizedImage = new FinalizeImageService();
            $imageUrl = $openAIClient->generateImage($text, $apiKey, $model);
            $optimizeImageUrl = $optimizedImage->handleImage($imageUrl, imageFileName(str($text)->limit()->slug()));

            Log::info('Image generation completed', [
                'user_id' => auth()->id(),
                'image_url' => $optimizeImageUrl
            ]);

            return response()->json([
                'url' => $optimizeImageUrl
            ]);
        } catch (\Exception $e) {
            Log::error('Image generation failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => 'Failed to generate image: '.$e->getMessage()
            ], 500);
        }
    }

    public function generateContent(Request $request)
    {
        // Validate request
        $request->validate([
            'prompt' => 'required|string|max:10000',
            'stream' => 'boolean'
        ]);

        $prompt = $request->input('prompt');
        $stream = $request->input('stream', false);

        Log::info('Text generation request', [
            'user_id' => auth()->id(),
            'prompt_length' => strlen($prompt),
            'stream' => $stream
        ]);

        // Check if user has OpenAI API key
        $apiKey = auth()->user()->getOpenAiApiKey();
        if (!$apiKey) {
            Log::warning('User attempted text generation without OpenAI API key', [
                'user_id' => auth()->id()
            ]);
            return response()->json([
                'error' => 'OpenAI API key not configured. Please add your OpenAI API key in your account settings.'
            ], 400);
        }

        $openAIClient = new OpenAIClient();

        try {
            if ($stream) {
                Log::info('Starting streaming text generation', [
                    'user_id' => auth()->id(),
                    'prompt_length' => strlen($prompt)
                ]);

                return response()->stream(function () use ($openAIClient, $prompt, $apiKey) {
                    try {
                        $openAIClient->streamAIModel($prompt, $apiKey, function ($chunk) {
                            echo "data: ".json_encode(['content' => $chunk])."\n\n";
                        });
                        echo "data: ".json_encode(['done' => true])."\n\n";

                        Log::info('Streaming text generation completed', [
                            'user_id' => auth()->id()
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error during streaming text generation', [
                            'user_id' => auth()->id(),
                            'error' => $e->getMessage()
                        ]);
                        echo "data: ".json_encode(['error' => $e->getMessage()])."\n\n";
                    }
                }, 200, [
                    'Content-Type' => 'text/event-stream',
                    'Cache-Control' => 'no-cache',
                    'Connection' => 'keep-alive',
                    'X-Accel-Buffering' => 'no',
                ]);
            } else {
                $content = $openAIClient->callAIModel($prompt, $apiKey);
                Log::info('Non-streaming text generation completed', [
                    'user_id' => auth()->id(),
                    'content_length' => strlen($content)
                ]);
                return response()->json([
                    'content' => $content
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Text generation failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => 'Failed to generate content: '.$e->getMessage()
            ], 500);
        }
    }
}
