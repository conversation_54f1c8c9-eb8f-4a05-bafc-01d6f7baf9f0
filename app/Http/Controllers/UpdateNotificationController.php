<?php

namespace App\Http\Controllers;

use App\Service\UpdateNotificationService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class UpdateNotificationController extends Controller
{
    public function __construct(
        private UpdateNotificationService $updateNotificationService
    ) {}

    /**
     * Unsubscribe user from update notifications
     */
    public function unsubscribe(string $token): View
    {
        $success = $this->updateNotificationService->unsubscribeUser($token);

        return view('update-notifications.unsubscribe', compact('success'));
    }
}
