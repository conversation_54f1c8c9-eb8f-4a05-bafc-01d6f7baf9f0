<?php

namespace App\Http\Controllers;

use App\Models\Campaign;
use App\Models\Chapter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ChapterAudioController extends Controller
{
    /**
     * Download chapter audio ZIP file for a campaign
     *
     * @param Campaign $campaign
     * @return BinaryFileResponse
     */
    public function downloadZip(Campaign $campaign, ?string $model = null)
    {
        // Check if user has access to this campaign
        if (!auth()->check() || (auth()->user()->id !== $campaign->user_id && !auth()->user()->isAdmin())) {
            abort(403, 'Unauthorized access');
        }

        // Get the requested model from route parameter or query parameter
        $requestedModel = $model ?: request()->get('model');

        // Get available models for this campaign
        $availableModels = $this->getAvailableModelsForCampaign($campaign);

        // If multiple models available and none specified, show selection
        if (count($availableModels) > 1 && !$requestedModel) {
            return $this->showModelSelection($campaign, $availableModels);
        }

        // Use the requested model or the first available one
        $selectedModel = $requestedModel ?: $availableModels[0] ?? null;

        if (!$selectedModel) {
            abort(404, 'No audio models found for this campaign');
        }

        $metaKey = $selectedModel ? "chapter_audio_zip_{$selectedModel}" : 'chapter_audio_zip';
        $zipInfo = $campaign->getMeta($metaKey);

        // If ZIP doesn't exist or has expired, start generation
        if (!$zipInfo || $this->isZipExpired($zipInfo)) {
            return $this->startZipGeneration($campaign, $selectedModel);
        }

        $s3Path = $zipInfo['s3_path'];
        if (!Storage::disk('s3')->exists($s3Path)) {
            // ZIP info exists but file is missing, regenerate
            return $this->startZipGeneration($campaign, $selectedModel);
        }

        // ZIP exists and is valid, redirect to S3 temporary URL
        $temporaryUrl = Storage::disk('s3')->temporaryUrl($s3Path, now()->addMinutes(30));
        return redirect()->to($temporaryUrl);
    }

    /**
     * Download individual chapter audio file
     *
     * @param Chapter $chapter
     * @return BinaryFileResponse
     */
    public function downloadSingleChapter(Chapter $chapter)
    {
        // Check if user has access to this chapter's campaign
        if (!auth()->check() || (auth()->user()->id !== $chapter->campaign->user_id && !auth()->user()->isAdmin())) {
            abort(403, 'Unauthorized access');
        }

        // Get the requested model from query parameter
        $requestedModel = request()->get('model');

        $s3Path = $chapter->getAudioS3Path($requestedModel);
        if (!$s3Path) {
            abort(404, 'Chapter audio not found for the requested model');
        }

        if (!Storage::disk('s3')->exists($s3Path)) {
            abort(404, 'Audio file not found');
        }

        // For MP3 files, redirect to temporary URL (same as existing audio downloads)
        return redirect()->to(Storage::disk('s3')->temporaryUrl($s3Path, now()->addMinutes(30)));
    }

    /**
     * Get chapter audio ZIP information for a campaign
     *
     * @param Campaign $campaign
     * @param string|null $model
     * @param bool $skipAuthCheck Skip authentication check (for Filament contexts)
     * @return array|null
     */
    public function getZipInfo(Campaign $campaign, ?string $model = null, bool $skipAuthCheck = false): ?array
    {
        // Check if user has access to this campaign (skip for Filament contexts)
        if (!$skipAuthCheck && (!auth()->check() || (auth()->user()->id !== $campaign->user_id && !auth()->user()->isAdmin()))) {
            return null;
        }

        // Get available models and find the best ZIP to show
        $availableModels = $this->getAvailableModelsForCampaign($campaign);

        if (empty($availableModels)) {
            return null;
        }

        // If model specified, use it; otherwise use the first available
        $targetModel = $model ?: $availableModels[0];

        // Try model-specific ZIP first
        $metaKey = "chapter_audio_zip_{$targetModel}";
        $zipInfo = $campaign->getMeta($metaKey);

        // Fallback to generic ZIP for backward compatibility
        if (!$zipInfo) {
            $zipInfo = $campaign->getMeta('chapter_audio_zip');
        }

        if (!$zipInfo) {
            return null;
        }

        // Check if download link hasn't expired
        $expiresAt = \Carbon\Carbon::parse($zipInfo['expires_at'] ?? null);
        if (!$expiresAt || $expiresAt->isPast()) {
            return null;
        }

        return $zipInfo;
    }

    /**
     * Check if chapter audio ZIP is available for download
     *
     * @param Campaign $campaign
     * @param bool $skipAuthCheck Skip authentication check (for Filament contexts)
     * @return bool
     */
    public function isZipAvailable(Campaign $campaign, bool $skipAuthCheck = true): bool
    {
        return $this->getZipInfo($campaign, null, $skipAuthCheck) !== null;
    }

    /**
     * Get the download URL for chapter audio ZIP
     *
     * @param Campaign $campaign
     * @return string|null
     */
    public function getDownloadUrl(Campaign $campaign): ?string
    {
        $zipInfo = $this->getZipInfo($campaign, null, true); // Skip auth check for Filament
        if (!$zipInfo) {
            return null;
        }

        return route('download.chapter.audio', $campaign);
    }

    /**
     * Get formatted ZIP information for display
     *
     * @param Campaign $campaign
     * @return array|null
     */
    public function getFormattedZipInfo(Campaign $campaign): ?array
    {
        $zipInfo = $this->getZipInfo($campaign);
        if (!$zipInfo) {
            return null;
        }

        return [
            'file_name' => $zipInfo['file_name'],
            'chapter_count' => $zipInfo['chapter_count'],
            'created_at' => \Carbon\Carbon::parse($zipInfo['created_at'])->format('M j, Y \a\t g:i A'),
            'expires_at' => \Carbon\Carbon::parse($zipInfo['expires_at'])->format('M j, Y \a\t g:i A'),
            'download_url' => $this->getDownloadUrl($campaign),
            'size_mb' => $this->getFileSizeMB($zipInfo['s3_path'] ?? null),
        ];
    }

    /**
     * Get file size in MB from S3
     *
     * @param string|null $s3Path
     * @return string|null
     */
    private function getFileSizeMB(?string $s3Path): ?string
    {
        if (!$s3Path || !Storage::disk('s3')->exists($s3Path)) {
            return null;
        }

        try {
            $sizeBytes = Storage::disk('s3')->size($s3Path);
            $sizeMB = round($sizeBytes / 1024 / 1024, 2);
            return $sizeMB . ' MB';
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Sanitize filename for safe download
     *
     * @param string $name
     * @return string
     */
    private function sanitizeFileName(string $name): string
    {
        // Remove HTML tags and decode entities
        $name = strip_tags(html_entity_decode($name));

        // Replace problematic characters
        $name = preg_replace('/[^a-zA-Z0-9\-_\s]/', '', $name);

        // Replace spaces with underscores and limit length
        $name = str_replace(' ', '_', trim($name));

        return substr($name, 0, 50);
    }

    /**
     * Check if ZIP info has expired
     */
    private function isZipExpired(?array $zipInfo): bool
    {
        if (!$zipInfo) {
            return true;
        }

        $expiresAt = \Carbon\Carbon::parse($zipInfo['expires_at'] ?? null);
        return !$expiresAt || $expiresAt->isPast();
    }

    /**
     * Get available audio models for a campaign
     */
    private function getAvailableModelsForCampaign(Campaign $campaign): array
    {
        $models = [];

        foreach ($campaign->chapters as $chapter) {
            $chapterModels = $chapter->getAvailableAudioModels();
            $models = array_merge($models, $chapterModels);
        }

        return array_unique($models);
    }

    /**
     * Show model selection interface
     */
    private function showModelSelection(Campaign $campaign, array $availableModels)
    {
        // For now, just use the first model. In a real implementation,
        // you might want to show a selection page or modal
        return $this->startZipGeneration($campaign, $availableModels[0]);
    }

    /**
     * Start ZIP generation process
     */
    private function startZipGeneration(Campaign $campaign, ?string $model = null)
    {
        // Check if chapters have audio for the specified model
        if ($model) {
            $chaptersWithAudio = $campaign->chapters()
                ->whereNotNull("meta->audio_models->{$model}->s3_path")
                ->count();
        } else {
            $chaptersWithAudio = $campaign->chapters()
                ->where(function($query) {
                    $query->whereNotNull('meta->audio_s3_path')
                          ->orWhereNotNull('meta->audio_models');
                })
                ->count();
        }

        if ($chaptersWithAudio === 0) {
            $modelText = $model ? " for model '{$model}'" : '';
            abort(404, "No chapter audio files found for this campaign{$modelText}");
        }

        // Dispatch ZIP creation job with model
        \App\Jobs\ZipChapterAudioJob::dispatch($campaign, $model)->onQueue($campaign->getChapterAudioQueueName());

        // Send notification
        $modelText = $model ? " ({$model} model)" : '';
        \Filament\Notifications\Notification::make()
            ->title('ZIP Generation Started')
            ->body("We are creating your chapter audio ZIP file{$modelText}. You will receive an email when it's ready.")
            ->success()
            ->send();

        // Redirect back with message
        return redirect()->back()->with('message', 'ZIP generation started. You will receive an email when ready.');
    }
}
