<?php

namespace App\Http\Controllers;

use App\Models\RoadmapItem;
use App\Models\RoadmapLike;
use App\Models\FeatureRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PublicRoadmapController extends Controller
{
    public function index(Request $request)
    {
        // Get filters from request
        $filters = [
            'search' => $request->get('search', ''),
            'status' => $request->get('status', ''),
            'category' => $request->get('category', ''),
            'priority' => $request->get('priority', ''),
        ];

        // Load roadmap items with the same logic as the Filament page
        $roadmapItems = $this->loadRoadmapItems($filters);
        $statusCounts = $this->getStatusCounts();

        return view('public.roadmap', compact('roadmapItems', 'statusCounts', 'filters'));
    }

    public function toggleLike(Request $request, $roadmapItemId)
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to like roadmap items.'
            ], 401);
        }

        try {
            // Verify the roadmap item exists and is published
            RoadmapItem::published()->findOrFail($roadmapItemId);
            $userId = auth()->id();

            // Check if user already liked this item
            $existingLike = RoadmapLike::where('user_id', $userId)
                ->where('roadmap_item_id', $roadmapItemId)
                ->first();

            if ($existingLike) {
                // Unlike - remove the like
                $existingLike->delete();
                $action = 'unliked';
            } else {
                // Like - create new like
                RoadmapLike::create([
                    'user_id' => $userId,
                    'roadmap_item_id' => $roadmapItemId,
                ]);
                $action = 'liked';
            }

            // Get updated like count
            $likeCount = RoadmapLike::where('roadmap_item_id', $roadmapItemId)->count();
            $isLiked = $action === 'liked';

            return response()->json([
                'success' => true,
                'message' => "You have {$action} this roadmap item.",
                'like_count' => $likeCount,
                'is_liked' => $isLiked
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to toggle like: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'There was an error processing your request. Please try again.'
            ], 500);
        }
    }

    public function submitRequest(Request $request)
    {
        // Check if user is authenticated and has valid subscription
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to submit feature requests.'
            ], 401);
        }

        if (!auth()->user()->hasValidSubscription()) {
            return response()->json([
                'success' => false,
                'message' => 'A valid subscription is required to submit feature requests.'
            ], 403);
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:10',
            'type' => 'required|in:feature,improvement,bug',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            FeatureRequest::create([
                'user_id' => auth()->id(),
                'title' => $request->title,
                'content' => $request->content,
                'type' => $request->type,
                'status' => 'pending',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your request! Our team will review it and get back to you.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create feature request: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'There was an error submitting your request. Please try again.'
            ], 500);
        }
    }

    private function loadRoadmapItems($filters)
    {
        try {
            $query = RoadmapItem::published()->orderByRaw("
                CASE status
                    WHEN 'in_progress' THEN 1
                    WHEN 'planned' THEN 2
                    WHEN 'completed' THEN 3
                    WHEN 'cancelled' THEN 4
                END
            ")->orderBy('estimated_date', 'asc');

            // Apply filters
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            if (!empty($filters['priority'])) {
                $query->where('priority', $filters['priority']);
            }

            if (!empty($filters['search'])) {
                $search = $filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('summary', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
                });
            }

            return $query->with(['likes' => function ($query) {
                if (auth()->check()) {
                    $query->where('user_id', auth()->id());
                }
            }])->withCount('likes')->limit(50)->get();

        } catch (\Exception $e) {
            Log::error('Failed to load roadmap items: ' . $e->getMessage());
            return collect([]);
        }
    }

    private function getStatusCounts()
    {
        try {
            return [
                'planned' => RoadmapItem::published()->byStatus('planned')->count(),
                'in_progress' => RoadmapItem::published()->byStatus('in_progress')->count(),
                'completed' => RoadmapItem::published()->byStatus('completed')->count(),
                'cancelled' => RoadmapItem::published()->byStatus('cancelled')->count(),
            ];
        } catch (\Exception $e) {
            return [
                'planned' => 0,
                'in_progress' => 0,
                'completed' => 0,
                'cancelled' => 0,
            ];
        }
    }
}
