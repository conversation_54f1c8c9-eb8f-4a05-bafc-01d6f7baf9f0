<?php

namespace App\Http\Controllers;

use App\Enum\ResearchEbookCampaignIdeaTypeEnum;

use App\Models\Campaign;
use App\Models\CampaignReview;
use App\Models\PopularBookInNicheEntry;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use League\Csv\CannotInsertRecord;
use League\Csv\Exception;
use League\Csv\Writer;

class BulkDownloadController extends Controller
{



    public function downloadReviews(Campaign $campaign)
    {
        
        $filename = Str::slug(config('app.name') . '_export_' . auth()->user()->name . '_campaign_' . now()->toDateTimeString(), '_') . '.csv';


        $header = ['Name', 'Email', 'Rating', 'Comment',"Submission Time"];

        // $header = ['competition_level', 'keyword_difficulty', 'last_three_month_changes', 'low_top_of_page_bid', 'high_top_of_page_bid'];

        // $header = array_merge($generalHeader, $header);

        $csv = Writer::createFromString();
        $csv->insertOne($header);

        $reviews = $campaign->reviews()->get();

        $reviews->each(function (CampaignReview $campignReview) use ($csv) {

            $generalRow = [
                $campignReview->name,
                $campignReview->email,
                $campignReview->rating,
                $campignReview->comment,
                $campignReview->created_at?$campignReview->created_at->format("h:m d-m-y"):""

            ];

            $row = array_merge($generalRow);
            $csv->insertOne($row);
        });

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->streamDownload(function () use ($csv) {
            echo $csv->toString();
        }, $filename, $headers);
    }
    public function downloadCSV(Request $request)
    {
        $type = $request->input('type', 'book_title_generation');
        $exportType = $request->input('export_type', 'csv');

        if (!in_array($exportType, ['csv', 'txt'])) {
            $exportType = 'csv';
        }



        if ($type == ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE->value) {
            $header = ['id', "topic", 'book_name', 'author', 'summary', 'key_takeways', 'created_at', 'updated_at'];
        } else {
            $header = ['id', 'name', 'entry', 'created_at', 'updated_at'];
        }


        // load the CSV document from a string
        $csv = Writer::createFromString();

        // insert the header
        $csv->insertOne($header);
        if ($type == ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE->value) {
            return $this->popularBooksInNicheEntryRows($request, $exportType, $csv);
        }

        return $this->researchCampaignEntryRows($request, $exportType, $csv);
    }
    public function __invoke(Request $request)
    {
        if ($request->input('campaign_ids')) {
            $filename = Str::slug(config('app.name') . '_export_' . $request->user()->first_name . '_campaign_' . now()->toDateTimeString() . '_' . uniqid(), '_') . '.csv';
        } else {
            $filename = Str::slug(config('app.name') . '_export_' . $request->user()->first_name . '_' . now()->toDateTimeString() . '_' . uniqid(), '_') . '.csv';
        }

        $header = ['id', 'title', 'body', 'meta_description', 'link', 'tags', 'target', 'campaign_id', 'campaign_name'];

        $isMedPosterApp = SiteSetting::isMedPosterAI();

        if (!$isMedPosterApp) {
            $header[] = 'category';
        }

        // load the CSV document from a string
        $csv = Writer::createFromString();

        // insert the header
        $csv->insertOne($header);

        Content::query()
            ->when($request->input('campaign_ids'), fn($query) => $query->whereIn('campaign_id', str($request->input('campaign_ids'))->explode(',')->filter()->toArray()))
            ->when($request->input('ids'), fn($query) => $query->whereIn('id', str($request->input('ids'))->explode(',')->filter()->toArray()))
            ->when(!$request->user()->isAdmin(), fn($query) => $query->where('user_id', $request->user()->getUser()->id))
            ->when($request->user()->isAdmin() && $request->collect()->count() === 0, fn($query) => $query->where('user_id', $request->user()->id))
            ->whereNotNull('body')
            ->with(['site:id,name,categories', 'campaign:id,name'])
            ->orderBy('id', 'desc')
            ->limit(1000)
            ->cursor()
            ->each(function (Content $content) use ($csv, $isMedPosterApp) {
                $category = str($content->getMeta('category'))->explode(',')->map(fn($tag) => trim($tag))->filter()->map(function ($category) use ($content) {
                    if ($exisingCat = collect($content?->site?->categories ?: [])->where('id', $category)->first()) {
                        return $exisingCat['name'] ?? $category;
                    }

                    return $category;
                })->join(', ');

                $row = [
                    $content->id,
                    decodeHtmlEntity($content->title),
                    decodeHtmlEntity(preg_replace('/\s+/', ' ', $content->body)),
                    decodeHtmlEntity($content->description),
                    $isMedPosterApp ? $content->getMeta('medium_post_url')[0] ?? '' : $content->getMeta('wp_post_link'),
                    $content->getMeta('tags'),
                    $content->target,
                    $content->campaign_id,
                    $content->campaign->name ?? null,
                ];

                if (!$isMedPosterApp) {
                    $row[] = $category;
                }

                $csv->insertOne($row);
            });

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->streamDownload(function () use ($csv) {
            echo $csv->toString();
        }, $filename, $headers);
    }

    private function researchCampaignEntryRows($request, $exportType, $csv)
    {
        $filename = Str::slug(config('app.name') . '_export_' . $request->user()->name . '_' . now()->toDateTimeString() . '_' . uniqid(), '_') . '.' . $exportType;
        $txt = '';

        ResearchCampaignEntry::query()
            ->when($request->input('ids'), fn($query) => $query->whereIn('id', str($request->input('ids'))->explode(',')->filter()->toArray()))
            ->when(!$request->user()->isAdmin(), fn($query) => $query->whereHas('researchEbookCampaign', function ($query) use ($request) {
                $query->where('user_id', $request->user()->id);
            }))
            ->when($request->user()->isAdmin() && $request->collect()->count() === 0, fn($query) => $query->where('user_id', $request->user()->id))
            ->with('researchEbookCampaign')
            ->orderByDesc('id')
            ->limit(1000)
            ->lazyById()
            ->each(function (ResearchCampaignEntry $content) use ($exportType, $csv, &$txt) {
                if ($exportType == 'csv') {
                    $row = [
                        $content->id,
                        $content->researchEbookCampaign->name,
                        $content->entry,
                        $content->created_at,
                        $content->updated_at,
                    ];
                    $csv->insertOne($row);
                } else {
                    $txt .= $content->entry . PHP_EOL;
                }
            });

        $headers = [
            'Content-Type' => $exportType == 'csv' ? 'text/csv' : 'text/plain',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->streamDownload(function () use ($exportType, $csv, $txt) {
            echo $exportType == 'csv' ? $csv->toString() : $txt;
        }, $filename, $headers);
    }

    private function popularBooksInNicheEntryRows($request, $exportType, $csv)
    {
        $filename = Str::slug(config('app.name') . '_export_' . $request->user()->name . '_' . now()->toDateTimeString() . '_' . uniqid(), '_') . '.' . $exportType;
        $txt = '';

        PopularBookInNicheEntry::query()
            ->when($request->input('ids'), fn($query) => $query->whereIn('id', str($request->input('ids'))->explode(',')->filter()->toArray()))
            ->when(!$request->user()->isAdmin(), fn($query) => $query->whereHas('researchEbookCampaign', function ($query) use ($request) {
                $query->where('user_id', $request->user()->id);
            }))
            ->when($request->user()->isAdmin() && $request->collect()->count() === 0, fn($query) => $query->where('user_id', $request->user()->id))
            ->with('researchEbookCampaign')
            ->orderByDesc('id')
            ->limit(1000)
            ->lazyById()
            ->each(function (PopularBookInNicheEntry $content) use ($exportType, $csv, &$txt) {
                if ($exportType == 'csv') {
                    $row = [
                        $content->id,
                        $content->researchEbookCampaign->name,
                        $content->book_name,
                        $content->author,
                        $content->summary,
                        implode("\n", array_map(fn($i, $item) => ($i + 1) . ". " . $item, array_keys($content->key_takeways), $content->key_takeways)),

                        $content->created_at,
                        $content->updated_at,
                    ];
                    $csv->insertOne($row);
                } else {
                    $txt .= "ID: {$content->id}" . PHP_EOL;
                    $txt .= "Campaign: {$content->researchEbookCampaign->name}" . PHP_EOL;
                    $txt .= "Book Name: {$content->book_name}" . PHP_EOL;
                    $txt .= "Author: {$content->author}" . PHP_EOL;
                    $txt .= "Summary: {$content->summary}" . PHP_EOL;
                    $txt .= "Key Takeaways:" . PHP_EOL;
                    $txt .= implode(PHP_EOL, array_map(fn($i, $item) => ($i + 1) . ". " . $item, array_keys($content->key_takeways), $content->key_takeways)) . PHP_EOL;
                    $txt .= "Created At: {$content->created_at}" . PHP_EOL;
                    $txt .= "Updated At: {$content->updated_at}" . PHP_EOL;
                    $txt .= str_repeat("-", 50) . PHP_EOL; // Adds a separator between entries

                }
            });

        $headers = [
            'Content-Type' => $exportType == 'csv' ? 'text/csv' : 'text/plain',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->streamDownload(function () use ($exportType, $csv, $txt) {
            echo $exportType == 'csv' ? $csv->toString() : $txt;
        }, $filename, $headers);
    }
}
