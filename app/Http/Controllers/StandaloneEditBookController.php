<?php

namespace App\Http\Controllers;

use App\Models\Campaign;
use Illuminate\Http\Request;
use Illuminate\View\View;

class StandaloneEditBookController extends Controller
{
    /**
     * Display the standalone ebook editing page
     */
    public function show(Campaign $campaign): View
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            abort(403, 'Unauthorized access to this campaign.');
        }
        
        return view('standalone.edit-book', [
            'campaign' => $campaign,
        ]);
    }
}
