<?php

namespace App\Http\Controllers;

use App\Models\UserUpdatePreference;
use App\Service\UpdateNotificationService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class UserUpdatePreferenceController extends Controller
{
    public function __construct(
        private UpdateNotificationService $updateNotificationService
    ) {}

    /**
     * Show the user's update preferences
     */
    public function show(Request $request): View
    {
        $user = $request->user();
        $preferences = UserUpdatePreference::getOrCreateForUser($user);
        return view('update-preferences', compact('preferences', 'user'));
    }

    /**
     * Update the user's preferences
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'email_major_updates' => 'boolean',
            'email_minor_updates' => 'boolean',
            'email_patch_updates' => 'boolean',
            'email_security_updates' => 'boolean',
            'categories' => 'array',
            'categories.*' => 'string|in:features,fixes,improvements,security',
        ]);

        $user = $request->user();

        $this->updateNotificationService->updateUserPreferences($user, [
            'email_major_updates' => $request->boolean('email_major_updates'),
            'email_minor_updates' => $request->boolean('email_minor_updates'),
            'email_patch_updates' => $request->boolean('email_patch_updates'),
            'email_security_updates' => $request->boolean('email_security_updates'),
            'categories' => $request->input('categories', []),
        ]);

        return redirect()
            ->route('user.update-preferences')
            ->with('success', 'Your notification preferences have been updated successfully!');
    }
}
