<?php

namespace App\Http\Controllers;

use App\Models\Campaign;
use App\Models\CampaignReview;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    /**
     * Store a newly created review in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if the campaign exists first
        $campaign = Campaign::find($request->campaign_id);
        if (!$campaign) {
            return response()->json([
                'success' => false,
                'message' => 'Campaign not found'
            ], 404);
        }

        // Build dynamic validation rules based on campaign configuration
        $rules = [
            'campaign_id' => 'required|exists:campaigns,id',
        ];

        // Add validation rules for each field based on campaign configuration
        if ($campaign->shouldShowReviewField('name')) {
            $rules['name'] = $campaign->isReviewFieldRequired('name') ? 'required|string|max:255' : 'nullable|string|max:255';
        }

        if ($campaign->shouldShowReviewField('email')) {
            $rules['email'] = $campaign->isReviewFieldRequired('email') ? 'required|email|max:255' : 'nullable|email|max:255';
        }

        if ($campaign->shouldShowReviewField('rating')) {
            $rules['rating'] = $campaign->isReviewFieldRequired('rating') ? 'required|integer|min:1|max:5' : 'nullable|integer|min:1|max:5';
        }

        if ($campaign->shouldShowReviewField('comment')) {
            $rules['comment'] = $campaign->isReviewFieldRequired('comment') ? 'required|string' : 'nullable|string';
        }

        // Validate the request data with dynamic rules
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Prepare review data
        $reviewData = [
            'campaign_id' => $request->campaign_id,
        ];

        // Only include fields that are shown in the form
        if ($campaign->shouldShowReviewField('name')) {
            $reviewData['name'] = $request->name;
        }

        if ($campaign->shouldShowReviewField('email')) {
            $reviewData['email'] = $request->email;
        }

        if ($campaign->shouldShowReviewField('rating')) {
            $reviewData['rating'] = $request->rating;
        }

        if ($campaign->shouldShowReviewField('comment')) {
            $reviewData['comment'] = $request->comment;
        }

        // Create the review
        $review = CampaignReview::create($reviewData);

        return response()->json([
            'success' => true,
            'message' => 'Review submitted successfully',
            'data' => $review
        ], 201);
    }
}
