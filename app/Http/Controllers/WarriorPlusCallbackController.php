<?php

namespace App\Http\Controllers;

use App\Mail\WelcomeEmail;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Webhook;
use App\Service\WarriorPlus;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class WarriorPlusCallbackController extends Controller
{
    function ipn(Request $request)
    {
        if (!$request->all()) {
            return response('error', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        list($hook, $data) = $this->parseRequest($request);

        if ($request->input('WP_SECURITYKEY') !== config('services.warriorplus.security_key')) {
            $hook->update(['action' => 'Invalid security key']);
            return response('error', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $allowedActions = str($request->input('_only'))->explode(',')->filter()->toArray();

        if ($request->input('WP_ACTION') === 'subscr_created' && $request->input('WP_SUBSCR_PAYMENT_NUM') == 1) {
            $hook->update(['action' => "Skip action {$request->input('WP_ACTION')} #1. It will be handled by sale event"]);
            return response($hook->action, Response::HTTP_OK);
        }

        if (count($allowedActions) > 0 && !in_array($request->input('WP_ACTION'), $allowedActions)) {
            $hook->update(['action' => "Skip action {$request->input('WP_ACTION')}. Allow only {$request->input('_only')}"]);
            return response($hook->action, Response::HTTP_OK);
        }

        return match ($request->input('WP_ACTION')) {
            'sale', 'subscr_created', 'subscr_completed', 'subscr_reactivated' => $this->keygen($hook, $data),
            'refund', 'subscr_cancelled', 'subscr_failed_invalid', 'subscr_failed_declined', 'subscr_suspended', 'subscr_ended', 'subscr_refunded' => $this->refund($hook, $data),
            default => response('error', Response::HTTP_UNPROCESSABLE_ENTITY),
        };
    }

    function keygen($hook, $data)
    {
        $valitator = validator()->make($data, [
            'WP_ACTION' => 'required',
            'WP_ITEM_NUMBER' => 'required',
            'WP_ITEM_NAME' => 'required',
            'WP_SALE_AMOUNT' => 'required',
            'WP_SALE' => 'required',
            'WP_TXNID' => 'required',
            'WP_SALEID' => 'required',
            'WP_PAYMETHOD' => 'nullable',
            'WP_BUYER_NAME' => 'required',
            'WP_BUYER_EMAIL' => 'required',
        ]);

        if ($valitator->fails()) {
            $hook->update(['action' => 'Missing required data']);
            return response('error', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return (new WarriorPlus)->handleSale($data, $hook);
    }

    function refund($hook, $data)
    {
        $valitator = validator()->make($data, [
            'WP_ACTION' => 'required',
            'WP_SALEID' => 'required',
            'WP_SALE' => 'required',
        ]);

        if ($valitator->fails()) {
            $hook->update(['action' => 'Missing required data']);
            return response('error', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return (new WarriorPlus)->handleRefund($data, $hook);
    }

    /**
     * @param Request $request
     * @return array
     */
    private function parseRequest(Request $request): array
    {
        $hook = Webhook::create(['payload' => $request->all()]);

        $data = $request->all();

        return array($hook, $data); // https://warriorplus.com/support/knowledgebase.php?article=63
    }
}
