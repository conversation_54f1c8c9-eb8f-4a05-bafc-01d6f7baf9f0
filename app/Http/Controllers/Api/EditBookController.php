<?php

namespace App\Http\Controllers\Api;

use App\Actions\ReGenerateEbookAction;
use App\Http\Controllers\Controller;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Models\Section;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class EditBookController extends Controller
{
    /**
     * Get campaign data with chapters and sections
     */
    public function getCampaign(Campaign $campaign): JsonResponse
    {
        // Debug authentication
        \Log::info('EditBook API - getCampaign called', [
            'authenticated' => auth()->check(),
            'user_id' => auth()->id(),
            'campaign_id' => $campaign->id,
        ]);

        // Check if user is authenticated
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated.'], 401);
        }

        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        $campaign->load(['chapters.sections' => function ($query) {
            $query->orderBy('order');
        }]);

        $campaign->chapters->each(function ($chapter) {
            $chapter->makeHidden(['created_at', 'updated_at']);
            $chapter->sections->each(function ($section) {
                $section->makeHidden(['created_at', 'updated_at']);
            });
        });

        return response()->json([
            'campaign' => $campaign,
            'chapters' => $campaign->chapters->map(function ($chapter) {
                return [
                    'id' => $chapter->id,
                    'title' => $chapter->title,
                    'intro' => $chapter->intro,
                    'chapter_number' => $chapter->chapter_number,
                    'status' => $chapter->status,
                    'sections' => $chapter->sections->map(function ($section) {
                        return [
                            'id' => $section->id,
                            'title' => $section->title,
                            'intro' => $section->intro,
                            'body' => $section->body,
                            'order' => $section->order,
                            'status' => $section->status,
                        ];
                    }),
                ];
            }),
        ]);
    }

    /**
     * Add new chapter to campaign
     */
    public function addChapter(Request $request, Campaign $campaign): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        try {
            // Get the highest chapter number
            $maxChapterNumber = $campaign->chapters()->max('chapter_number') ?? 0;

            $chapter = Chapter::create([
                'campaign_id' => $campaign->id,
                'title' => 'New Chapter',
                'intro' => '',
                'chapter_number' => $maxChapterNumber + 1,
                'status' => \App\Enum\CampaignStatusEnum::PENDING,
                'chapter_total_words' => 0,
            ]);

            return response()->json([
                'message' => 'New chapter added',
                'chapter' => [
                    'id' => $chapter->id,
                    'title' => $chapter->title,
                    'intro' => $chapter->intro,
                    'chapter_number' => $chapter->chapter_number,
                    'status' => $chapter->status,
                    'sections' => [],
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding chapter: ' . $e->getMessage());
            return response()->json(['error' => 'Error adding chapter'], 500);
        }
    }

    /**
     * Update chapter
     */
    public function updateChapter(Request $request, Campaign $campaign, Chapter $chapter): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        // Verify chapter belongs to campaign
        if ($chapter->campaign_id !== $campaign->id) {
            return response()->json(['error' => 'Chapter not found.'], 404);
        }

        $request->validate([
            'title' => 'required|string',
            'intro' => 'nullable|string',
        ]);

        try {
            $chapter->update([
                'title' => $request->title,
                'intro' => $request->intro,
            ]);

            return response()->json([
                'message' => 'Chapter saved successfully',
                'chapter' => [
                    'id' => $chapter->id,
                    'title' => $chapter->title,
                    'intro' => $chapter->intro,
                    'chapter_number' => $chapter->chapter_number,
                    'status' => $chapter->status,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving chapter: ' . $e->getMessage());
            return response()->json(['error' => 'Error saving chapter'], 500);
        }
    }

    /**
     * Delete chapter
     */
    public function deleteChapter(Campaign $campaign, Chapter $chapter): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        // Verify chapter belongs to campaign
        if ($chapter->campaign_id !== $campaign->id) {
            return response()->json(['error' => 'Chapter not found.'], 404);
        }

        try {
            // Delete the chapter (sections will be deleted via cascade)
            $chapter->delete();

            return response()->json(['message' => 'Chapter deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting chapter: ' . $e->getMessage());
            return response()->json(['error' => 'Error deleting chapter'], 500);
        }
    }

    /**
     * Update section
     */
    public function updateSection(Request $request, Campaign $campaign, Section $section): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        // Verify section belongs to campaign
        if ($section->chapter->campaign_id !== $campaign->id) {
            return response()->json(['error' => 'Section not found.'], 404);
        }

        $request->validate([
            'title' => 'required|string',
            'intro' => 'nullable|string',
            'body' => 'nullable|string',
        ]);

        try {
            $section->update([
                'title' => $request->title,
                'intro' => $request->intro,
                'body' => $request->body,
            ]);

            return response()->json([
                'message' => 'Section saved successfully',
                'section' => [
                    'id' => $section->id,
                    'title' => $section->title,
                    'intro' => $section->intro,
                    'body' => $section->body,
                    'order' => $section->order,
                    'status' => $section->status,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving section: ' . $e->getMessage());
            return response()->json(['error' => 'Error saving section'], 500);
        }
    }

    /**
     * Add new section to chapter
     */
    public function addSection(Request $request, Campaign $campaign, Chapter $chapter): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        // Verify chapter belongs to campaign
        if ($chapter->campaign_id !== $campaign->id) {
            return response()->json(['error' => 'Chapter not found.'], 404);
        }

        try {
            // Get the highest order number for sections in this chapter
            $maxOrder = $chapter->sections()->max('order') ?? 0;

            $section = Section::create([
                'chapter_id' => $chapter->id,
                'title' => 'New Section',
                'intro' => '',
                'body' => '',
                'order' => $maxOrder + 1,
                'status' => \App\Enum\CampaignStatusEnum::PENDING,
            ]);

            return response()->json([
                'message' => 'New section added',
                'section' => [
                    'id' => $section->id,
                    'title' => $section->title,
                    'intro' => $section->intro,
                    'body' => $section->body,
                    'order' => $section->order,
                    'status' => $section->status,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding section: ' . $e->getMessage());
            return response()->json(['error' => 'Error adding section'], 500);
        }
    }

    /**
     * Delete section
     */
    public function deleteSection(Campaign $campaign, Section $section): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        // Verify section belongs to campaign
        if ($section->chapter->campaign_id !== $campaign->id) {
            return response()->json(['error' => 'Section not found.'], 404);
        }

        try {
            $section->delete();
            return response()->json(['message' => 'Section deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting section: ' . $e->getMessage());
            return response()->json(['error' => 'Error deleting section'], 500);
        }
    }

    /**
     * Reorder sections within a chapter
     */
    public function reorderSections(Request $request, Campaign $campaign): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        $request->validate([
            'sections' => 'required|array',
            'sections.*.id' => 'required|integer|exists:sections,id',
            'sections.*.order' => 'required|integer|min:1',
        ]);

        try {
            foreach ($request->sections as $sectionData) {
                $section = Section::find($sectionData['id']);

                // Verify section belongs to campaign
                if ($section->chapter->campaign_id !== $campaign->id) {
                    continue;
                }

                $section->update(['order' => $sectionData['order']]);
            }

            return response()->json(['message' => 'Sections reordered successfully']);
        } catch (\Exception $e) {
            Log::error('Error reordering sections: ' . $e->getMessage());
            return response()->json(['error' => 'Error reordering sections'], 500);
        }
    }

    /**
     * Reorder chapters
     */
    public function reorderChapters(Request $request, Campaign $campaign): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        $request->validate([
            'chapters' => 'required|array',
            'chapters.*.id' => 'required|integer|exists:chapters,id',
            'chapters.*.order' => 'required|integer|min:1',
        ]);

        try {
            foreach ($request->chapters as $chapterData) {
                $chapter = Chapter::find($chapterData['id']);

                // Verify chapter belongs to campaign
                if ($chapter->campaign_id !== $campaign->id) {
                    continue;
                }

                $chapter->update(['chapter_number' => $chapterData['order']]);
            }

            return response()->json(['message' => 'Chapters reordered successfully']);
        } catch (\Exception $e) {
            Log::error('Error reordering chapters: ' . $e->getMessage());
            return response()->json(['error' => 'Error reordering chapters'], 500);
        }
    }

    /**
     * Regenerate ebook for campaign
     */
    public function regenerateEbook(Request $request, Campaign $campaign): JsonResponse
    {
        // Authorize user can edit this campaign
        if (auth()->user()->cannot('update', $campaign)) {
            return response()->json(['error' => 'Unauthorized access to this campaign.'], 403);
        }

        try {
            ReGenerateEbookAction::regenerateAction($campaign);
            return response()->json(['message' => 'Ebook regeneration started successfully']);
        } catch (\Exception $e) {
            Log::error('Error regenerating ebook: ' . $e->getMessage());
            return response()->json(['error' => 'Error regenerating ebook'], 500);
        }
    }
}
