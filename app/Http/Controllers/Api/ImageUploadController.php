<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ImageUploadController extends Controller
{
    /**
     * Upload an image file to S3 and return the URL
     */
    public function uploadImage(Request $request): JsonResponse
    {
        // Check authentication
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'image' => [
                'required',
                'file',
                'image',
                'mimes:jpg,jpeg,png,gif,webp,bmp',
                'max:10240' // 10MB max
            ]
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Invalid image file.',
                'details' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('image');
            
            // Generate unique filename
            $filename = 'campaign_images/'.date('Y-m-d') . '/' . Str::uuid() . '.' . $file->getClientOriginalExtension();
            
            // Upload to S3
            $uploadSuccess = Storage::disk('s3')->put($filename, file_get_contents($file), 'public');
            
            if (!$uploadSuccess) {
                throw new \Exception('Failed to upload image to S3');
            }
            
            // Get the public URL
            $url = Storage::disk('s3')->url($filename);
            
            return response()->json([
                'success' => true,
                'url' => $url,
                'filename' => $filename
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Image upload failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'file_size' => $request->file('image')?->getSize(),
                'file_type' => $request->file('image')?->getMimeType()
            ]);
            
            return response()->json([
                'error' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }
}
