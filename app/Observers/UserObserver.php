<?php

namespace App\Observers;

use App\Models\User;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        try {
            $user->log("Submitting user email {$user->email} to ConvertKit");
            $api = new \ConvertKit_API\ConvertKit_API(config('services.convertkit.api'), config('services.convertkit.secret'));
            $api->tag_subscriber(config('services.convertkit.tag_id'), $user->email, $user->name);
            $user->log("Submitted user email {$user->email} to ConvertKit");
        } catch (\Throwable $e) {
            $user->log("ConvertKit API error : " . $e->getMessage());
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }
}
