<?php

namespace App\Observers;

use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\CreditLogActionEnum;
use App\Models\Download;
use App\Models\Section;
use App\Models\Campaign;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class DownloadObserver
{
    /**
     * Handle the Download "created" event.
     */
    public function created(Download $download): void
    {
        $campaign = $download->campaign;
        $user = $download->user;

        $campaign->log("Deducting credit start");

        $downloadTypes = $campaign->downloads()
                                  ->whereIn('type', ['pdf', 'epub']) // Replace with Enum if available
                                  ->select('type')
                                  ->distinct()
                                  ->pluck('type')
                                  ->all();

        $hasPdfAndEpub = in_array('pdf', $downloadTypes) && in_array('epub', $downloadTypes);

        $campaign->log("{$hasPdfAndEpub}, {$campaign->charged}"
        );

        // When NOT charged and both types exist
        if (
            !$campaign->charged &&
            $hasPdfAndEpub
        ) {
            $campaign->log("Campaign is done and charged is false. Updating credit use and charged to true.");

            $campaign->update(['charged' => true]);

            // Clean meta errors after charging
            $this->cleanCampaignMetaErrors($campaign);

            $requiredCredits = $campaign->requiredCredits();

            if ($user) {
                $user->logCreditActivity(
                    user: $user,
                    action: CreditLogActionEnum::CAMPAIGN,
                    credit: $requiredCredits,
                    description: "{$requiredCredits} Credit deduct for ebook campaign: {$campaign->id}",
                );

                $user->useCredit($requiredCredits);
            }
        }
        // If already charged, only deduct ebook generation credit
        elseif ($campaign->charged && $user && in_array($download->type, ['pdf', 'epub'])) {
            $credit = CreditDeductEnum::EBOOK_GENERATION_CREDIT->value / 2;

            $user->logCreditActivity(
                user: $user,
                action: CreditLogActionEnum::CAMPAIGN,
                credit: $credit,
                description: "{$credit} Credit deduct for {$download->type} ebook regeneration (campaign already charged): {$campaign->id}",
            );

            $user->useCredit($credit);
        }
        elseif ($campaign->charged && $user && in_array($download->type, ['mp3'])) {
            $credit = CreditDeductEnum::AUDIO_GENERATION_CREDIT->value;

            $user->logCreditActivity(
                user: $user,
                action: CreditLogActionEnum::CAMPAIGN,
                credit: $credit,
                description: "{$credit} Credit deduct for audio generation (campaign already charged): {$campaign->id}",
            );

            $user->useCredit($credit);
        }

        if (!$campaign->getMeta('total_word_length_updated')) {
            $total = Section::where('campaign_id', $campaign->id)->sum('section_total_words');
            Campaign::where('id', $campaign->id)->update(
                [
                    'total_word_length' => $total,
                    'meta->total_word_length_updated' => true,
                ]
            );
        }
    }

    /**
     * Handle the Download "updated" event.
     */
    public function updated(Download $download): void
    {
        //
    }

    /**
     * Handle the Download "deleting" event.
     * This runs before the download record is deleted from the database.
     *
     * Note: This handles individual download deletions. For campaign cascade deletions,
     * the S3 cleanup is handled in CampaignObserver::deleting() method.
     */
    public function deleting(Download $download): void
    {
        // Only attempt S3 deletion if there's a path
        if (empty($download->path)) {
            return;
        }

        try {
            // Check if file exists in S3 before attempting deletion
            if (Storage::disk('s3')->exists($download->path)) {
                // Delete the file from S3
                Storage::disk('s3')->delete($download->path);

                // Log successful deletion to campaign log
                if ($download->campaign) {
                    $download->campaign->log("Successfully deleted S3 file: " . $download->path);
                }

            } else {
                // File doesn't exist in S3, log this information
                if ($download->campaign) {
                    $download->campaign->log("S3 file not found (may have been already deleted): " . $download->path);
                }
            }
        } catch (\Exception $e) {
            // Log the error but don't prevent database deletion
            if ($download->campaign) {
                $download->campaign->log("Failed to delete S3 file: " . $download->path . " - Error: " . $e->getMessage());
            }
        }
    }

    /**
     * Handle the Download "deleted" event.
     */
    public function deleted(Download $download): void
    {
        //
    }

    /**
     * Handle the Download "restored" event.
     */
    public function restored(Download $download): void
    {
        //
    }

    /**
     * Handle the Download "force deleted" event.
     */
    public function forceDeleted(Download $download): void
    {
        //
    }

    /**
     * Clean meta errors from campaign after charging
     */
    private function cleanCampaignMetaErrors(Campaign $campaign): void
    {
        $campaign->log("Cleaning meta errors after campaign charged.");

        // Get current meta data
        $meta = $campaign->meta ?? [];

        // Remove error-related keys from meta
        $errorKeys = [
            'errors',
            'error',
            'error_message',
        ];

        $cleaned = false;
        foreach ($errorKeys as $key) {
            if (isset($meta[$key])) {
                unset($meta[$key]);
                $cleaned = true;
            }
        }

        // Update campaign meta if any errors were cleaned
        if ($cleaned) {
            $campaign->update(['meta' => $meta]);
            $campaign->log("Meta errors cleaned successfully.");
        } else {
            $campaign->log("No meta errors found to clean.");
        }
    }
}
