<?php

namespace App\Observers;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\Chapter;

class ChapterObserver
{
    /**
     * Handle the Chapter "created" event.
     */
    public function created(Chapter $chapter): void
    {
        //
    }

    /**
     * Handle the Chapter "updated" event.
     */
    public function updated(Chapter $chapter): void
    {
        if($chapter->campaign->status == CampaignStatusEnum::AUDIO_PROCESSING){
            $chapter->campaign->log("Campaign is in audio processing status. Skipping chapter update.");
            return;
        }
        if ($this->isChapterCompleted($chapter)) {
            $campaign = $chapter->campaign;
            $campaign->log("Chapter is completed.");
            $campaign->log("Current campaign status: " . $campaign->status->value);

            if ($this->areAllChaptersCompleted($campaign)) {
                $campaign->log("All chapters are completed. Updating campaign status");
                $this->updateCampaignStatus($campaign);
            }
        }
    }

    /**
     * Handle the Chapter "deleting" event.
     * Soft delete all sections when chapter is being deleted.
     */
    public function deleting(Chapter $chapter): void
    {
        try {
            // Debug: Count sections before deletion
            $sectionCountBefore = $chapter->sections()->count();
            $chapter->campaign->log("ChapterObserver::deleting triggered for chapter {$chapter->id}");
            $chapter->campaign->log("Chapter {$chapter->id} - Before soft delete: {$sectionCountBefore} sections");

            // Soft delete all sections for this chapter
            $deletedSections = $chapter->sections()->delete();

            // Debug: Count sections after deletion
            $sectionCountAfter = $chapter->sections()->withTrashed()->count();
            $sectionCountAfterDirect = \App\Models\Section::withTrashed()->where('chapter_id', $chapter->id)->count();

            $chapter->campaign->log("Chapter {$chapter->id} - Section delete() returned: {$deletedSections}");
            $chapter->campaign->log("Chapter {$chapter->id} - After soft delete (withTrashed): {$sectionCountAfter} sections");
            $chapter->campaign->log("Chapter {$chapter->id} - After soft delete (direct query): {$sectionCountAfterDirect} sections");

        } catch (\Exception $e) {
            $chapter->campaign->log("ChapterObserver::deleting error for chapter {$chapter->id}: " . $e->getMessage());
        }
    }

    /**
     * Handle the Chapter "deleted" event.
     */
    public function deleted(Chapter $chapter): void
    {
        //
    }

    /**
     * Handle the Chapter "restored" event.
     */
    public function restored(Chapter $chapter): void
    {
        //
    }

    /**
     * Handle the Chapter "force deleted" event.
     */
    public function forceDeleted(Chapter $chapter): void
    {
        //
    }

    private function isChapterCompleted(Chapter $chapter): bool
    {
        return $chapter->status === CampaignStatusEnum::DONE;
    }

    private function areAllChaptersCompleted(Campaign $campaign): bool
    {
        return $campaign->chapters()
                        ->where('status', '!=', CampaignStatusEnum::DONE)
                        ->doesntExist();
    }

    private function updateCampaignStatus(Campaign $campaign): void
    {
        $newStatus = $campaign->image()
            ? CampaignStatusEnum::IMAGE_PROCESSING
            : CampaignStatusEnum::DONE;

        $campaign->update(['status' => $newStatus]);

        //session()->forget("campaign_ebook_plan_" . $campaign->id);
    }
}
