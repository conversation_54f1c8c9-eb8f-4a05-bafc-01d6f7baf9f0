<?php

namespace App\Observers;

use App\Actions\RunResearchEbookCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Models\ResearchEbookCampaign;

class ResearchEbookCampaignObserver
{
    protected RunResearchEbookCampaignAction $runResearchEbookCampaignAction;

    public function __construct(RunResearchEbookCampaignAction $runResearchEbookCampaignAction)
    {
        $this->runResearchEbookCampaignAction = $runResearchEbookCampaignAction;
    }
    /**
     * Handle the ResearchEbookCampaign "created" event.
     */
    public function created(ResearchEbookCampaign $researchEbookCampaign): void
    {
        if ($researchEbookCampaign->status->value == CampaignStatusEnum::IN_PROGRESS->value ||
            $researchEbookCampaign->status->value == CampaignStatusEnum::PENDING->value){
            $researchEbookCampaign->log("Starting research ebook campaign. Type: " . $researchEbookCampaign->type->getLabel());
            $this->runResearchEbookCampaignAction->execute($researchEbookCampaign);
        }
    }

    /**
     * Handle the ResearchEbookCampaign "updated" event.
     */
    public function updated(ResearchEbookCampaign $researchEbookCampaign): void
    {
        //
    }

    /**
     * Handle the ResearchEbookCampaign "deleted" event.
     */
    public function deleted(ResearchEbookCampaign $researchEbookCampaign): void
    {
        //
    }

    /**
     * Handle the ResearchEbookCampaign "restored" event.
     */
    public function restored(ResearchEbookCampaign $researchEbookCampaign): void
    {
        //
    }

    /**
     * Handle the ResearchEbookCampaign "force deleted" event.
     */
    public function forceDeleted(ResearchEbookCampaign $researchEbookCampaign): void
    {
        //
    }
}
