<?php

namespace App\Observers;

use App\Enum\CreditLogActionEnum;
use App\Models\User;
use App\Models\Webhook;

class UpdateUserCreditWebhookObserver
{
    /**
     * Handle the Webhook "created" event.
     */
    public function created(Webhook $webhook): void
    {
        if ($webhook->user_id){
            $user = User::findOrFail($webhook->user_id);
            $this->updateUserCredit($user, $webhook);
        }
    }

    /**
     * Handle the Webhook "updated" event.
     */
    public function updated(Webhook $webhook): void
    {
        //
    }

    /**
     * Handle the Webhook "deleted" event.
     */
    public function deleted(Webhook $webhook): void
    {
        //
    }

    /**
     * Handle the Webhook "restored" event.
     */
    public function restored(Webhook $webhook): void
    {
        //
    }

    /**
     * Handle the Webhook "force deleted" event.
     */
    public function forceDeleted(Webhook $webhook): void
    {
        //
    }

    private function updateUserCredit($user, $webhook)
    {

        if ($user) {
            $user->logCreditActivity($user, $this->getLogType($webhook->status), abs($webhook->amount));
            $user->credits += $webhook->amount;
            $user->save();
        }
    }

    private function getLogType($key)
    {
        $arr = [
            'complete' => CreditLogActionEnum::BILLING,
            'refunded' => CreditLogActionEnum::GATEWAY_REFUND,
        ];

        return array_key_exists("$key", $arr) ? $arr[$key] : CreditLogActionEnum::BILLING;
    }
}
