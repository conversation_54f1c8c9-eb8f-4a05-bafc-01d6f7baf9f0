<?php

namespace App\Observers;

use App\Actions\RunCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\CreditLogActionEnum;
use App\Jobs\DispatchChapterSectionsJob;
use App\Jobs\GenerateCoverImage;
use App\Jobs\GenerateEbookChaptersJob;
use App\Jobs\GenerateEbookJob;
use App\Jobs\GenerateEbookTitleJob;
use App\Jobs\ImageProcessingJob;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Service\Contracts\CampaignImageProcessingServiceInterface;
use App\Traits\HasCreditLog;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CampaignObserver
{
    use HasCreditLog;
    protected RunCampaignAction $runCampaignAction;
    protected CampaignImageProcessingServiceInterface $imageProcessingService;
    public function __construct(
        RunCampaignAction $runCampaignAction,
        CampaignImageProcessingServiceInterface $imageProcessingService
    )
    {
        $this->runCampaignAction = $runCampaignAction;
        $this->imageProcessingService = $imageProcessingService;
    }
    /**
     * Handle the Campaign "created" event.
     */
    public function created(Campaign $campaign): void
    {
        if ($campaign->status->value == CampaignStatusEnum::IN_PROGRESS->value ||
            $campaign->status->value == CampaignStatusEnum::PENDING->value){
            $campaign->log("Starting ebook campaign");
            $this->runCampaignAction->execute($campaign);
        }
    }

    /**
     * Handle the Campaign "updated" event.
     */
    public function updated(Campaign $campaign): void
    {
        if ($campaign->wasChanged('status') && $campaign->status == CampaignStatusEnum::IMAGE_PROCESSING) {
            $campaign->log("Image processing for ebook campaign starting.");
            $this->imageProcessingService->processImagesForCampaign($campaign);
        }

        if($campaign->status == CampaignStatusEnum::DONE && (
            $campaign->getOriginal('status') == CampaignStatusEnum::IN_PROGRESS ||
           $campaign->getOriginal('status') == CampaignStatusEnum::IMAGE_PROCESSING)
        ){
            $campaign->log("Starting ebook generation from observer");

            if($campaign->getForm("generate_cover_image") == 'ai_generate'){
                GenerateCoverImage::dispatch($campaign->id);
            }
            GenerateEbookJob::dispatch($campaign->id);
        }
    }

    /**
     * Handle the Campaign "deleting" event.
     * This runs before the campaign is soft deleted.
     * We now use soft deletes, so no immediate file cleanup is needed.
     */
    public function deleting(Campaign $campaign): void
    {
        $campaign->log("Campaign is being soft deleted. Related records will also be soft deleted.");

        try {
            // Debug: Count records before deletion
            $downloadCount = $campaign->downloads()->count();
            $chapterCount = $campaign->chapters()->count();
            $sectionCount = $campaign->sections()->count();
            $reviewCount = $campaign->reviews()->count();

            $campaign->log("Before soft delete - Downloads: {$downloadCount}, Chapters: {$chapterCount}, Sections: {$sectionCount}, Reviews: {$reviewCount}");

            // Soft delete all related records
            $campaign->downloads()->delete();

            // Delete chapters individually to trigger ChapterObserver for each chapter
            $campaign->chapters->each(function ($chapter) {
                $chapter->delete(); // This triggers ChapterObserver::deleting()
            });

            $campaign->reviews()->delete();

            // Debug: Count records after deletion using direct queries
            $downloadCountAfter = $campaign->downloads()->withTrashed()->count();
            $chapterCountAfter = $campaign->chapters()->withTrashed()->count();

            // Check sections using direct query (not HasManyThrough)
            $chapterIds = Chapter::withTrashed()->where('campaign_id', $campaign->id)->pluck('id')->toArray();
            $sectionCountAfter = \App\Models\Section::withTrashed()->whereIn('chapter_id', $chapterIds)->count();
            $sectionCountAfterRelationship = $campaign->sections()->withTrashed()->count();

            $reviewCountAfter = $campaign->reviews()->withTrashed()->count();

            $campaign->log("After soft delete (withTrashed) - Downloads: {$downloadCountAfter}, Chapters: {$chapterCountAfter}, Sections (direct): {$sectionCountAfter}, Sections (relationship): {$sectionCountAfterRelationship}, Reviews: {$reviewCountAfter}");

            $campaign->log("All related records have been soft deleted.");

        } catch (\Exception $e) {
            $campaign->log("Error during soft delete of related records: " . $e->getMessage());
        }
    }

    /**
     * Handle the Campaign "deleted" event.
     */
    public function deleted(Campaign $campaign): void
    {
        //
    }

    /**
     * Handle the Campaign "restored" event.
     */
    public function restored(Campaign $campaign): void
    {
        //
    }

    /**
     * Handle the Campaign "force deleted" event.
     */
    public function forceDeleted(Campaign $campaign): void
    {
        //
    }
}
