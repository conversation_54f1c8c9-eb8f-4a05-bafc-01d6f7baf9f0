<?php

namespace App\Observers;

use App\Enum\CampaignStatusEnum;
use App\Jobs\GenerateSectionKeyPointContentJob;
use App\Models\Section;

class SectionObserver
{
    /**
     * Handle the Section "created" event.
     */
    public function created(Section $section): void
    {
        $chapter = $section->chapter;
        $section->chapter->campaign->log("Generating content for section key points starting job for section: {$section->id}");
        if(!$section || !$chapter){
            $section->chapter->campaign->log("Section or chapter missing. Section: {$section} - Chapter: {$chapter}");
        }

        GenerateSectionKeyPointContentJob::dispatch($chapter, $section)->onQueue($section->getSectionQueueName());
    }

    /**
     * Handle the Section "updated" event.
     */
    public function updated(Section $section): void
    {
        if ($section->status === CampaignStatusEnum::DONE) {
            $section->chapter->campaign->log("Section is completed");
            $chapter = $section->chapter;
            if ($chapter->sections()->where('status', '!=', CampaignStatusEnum::DONE)->doesntExist()) {
                $chapter->campaign->log("Chapter is completed");
                $chapter->update(['status' => CampaignStatusEnum::DONE]);
            }
        }
    }

    /**
     * Handle the Section "deleted" event.
     */
    public function deleted(Section $section): void
    {
        //
    }

    /**
     * Handle the Section "restored" event.
     */
    public function restored(Section $section): void
    {
        //
    }

    /**
     * Handle the Section "force deleted" event.
     */
    public function forceDeleted(Section $section): void
    {
        //
    }
}
