{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@alpinejs/focus": "^3.10.5", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10", "@vitejs/plugin-vue": "^4.5.0", "@vue/compiler-sfc": "^3.3.8", "alpinejs": "^3.0.6", "autoprefixer": "^10.4.16", "axios": "^1.1.2", "laravel-vite-plugin": "^0.7.2", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "vite": "^4.0.0"}, "dependencies": {"@tiptap/core": "^2.23.0", "@tiptap/extension-bold": "^2.23.0", "@tiptap/extension-bullet-list": "^2.23.0", "@tiptap/extension-document": "^2.23.0", "@tiptap/extension-hard-break": "^2.23.0", "@tiptap/extension-heading": "^2.23.0", "@tiptap/extension-history": "^2.23.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-italic": "^2.23.0", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-list-item": "^2.23.0", "@tiptap/extension-ordered-list": "^2.23.0", "@tiptap/extension-paragraph": "^2.23.0", "@tiptap/extension-strike": "^2.23.0", "@tiptap/extension-text": "^2.23.0", "@tiptap/extension-underline": "^2.23.0", "@tiptap/pm": "^2.23.0", "@tiptap/vue-3": "^2.23.0", "sortablejs": "^1.15.0", "vue": "^3.3.8", "vuedraggable": "^4.1.0"}}