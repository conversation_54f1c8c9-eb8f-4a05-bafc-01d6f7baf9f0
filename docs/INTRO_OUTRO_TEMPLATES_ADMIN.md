# Intro/Outro Templates Admin Interface

## Overview

The **Intro/Outro Templates** admin page provides a comprehensive interface for managing dynamic audio templates across multiple languages. This page allows administrators to edit, add, and organize templates used for chapter introductions and conclusions in audiobook generation.

## Access

**Navigation:** Admin → Audio Templates  
**URL:** `/admin/intro-outro-templates`  
**Permission:** Admin users only  
**Icon:** Microphone icon

## Features

### 🌍 Multi-Language Support

- **14 Languages Supported**: English, Spanish, French, German, Italian, Portuguese, Russian, Chinese, Japanese, Korean, Arabic, Hindi, Turkish, Dutch
- **Tabbed Interface**: Each language has its own tab for easy navigation
- **Language-Specific Templates**: Manage intro/outro templates per language

### 📝 Template Management

#### **Intro Templates**
- **Purpose**: Chapter opening voiceovers
- **Placeholders Available**:
  - `{chapter_number}` - Chapter number
  - `{chapter_title}` - Chapter title
  - `{book_title}` - Book title
  - `{total_chapters}` - Total number of chapters

#### **Outro Templates**
- **Purpose**: Chapter closing voiceovers
- **Additional Placeholders**:
  - `{next_chapter_number}` - Next chapter number
  - All intro placeholders also available

### 🔧 Interface Features

#### **Repeater Fields**
- **Dynamic Addition**: Add/remove templates as needed
- **Collapsible Items**: Templates collapse with preview text
- **Minimum/Maximum**: 1-10 templates per type per language
- **Item Labels**: Show first 50 characters of template

#### **Template Guidelines**
- **Length Recommendation**: 50-150 characters
- **Professional Tone**: Maintain consistent style
- **Cultural Appropriateness**: Consider language-specific phrasing
- **Placeholder Usage**: Use dynamic placeholders for flexibility

## Usage Instructions

### 1. Accessing the Page

1. **Login** as an admin user
2. **Navigate** to Admin → Audio Templates
3. **Select Language Tab** for the language you want to edit

### 2. Editing Templates

#### **Adding New Templates**
1. **Click** "Add Intro Template" or "Add Outro Template"
2. **Enter** template text with placeholders
3. **Use** helper text for placeholder reference
4. **Save** changes

#### **Editing Existing Templates**
1. **Expand** template item by clicking on it
2. **Modify** template text in textarea
3. **Use** placeholders for dynamic content
4. **Collapse** item when done

#### **Removing Templates**
1. **Expand** template item
2. **Click** remove button (trash icon)
3. **Confirm** removal

### 3. Template Examples

#### **English Intro Templates**
```
Welcome to Chapter {chapter_number} of {book_title}. In this chapter, we'll explore {chapter_title}.
Chapter {chapter_number}: {chapter_title}. Let's dive into this important topic.
You're listening to Chapter {chapter_number} of {book_title}. Today we're covering {chapter_title}.
```

#### **Spanish Intro Templates**
```
Bienvenidos al Capítulo {chapter_number} de {book_title}. En este capítulo, exploraremos {chapter_title}.
Capítulo {chapter_number}: {chapter_title}. Profundicemos en este tema importante.
Están escuchando el Capítulo {chapter_number} de {book_title}. Hoy cubriremos {chapter_title}.
```

### 4. Saving Changes

1. **Review** all changes across languages
2. **Click** "Update Templates" button
3. **Confirm** success notification
4. **Automatic Backup** created before update

## Technical Details

### File Storage
- **Location**: `resources/prompts/intro-outro-templates.json`
- **Format**: JSON with language codes as keys
- **Encoding**: UTF-8 with Unicode support
- **Backup**: Automatic timestamped backups created

### API Access
- **All Templates**: `GET /api/intro-outro-templates`
- **Language-Specific**: `GET /api/intro-outro-templates/{language}`
- **Format**: JSON response
- **Caching**: 1-hour cache for performance

### Integration Points
- **Audio Generation**: Templates used in `GenerateChapterWiseAudioJob`
- **Campaign Configuration**: Language detection from campaign settings
- **Template Selection**: Random or specific template choice in audio form

## Best Practices

### 📝 Template Writing

1. **Keep It Concise**: 50-150 characters recommended
2. **Use Placeholders**: Always include dynamic content
3. **Maintain Consistency**: Similar tone within language
4. **Test Variations**: Consider different book/chapter titles
5. **Cultural Sensitivity**: Appropriate phrasing for each language

### 🔧 Management

1. **Regular Backups**: System creates automatic backups
2. **Test Changes**: Preview templates before finalizing
3. **Language Consistency**: Maintain similar template count per language
4. **Quality Control**: Review templates for grammar and flow

### 🚀 Performance

1. **Template Caching**: Changes clear cache automatically
2. **Efficient Loading**: Templates loaded once and cached
3. **API Optimization**: Cached responses for external access

## Troubleshooting

### Common Issues

#### **Templates Not Appearing**
- **Check**: Language code mapping in campaign
- **Verify**: Template file permissions
- **Clear**: Application cache

#### **Placeholders Not Working**
- **Ensure**: Correct placeholder syntax `{placeholder_name}`
- **Check**: Spelling of placeholder names
- **Verify**: Template service integration

#### **Form Not Saving**
- **Confirm**: Admin permissions
- **Check**: File write permissions
- **Verify**: JSON syntax validity

### Error Messages

- **"Failed to update templates"**: Check file permissions
- **"Templates updated successfully"**: Changes saved correctly
- **Form validation errors**: Check required fields

## Security

- **Admin Only**: Page restricted to admin users
- **Authentication**: Requires valid admin session
- **File Protection**: Templates stored in protected directory
- **Backup System**: Automatic backups prevent data loss

## Future Enhancements

### Planned Features
- **Template Preview**: Live preview with sample data
- **Import/Export**: Bulk template management
- **Version Control**: Template change history
- **Template Analytics**: Usage statistics per template

### Potential Additions
- **Custom Placeholders**: User-defined variables
- **Template Categories**: Organize by genre/style
- **Multi-User Editing**: Collaborative template management
- **Template Validation**: Automated quality checks

## Conclusion

The Intro/Outro Templates admin interface provides a powerful, user-friendly way to manage dynamic audio templates across multiple languages. With its intuitive design, comprehensive features, and robust backup system, administrators can easily maintain high-quality, culturally appropriate templates for professional audiobook generation.
