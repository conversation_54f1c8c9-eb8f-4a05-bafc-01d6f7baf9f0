# EditBook Vue.js Implementation

This document outlines the conversion of the FilamentPHP EditBook page from Livewire to Vue.js 3.

## Overview

The EditBook page has been successfully converted to use Vue.js 3 with the following key features:
- Page-specific Vue.js loading (only loads on EditBook page)
- Component-based architecture
- Rich text editing with TipTap
- Drag & drop functionality for reordering
- Real-time unsaved changes tracking
- API-based backend communication
- CSRF protection and authentication

## Architecture

### Frontend Components

1. **EditBookApp.vue** - Main application component
   - Manages overall state and data flow
   - Handles API communication
   - Coordinates between child components

2. **BookStructureSidebar.vue** - Chapter/section navigation
   - Displays hierarchical book structure
   - Drag & drop reordering for chapters and sections
   - Add/delete functionality for sections

3. **ChapterEditor.vue** - Chapter editing interface
   - Rich text editor for chapter title and introduction
   - Form validation and saving
   - Unsaved changes tracking

4. **SectionEditor.vue** - Section editing interface
   - Rich text editor for section title and content
   - Form validation and saving
   - Status display

5. **RichTextEditor.vue** - WYSIWYG editor component
   - Built with TipTap editor
   - Toolbar with formatting options
   - HTML content handling

6. **UnsavedChangesIndicator.vue** - Visual indicator for unsaved changes
7. **LoadingOverlay.vue** - Loading state overlay

### Backend API

**Controller**: `app/Http/Controllers/Api/EditBookController.php`

**Endpoints**:
- `GET /api/edit-book/campaign/{campaign}` - Get campaign data with chapters/sections
- `PUT /api/edit-book/campaign/{campaign}/chapter/{chapter}` - Update chapter
- `PUT /api/edit-book/campaign/{campaign}/section/{section}` - Update section
- `POST /api/edit-book/campaign/{campaign}/chapter/{chapter}/section` - Add new section
- `DELETE /api/edit-book/campaign/{campaign}/section/{section}` - Delete section
- `POST /api/edit-book/campaign/{campaign}/reorder-sections` - Reorder sections
- `POST /api/edit-book/campaign/{campaign}/reorder-chapters` - Reorder chapters

## Key Features

### 1. Page-specific Loading
- Vue.js bundle only loads on EditBook page via conditional asset loading
- Entry point: `resources/js/edit-book.js`
- Vite configuration supports multiple entry points

### 2. Component Architecture
- Modular design with reusable components
- Clear separation of concerns
- Props and events for component communication

### 3. Rich Text Editing
- TipTap editor with comprehensive formatting options
- HTML content preservation
- Toolbar with common formatting tools

### 4. Drag & Drop
- Vue Draggable for reordering chapters and sections
- Visual feedback during drag operations
- API calls to persist new order

### 5. State Management
- Reactive state using Vue 3 Composition API
- Unsaved changes tracking
- Loading states and error handling

### 6. Authentication & Security
- Laravel Sanctum for API authentication
- CSRF protection
- Authorization checks in API controller

## File Structure

```
resources/js/
├── edit-book.js                          # Entry point
└── components/EditBook/
    ├── EditBookApp.vue                   # Main app component
    ├── BookStructureSidebar.vue          # Sidebar navigation
    ├── ChapterEditor.vue                 # Chapter editing
    ├── SectionEditor.vue                 # Section editing
    ├── RichTextEditor.vue                # WYSIWYG editor
    ├── UnsavedChangesIndicator.vue       # Unsaved changes indicator
    └── LoadingOverlay.vue                # Loading overlay

app/Http/Controllers/Api/
└── EditBookController.php               # API controller

app/Filament/Pages/
└── EditBook.php                         # Updated Filament page

resources/views/filament/pages/
└── edit-book.blade.php                  # Updated Blade template
```

## Dependencies

### Vue.js Ecosystem
- `vue@^3.3.8` - Vue.js 3 framework
- `@vitejs/plugin-vue@^4.5.0` - Vite Vue plugin
- `@vue/compiler-sfc@^3.3.8` - Single File Component compiler

### Rich Text Editor
- `@tiptap/core@^2.1.13` - TipTap core
- `@tiptap/vue-3@^2.1.13` - Vue 3 integration
- Various TipTap extensions for formatting

### Drag & Drop
- `vuedraggable@^4.1.0` - Vue draggable component
- `sortablejs@^1.15.0` - Underlying sortable library

## Usage

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

### Accessing the Page
Navigate to `/edit-book/{campaign_id}` where `{campaign_id}` is a valid campaign ID.

## Integration with FilamentPHP

The Vue.js implementation maintains full integration with FilamentPHP:
- Uses FilamentPHP page layout and navigation
- Respects authentication and authorization
- Maintains consistent styling with Filament theme
- Preserves existing functionality

## Keyboard Shortcuts

- `Ctrl+S` / `Cmd+S` - Save current content (chapter or section)

## Error Handling

- API errors are logged to console and can be extended with user notifications
- Form validation prevents saving invalid data
- Confirmation dialogs for destructive actions (delete)

## Future Enhancements

Potential improvements that could be added:
1. Real-time collaboration features
2. Auto-save functionality
3. Version history
4. Advanced text formatting options
5. Image upload and management
6. Export functionality
7. Spell check integration

## Testing

To test the implementation:
1. Ensure you have a campaign with chapters and sections
2. Navigate to the EditBook page
3. Verify all CRUD operations work correctly
4. Test drag & drop reordering
5. Confirm unsaved changes tracking works
6. Test keyboard shortcuts

## Troubleshooting

### Common Issues

1. **Vue app not mounting**: Check browser console for JavaScript errors
2. **API calls failing**: Verify CSRF token and authentication
3. **Drag & drop not working**: Ensure vuedraggable is properly imported
4. **Rich text editor issues**: Check TipTap extensions are loaded correctly

### Debug Mode

Add `console.log` statements in Vue components to debug state changes and API calls.
