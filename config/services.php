<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    'browserless' => [
        'api_key' => env('BROWSERLESS_API_KEY'),
    ],

    'shifter' => [
        'api_key' => env('SHIFTER_API_KEY'),
    ],

    'dataforseo' => [
        'base_url' => env('DATAFORSEO_BASE_URL', 'https://api.dataforseo.com/v3/'),
        'email' => env('DATAFORSEO_EMAIL', '<EMAIL>'),
        'password' => env('DATAFORSEO_PASSWORD'),
    ],

    'scrapeowl' => [
        'api_key' => env('SCRAPEOWL_API_KEY'),
        'url_v1' => "https://api.scrapeowl.com/v1/scrape"
    ],
    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'model' => env('OPENAI_MODEL', 'gpt-4o'),
    ],
    'ai_model' => env('AI_MODEL', "openai"),
    'convertkit' => [
        'api' => env('CONVERTKIT_API'),
        'secret' => env('CONVERTKIT_SECRET'),
        'tag_id' => env('CONVERTKIT_TAG_ID')
    ],
    'stripe' => [
        'secret' => env('STRIPE_SECRET'),
        'price_id' => env('STRIPE_PRICE_SECRET'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET')  // Rename STRIPE_PRICE_SECRET to STRIPE_PRICE_ID for clarity
    ],
    'warriorplus' => [
        'api_key' => env('WARRIORPLUS_API_KEY'),
        'security_key' => env('WARRIORPLUS_SECURITYKEY'),
    ],
];
