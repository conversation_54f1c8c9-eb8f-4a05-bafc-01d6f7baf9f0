{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "andrewdwallo/filament-selectify": "^2.0", "barryvdh/laravel-dompdf": "^2.2", "campo/random-user-agent": "^1.3", "convertkit/convertkitapi": "^1.3", "ezyang/htmlpurifier": "^4.18", "filament/filament": "^3.3", "flowframe/laravel-trend": "^0.1.5", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^3.9", "lara-zeus/qr": "^1.2", "laravel/framework": "^10.10", "laravel/horizon": "^5.24", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.28", "livewire/livewire": "^3.4.10", "mpdf/mpdf": "^8.2", "openai-php/client": "^0.10.3", "openai-php/laravel": "^0.10.1", "ryangjchandler/filament-progress-column": "^0.4.1", "spatie/browsershot": "^5.0", "spatie/image": "^3.7", "spatie/image-optimizer": "^1.8", "spatie/laravel-ignition": "^2.7", "spatie/laravel-image-optimizer": "^1.8", "stechstudio/filament-impersonate": "^3.8", "stripe/stripe-php": "^14.8", "voku/simple_html_dom": "^4.8"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Sgcomptech\\FilamentTicketing\\": "packages/sgcomptech/filament-ticketing/src"}, "files": ["bootstrap/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}