(function(t,e){"use strict";if(typeof define==="function"&&define.amd){define("pdfjs-dist/build/pdf",["exports"],e)}else if(typeof exports!=="undefined"){e(exports)}else{e(t["pdfjsDistBuildPdf"]={})}})(this,function(t){"use strict";var e="1.7.225";var r="17d135f";var n=typeof document!=="undefined"&&document.currentScript?document.currentScript.src:null;var a={};(function t(){(function(t,e){e(t.pdfjsSharedUtil={})})(this,function(t){var e=typeof window!=="undefined"?window:typeof global!=="undefined"?global:typeof self!=="undefined"?self:this;var r=[.001,0,0,.001,0,0];var n={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};var a={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};var i={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};var s={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};var o={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};var l={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};var c={UNKNOWN:0,FLATE:1,LZW:2,DCT:3,JPX:4,JBIG:5,A85:6,AHX:7,CCF:8,RL:9};var h={UNKNOWN:0,TYPE1:1,TYPE1C:2,CIDFONTTYPE0:3,CIDFONTTYPE0C:4,TRUETYPE:5,CIDFONTTYPE2:6,TYPE3:7,OPENTYPE:8,TYPE0:9,MMTYPE1:10};var u={errors:0,warnings:1,infos:5};var f={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};var d=u.warnings;function p(t){d=t}function v(){return d}function g(t){if(d>=u.infos){console.log("Info: "+t)}}function m(t){if(d>=u.warnings){console.log("Warning: "+t)}}function A(t){console.log("Deprecated API usage: "+t)}function b(t){if(d>=u.errors){console.log("Error: "+t);console.log(y())}throw new Error(t)}function y(){try{throw new Error}catch(t){return t.stack?t.stack.split("\n").slice(2).join("\n"):""}}function x(t,e){if(!t){b(e)}}var S={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font"};function k(t,e){try{var r=new URL(t);if(!r.origin||r.origin==="null"){return false}}catch(t){return false}var n=new URL(e,r);return r.origin===n.origin}function w(t){if(!t){return false}switch(t.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return true;default:return false}}function _(t,e){if(!t){return null}try{var r=e?new URL(t,e):new URL(t);if(w(r)){return r}}catch(t){}return null}function C(t,e,r){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:false});return r}function T(t){var e;return function(){if(t){e=Object.create(null);t(e);t=null}return e}}var L={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};var P=function t(){function e(t,e){this.name="PasswordException";this.message=t;this.code=e}e.prototype=new Error;e.constructor=e;return e}();var E=function t(){function e(t,e){this.name="UnknownErrorException";this.message=t;this.details=e}e.prototype=new Error;e.constructor=e;return e}();var R=function t(){function e(t){this.name="InvalidPDFException";this.message=t}e.prototype=new Error;e.constructor=e;return e}();var I=function t(){function e(t){this.name="MissingPDFException";this.message=t}e.prototype=new Error;e.constructor=e;return e}();var D=function t(){function e(t,e){this.name="UnexpectedResponseException";this.message=t;this.status=e}e.prototype=new Error;e.constructor=e;return e}();var j=function t(){function e(t){this.message=t}e.prototype=new Error;e.prototype.name="NotImplementedException";e.constructor=e;return e}();var O=function t(){function e(t,e){this.begin=t;this.end=e;this.message="Missing data ["+t+", "+e+")"}e.prototype=new Error;e.prototype.name="MissingDataException";e.constructor=e;return e}();var F=function t(){function e(t){this.message=t}e.prototype=new Error;e.prototype.name="XRefParseException";e.constructor=e;return e}();var M=/\x00/g;function N(t){if(typeof t!=="string"){m("The argument for removeNullCharacters must be a string.");return t}return t.replace(M,"")}function U(t){x(t!==null&&typeof t==="object"&&t.length!==undefined,"Invalid argument for bytesToString");var e=t.length;var r=8192;if(e<r){return String.fromCharCode.apply(null,t)}var n=[];for(var a=0;a<e;a+=r){var i=Math.min(a+r,e);var s=t.subarray(a,i);n.push(String.fromCharCode.apply(null,s))}return n.join("")}function B(t){x(typeof t==="string","Invalid argument for stringToBytes");var e=t.length;var r=new Uint8Array(e);for(var n=0;n<e;++n){r[n]=t.charCodeAt(n)&255}return r}function W(t){if(t.length!==undefined){return t.length}x(t.byteLength!==undefined);return t.byteLength}function G(t){if(t.length===1&&t[0]instanceof Uint8Array){return t[0]}var e=0;var r,n=t.length;var a,i;for(r=0;r<n;r++){a=t[r];i=W(a);e+=i}var s=0;var o=new Uint8Array(e);for(r=0;r<n;r++){a=t[r];if(!(a instanceof Uint8Array)){if(typeof a==="string"){a=B(a)}else{a=new Uint8Array(a)}}i=a.byteLength;o.set(a,s);s+=i}return o}function X(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,t&255)}function z(t){var e=1,r=0;while(t>e){e<<=1;r++}return r}function H(t,e){return t[e]<<24>>24}function Y(t,e){return t[e]<<8|t[e+1]}function V(t,e){return(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}function q(){var t=new Uint8Array(2);t[0]=1;var e=new Uint16Array(t.buffer);return e[0]===1}function J(){try{new Function("");return true}catch(t){return false}}var Q=function t(){function e(t,e){this.buffer=t;this.byteLength=t.length;this.length=e===undefined?this.byteLength>>2:e;a(this.length)}e.prototype=Object.create(null);var r=0;function n(t){return{get:function(){var e=this.buffer,r=t<<2;return(e[r]|e[r+1]<<8|e[r+2]<<16|e[r+3]<<24)>>>0},set:function(e){var r=this.buffer,n=t<<2;r[n]=e&255;r[n+1]=e>>8&255;r[n+2]=e>>16&255;r[n+3]=e>>>24&255}}}function a(t){while(r<t){Object.defineProperty(e.prototype,r,n(r));r++}}return e}();t.Uint32ArrayView=Q;var K=[1,0,0,1,0,0];var Z=function t(){function e(){}var r=["rgb(",0,",",0,",",0,")"];e.makeCssRgb=function t(e,n,a){r[1]=e;r[3]=n;r[5]=a;return r.join("")};e.transform=function t(e,r){return[e[0]*r[0]+e[2]*r[1],e[1]*r[0]+e[3]*r[1],e[0]*r[2]+e[2]*r[3],e[1]*r[2]+e[3]*r[3],e[0]*r[4]+e[2]*r[5]+e[4],e[1]*r[4]+e[3]*r[5]+e[5]]};e.applyTransform=function t(e,r){var n=e[0]*r[0]+e[1]*r[2]+r[4];var a=e[0]*r[1]+e[1]*r[3]+r[5];return[n,a]};e.applyInverseTransform=function t(e,r){var n=r[0]*r[3]-r[1]*r[2];var a=(e[0]*r[3]-e[1]*r[2]+r[2]*r[5]-r[4]*r[3])/n;var i=(-e[0]*r[1]+e[1]*r[0]+r[4]*r[1]-r[5]*r[0])/n;return[a,i]};e.getAxialAlignedBoundingBox=function t(r,n){var a=e.applyTransform(r,n);var i=e.applyTransform(r.slice(2,4),n);var s=e.applyTransform([r[0],r[3]],n);var o=e.applyTransform([r[2],r[1]],n);return[Math.min(a[0],i[0],s[0],o[0]),Math.min(a[1],i[1],s[1],o[1]),Math.max(a[0],i[0],s[0],o[0]),Math.max(a[1],i[1],s[1],o[1])]};e.inverseTransform=function t(e){var r=e[0]*e[3]-e[1]*e[2];return[e[3]/r,-e[1]/r,-e[2]/r,e[0]/r,(e[2]*e[5]-e[4]*e[3])/r,(e[4]*e[1]-e[5]*e[0])/r]};e.apply3dTransform=function t(e,r){return[e[0]*r[0]+e[1]*r[1]+e[2]*r[2],e[3]*r[0]+e[4]*r[1]+e[5]*r[2],e[6]*r[0]+e[7]*r[1]+e[8]*r[2]]};e.singularValueDecompose2dScale=function t(e){var r=[e[0],e[2],e[1],e[3]];var n=e[0]*r[0]+e[1]*r[2];var a=e[0]*r[1]+e[1]*r[3];var i=e[2]*r[0]+e[3]*r[2];var s=e[2]*r[1]+e[3]*r[3];var o=(n+s)/2;var l=Math.sqrt((n+s)*(n+s)-4*(n*s-i*a))/2;var c=o+l||1;var h=o-l||1;return[Math.sqrt(c),Math.sqrt(h)]};e.normalizeRect=function t(e){var r=e.slice(0);if(e[0]>e[2]){r[0]=e[2];r[2]=e[0]}if(e[1]>e[3]){r[1]=e[3];r[3]=e[1]}return r};e.intersect=function t(r,n){function a(t,e){return t-e}var i=[r[0],r[2],n[0],n[2]].sort(a),s=[r[1],r[3],n[1],n[3]].sort(a),o=[];r=e.normalizeRect(r);n=e.normalizeRect(n);if(i[0]===r[0]&&i[1]===n[0]||i[0]===n[0]&&i[1]===r[0]){o[0]=i[1];o[2]=i[2]}else{return false}if(s[0]===r[1]&&s[1]===n[1]||s[0]===n[1]&&s[1]===r[1]){o[1]=s[1];o[3]=s[2]}else{return false}return o};e.sign=function t(e){return e<0?-1:1};var n=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"];e.toRoman=function t(e,r){x(st(e)&&e>0,"The number should be a positive integer.");var a,i=[];while(e>=1e3){e-=1e3;i.push("M")}a=e/100|0;e%=100;i.push(n[a]);a=e/10|0;e%=10;i.push(n[10+a]);i.push(n[20+e]);var s=i.join("");return r?s.toLowerCase():s};e.appendToArray=function t(e,r){Array.prototype.push.apply(e,r)};e.prependToArray=function t(e,r){Array.prototype.unshift.apply(e,r)};e.extendObj=function t(e,r){for(var n in r){e[n]=r[n]}};e.getInheritableProperty=function t(e,r,n){while(e&&!e.has(r)){e=e.get("Parent")}if(!e){return null}return n?e.getArray(r):e.get(r)};e.inherit=function t(e,r,n){e.prototype=Object.create(r.prototype);e.prototype.constructor=e;for(var a in n){e.prototype[a]=n[a]}};e.loadScript=function t(e,r){var n=document.createElement("script");var a=false;n.setAttribute("src",e);if(r){n.onload=function(){if(!a){r()}a=true}}document.getElementsByTagName("head")[0].appendChild(n)};return e}();var $=function t(){function e(t,e,r,n,a,i){this.viewBox=t;this.scale=e;this.rotation=r;this.offsetX=n;this.offsetY=a;var s=(t[2]+t[0])/2;var o=(t[3]+t[1])/2;var l,c,h,u;r=r%360;r=r<0?r+360:r;switch(r){case 180:l=-1;c=0;h=0;u=1;break;case 90:l=0;c=1;h=1;u=0;break;case 270:l=0;c=-1;h=-1;u=0;break;default:l=1;c=0;h=0;u=-1;break}if(i){h=-h;u=-u}var f,d;var p,v;if(l===0){f=Math.abs(o-t[1])*e+n;d=Math.abs(s-t[0])*e+a;p=Math.abs(t[3]-t[1])*e;v=Math.abs(t[2]-t[0])*e}else{f=Math.abs(s-t[0])*e+n;d=Math.abs(o-t[1])*e+a;p=Math.abs(t[2]-t[0])*e;v=Math.abs(t[3]-t[1])*e}this.transform=[l*e,c*e,h*e,u*e,f-l*e*s-h*e*o,d-c*e*s-u*e*o];this.width=p;this.height=v;this.fontScale=e}e.prototype={clone:function t(r){r=r||{};var n="scale"in r?r.scale:this.scale;var a="rotation"in r?r.rotation:this.rotation;return new e(this.viewBox.slice(),n,a,this.offsetX,this.offsetY,r.dontFlip)},convertToViewportPoint:function t(e,r){return Z.applyTransform([e,r],this.transform)},convertToViewportRectangle:function t(e){var r=Z.applyTransform([e[0],e[1]],this.transform);var n=Z.applyTransform([e[2],e[3]],this.transform);return[r[0],r[1],n[0],n[1]]},convertToPdfPoint:function t(e,r){return Z.applyInverseTransform([e,r],this.transform)}};return e}();var tt=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function et(t){var e,r=t.length,n=[];if(t[0]==="þ"&&t[1]==="ÿ"){for(e=2;e<r;e+=2){n.push(String.fromCharCode(t.charCodeAt(e)<<8|t.charCodeAt(e+1)))}}else{for(e=0;e<r;++e){var a=tt[t.charCodeAt(e)];n.push(a?String.fromCharCode(a):t.charAt(e))}}return n.join("")}function rt(t){return decodeURIComponent(escape(t))}function nt(t){return unescape(encodeURIComponent(t))}function at(t){for(var e in t){return false}return true}function it(t){return typeof t==="boolean"}function st(t){return typeof t==="number"&&(t|0)===t}function ot(t){return typeof t==="number"}function lt(t){return typeof t==="string"}function ct(t){return t instanceof Array}function ht(t){return typeof t==="object"&&t!==null&&t.byteLength!==undefined}function ut(t){return t===32||t===9||t===13||t===10}function ft(){var t={};t.promise=new Promise(function(e,r){t.resolve=e;t.reject=r});return t}(function t(){if(e.Promise){if(typeof e.Promise.all!=="function"){e.Promise.all=function(t){var r=0,n=[],a,i;var s=new e.Promise(function(t,e){a=t;i=e});t.forEach(function(t,e){r++;t.then(function(t){n[e]=t;r--;if(r===0){a(n)}},i)});if(r===0){a(n)}return s}}if(typeof e.Promise.resolve!=="function"){e.Promise.resolve=function(t){return new e.Promise(function(e){e(t)})}}if(typeof e.Promise.reject!=="function"){e.Promise.reject=function(t){return new e.Promise(function(e,r){r(t)})}}if(typeof e.Promise.prototype.catch!=="function"){e.Promise.prototype.catch=function(t){return e.Promise.prototype.then(undefined,t)}}return}var r=0;var n=1;var a=2;var i=500;var s={handlers:[],running:false,unhandledRejections:[],pendingRejectionCheck:false,scheduleHandlers:function t(e){if(e._status===r){return}this.handlers=this.handlers.concat(e._handlers);e._handlers=[];if(this.running){return}this.running=true;setTimeout(this.runHandlers.bind(this),0)},runHandlers:function t(){var e=1;var r=Date.now()+e;while(this.handlers.length>0){var i=this.handlers.shift();var s=i.thisPromise._status;var o=i.thisPromise._value;try{if(s===n){if(typeof i.onResolve==="function"){o=i.onResolve(o)}}else if(typeof i.onReject==="function"){o=i.onReject(o);s=n;if(i.thisPromise._unhandledRejection){this.removeUnhandeledRejection(i.thisPromise)}}}catch(t){s=a;o=t}i.nextPromise._updateStatus(s,o);if(Date.now()>=r){break}}if(this.handlers.length>0){setTimeout(this.runHandlers.bind(this),0);return}this.running=false},addUnhandledRejection:function t(e){this.unhandledRejections.push({promise:e,time:Date.now()});this.scheduleRejectionCheck()},removeUnhandeledRejection:function t(e){e._unhandledRejection=false;for(var r=0;r<this.unhandledRejections.length;r++){if(this.unhandledRejections[r].promise===e){this.unhandledRejections.splice(r);r--}}},scheduleRejectionCheck:function t(){if(this.pendingRejectionCheck){return}this.pendingRejectionCheck=true;setTimeout(function t(){this.pendingRejectionCheck=false;var e=Date.now();for(var r=0;r<this.unhandledRejections.length;r++){if(e-this.unhandledRejections[r].time>i){var n=this.unhandledRejections[r].promise._value;var a="Unhandled rejection: "+n;if(n.stack){a+="\n"+n.stack}m(a);this.unhandledRejections.splice(r);r--}}if(this.unhandledRejections.length){this.scheduleRejectionCheck()}}.bind(this),i)}};var o=function t(e){this._status=r;this._handlers=[];try{e.call(this,this._resolve.bind(this),this._reject.bind(this))}catch(t){this._reject(t)}};o.all=function t(e){var r,n;var i=new o(function(t,e){r=t;n=e});var s=e.length;var l=[];if(s===0){r(l);return i}function c(t){if(i._status===a){return}l=[];n(t)}for(var h=0,u=e.length;h<u;++h){var f=e[h];var d=function(t){return function(e){if(i._status===a){return}l[t]=e;s--;if(s===0){r(l)}}}(h);if(o.isPromise(f)){f.then(d,c)}else{d(f)}}return i};o.isPromise=function t(e){return e&&typeof e.then==="function"};o.resolve=function t(e){return new o(function(t){t(e)})};o.reject=function t(e){return new o(function(t,r){r(e)})};o.prototype={_status:null,_value:null,_handlers:null,_unhandledRejection:null,_updateStatus:function t(e,r){if(this._status===n||this._status===a){return}if(e===n&&o.isPromise(r)){r.then(this._updateStatus.bind(this,n),this._updateStatus.bind(this,a));return}this._status=e;this._value=r;if(e===a&&this._handlers.length===0){this._unhandledRejection=true;s.addUnhandledRejection(this)}s.scheduleHandlers(this)},_resolve:function t(e){this._updateStatus(n,e)},_reject:function t(e){this._updateStatus(a,e)},then:function t(e,r){var n=new o(function(t,e){this.resolve=t;this.reject=e});this._handlers.push({thisPromise:this,onResolve:e,onReject:r,nextPromise:n});s.scheduleHandlers(this);return n},catch:function t(e){return this.then(undefined,e)}};e.Promise=o})();(function t(){if(e.WeakMap){return}var r=0;function n(){this.id="$weakmap"+r++}n.prototype={has:function(t){return!!Object.getOwnPropertyDescriptor(t,this.id)},get:function(t,e){return this.has(t)?t[this.id]:e},set:function(t,e){Object.defineProperty(t,this.id,{value:e,enumerable:false,configurable:true})},delete:function(t){delete t[this.id]}};e.WeakMap=n})();var dt=function t(){function e(t,e,r){while(t.length<r){t+=e}return t}function r(){this.started=Object.create(null);this.times=[];this.enabled=true}r.prototype={time:function t(e){if(!this.enabled){return}if(e in this.started){m("Timer is already running for "+e)}this.started[e]=Date.now()},timeEnd:function t(e){if(!this.enabled){return}if(!(e in this.started)){m("Timer has not been started for "+e)}this.times.push({name:e,start:this.started[e],end:Date.now()});delete this.started[e]},toString:function t(){var r,n;var a=this.times;var i="";var s=0;for(r=0,n=a.length;r<n;++r){var o=a[r]["name"];if(o.length>s){s=o.length}}for(r=0,n=a.length;r<n;++r){var l=a[r];var c=l.end-l.start;i+=e(l["name"]," ",s)+" "+c+"ms\n"}return i}};return r}();var pt=function t(e,r){if(typeof Blob!=="undefined"){return new Blob([e],{type:r})}m('The "Blob" constructor is not supported.')};var vt=function t(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return function t(r,n,a){if(!a&&typeof URL!=="undefined"&&URL.createObjectURL){var i=pt(r,n);return URL.createObjectURL(i)}var s="data:"+n+";base64,";for(var o=0,l=r.length;o<l;o+=3){var c=r[o]&255;var h=r[o+1]&255;var u=r[o+2]&255;var f=c>>2,d=(c&3)<<4|h>>4;var p=o+1<l?(h&15)<<2|u>>6:64;var v=o+2<l?u&63:64;s+=e[f]+e[d]+e[p]+e[v]}return s}}();function gt(t,e,r){this.sourceName=t;this.targetName=e;this.comObj=r;this.callbackIndex=1;this.postMessageTransfers=true;var n=this.callbacksCapabilities=Object.create(null);var a=this.actionHandler=Object.create(null);this._onComObjOnMessage=function t(e){var i=e.data;if(i.targetName!==this.sourceName){return}if(i.isReply){var s=i.callbackId;if(i.callbackId in n){var o=n[s];delete n[s];if("error"in i){o.reject(i.error)}else{o.resolve(i.data)}}else{b("Cannot resolve callback "+s)}}else if(i.action in a){var l=a[i.action];if(i.callbackId){var c=this.sourceName;var h=i.sourceName;Promise.resolve().then(function(){return l[0].call(l[1],i.data)}).then(function(t){r.postMessage({sourceName:c,targetName:h,isReply:true,callbackId:i.callbackId,data:t})},function(t){if(t instanceof Error){t=t+""}r.postMessage({sourceName:c,targetName:h,isReply:true,callbackId:i.callbackId,error:t})})}else{l[0].call(l[1],i.data)}}else{b("Unknown action from worker: "+i.action)}}.bind(this);r.addEventListener("message",this._onComObjOnMessage)}gt.prototype={on:function t(e,r,n){var a=this.actionHandler;if(a[e]){b('There is already an actionName called "'+e+'"')}a[e]=[r,n]},send:function t(e,r,n){var a={sourceName:this.sourceName,targetName:this.targetName,action:e,data:r};this.postMessage(a,n)},sendWithPromise:function t(e,r,n){var a=this.callbackIndex++;var i={sourceName:this.sourceName,targetName:this.targetName,action:e,data:r,callbackId:a};var s=ft();this.callbacksCapabilities[a]=s;try{this.postMessage(i,n)}catch(t){s.reject(t)}return s.promise},postMessage:function(t,e){if(e&&this.postMessageTransfers){this.comObj.postMessage(t,e)}else{this.comObj.postMessage(t)}},destroy:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}};function mt(t,e,r){var n=new Image;n.onload=function e(){r.resolve(t,n)};n.onerror=function e(){r.resolve(t,null);m("Error during JPEG image loading")};n.src=e}(function t(e){var r=false;try{if(typeof URL==="function"&&typeof URL.prototype==="object"&&"origin"in URL.prototype){var n=new URL("b","http://a");n.pathname="c%20d";r=n.href==="http://a/c%20d"}}catch(t){}if(r){return}var a=Object.create(null);a["ftp"]=21;a["file"]=0;a["gopher"]=70;a["http"]=80;a["https"]=443;a["ws"]=80;a["wss"]=443;var i=Object.create(null);i["%2e"]=".";i[".%2e"]="..";i["%2e."]="..";i["%2e%2e"]="..";function s(t){return a[t]!==undefined}function o(){v.call(this);this._isInvalid=true}function l(t){if(t===""){o.call(this)}return t.toLowerCase()}function c(t){var e=t.charCodeAt(0);if(e>32&&e<127&&[34,35,60,62,63,96].indexOf(e)===-1){return t}return encodeURIComponent(t)}function h(t){var e=t.charCodeAt(0);if(e>32&&e<127&&[34,35,60,62,96].indexOf(e)===-1){return t}return encodeURIComponent(t)}var u,f=/[a-zA-Z]/,d=/[a-zA-Z0-9\+\-\.]/;function p(t,e,r){function n(t){b.push(t)}var p=e||"scheme start",v=0,g="",m=false,A=false,b=[];t:while((t[v-1]!==u||v===0)&&!this._isInvalid){var y=t[v];switch(p){case"scheme start":if(y&&f.test(y)){g+=y.toLowerCase();p="scheme"}else if(!e){g="";p="no scheme";continue}else{n("Invalid scheme.");break t}break;case"scheme":if(y&&d.test(y)){g+=y.toLowerCase()}else if(y===":"){this._scheme=g;g="";if(e){break t}if(s(this._scheme)){this._isRelative=true}if(this._scheme==="file"){p="relative"}else if(this._isRelative&&r&&r._scheme===this._scheme){p="relative or authority"}else if(this._isRelative){p="authority first slash"}else{p="scheme data"}}else if(!e){g="";v=0;p="no scheme";continue}else if(u===y){break t}else{n("Code point not allowed in scheme: "+y);break t}break;case"scheme data":if(y==="?"){this._query="?";p="query"}else if(y==="#"){this._fragment="#";p="fragment"}else{if(u!==y&&"\t"!==y&&"\n"!==y&&"\r"!==y){this._schemeData+=c(y)}}break;case"no scheme":if(!r||!s(r._scheme)){n("Missing scheme.");o.call(this)}else{p="relative";continue}break;case"relative or authority":if(y==="/"&&t[v+1]==="/"){p="authority ignore slashes"}else{n("Expected /, got: "+y);p="relative";continue}break;case"relative":this._isRelative=true;if("file"!==this._scheme){this._scheme=r._scheme}if(u===y){this._host=r._host;this._port=r._port;this._path=r._path.slice();this._query=r._query;this._username=r._username;this._password=r._password;break t}else if(y==="/"||y==="\\"){if(y==="\\"){n("\\ is an invalid code point.")}p="relative slash"}else if(y==="?"){this._host=r._host;this._port=r._port;this._path=r._path.slice();this._query="?";this._username=r._username;this._password=r._password;p="query"}else if(y==="#"){this._host=r._host;this._port=r._port;this._path=r._path.slice();this._query=r._query;this._fragment="#";this._username=r._username;this._password=r._password;p="fragment"}else{var x=t[v+1];var S=t[v+2];if("file"!==this._scheme||!f.test(y)||x!==":"&&x!=="|"||u!==S&&"/"!==S&&"\\"!==S&&"?"!==S&&"#"!==S){this._host=r._host;this._port=r._port;this._username=r._username;this._password=r._password;this._path=r._path.slice();this._path.pop()}p="relative path";continue}break;case"relative slash":if(y==="/"||y==="\\"){if(y==="\\"){n("\\ is an invalid code point.")}if(this._scheme==="file"){p="file host"}else{p="authority ignore slashes"}}else{if("file"!==this._scheme){this._host=r._host;this._port=r._port;this._username=r._username;this._password=r._password}p="relative path";continue}break;case"authority first slash":if(y==="/"){p="authority second slash"}else{n("Expected '/', got: "+y);p="authority ignore slashes";continue}break;case"authority second slash":p="authority ignore slashes";if("/"!==y){n("Expected '/', got: "+y);continue}break;case"authority ignore slashes":if("/"!==y&&"\\"!==y){p="authority";continue}else{n("Expected authority, got: "+y)}break;case"authority":if(y==="@"){if(m){n("@ already seen.");g+="%40"}m=true;for(var k=0;k<g.length;k++){var w=g[k];if(w==="\t"||w==="\n"||w==="\r"){n("Invalid whitespace in authority.");continue}if(w===":"&&this._password===null){this._password="";continue}var _=c(w);if(null!==this._password){this._password+=_}else{this._username+=_}}g=""}else if(y===u||y==="/"||y==="\\"||y==="?"||y==="#"){v-=g.length;g="";p="host";continue}else{g+=y}break;case"file host":if(y===u||y==="/"||y==="\\"||y==="?"||y==="#"){if(g.length===2&&f.test(g[0])&&(g[1]===":"||g[1]==="|")){p="relative path"}else if(g.length===0){p="relative path start"}else{this._host=l.call(this,g);g="";p="relative path start"}continue}else if(y==="\t"||y==="\n"||y==="\r"){n("Invalid whitespace in file host.")}else{g+=y}break;case"host":case"hostname":if(y===":"&&!A){this._host=l.call(this,g);g="";p="port";if(e==="hostname"){break t}}else if(y===u||y==="/"||y==="\\"||y==="?"||y==="#"){this._host=l.call(this,g);g="";p="relative path start";if(e){break t}continue}else if("\t"!==y&&"\n"!==y&&"\r"!==y){if(y==="["){A=true}else if(y==="]"){A=false}g+=y}else{n("Invalid code point in host/hostname: "+y)}break;case"port":if(/[0-9]/.test(y)){g+=y}else if(y===u||y==="/"||y==="\\"||y==="?"||y==="#"||e){if(""!==g){var C=parseInt(g,10);if(C!==a[this._scheme]){this._port=C+""}g=""}if(e){break t}p="relative path start";continue}else if(y==="\t"||y==="\n"||y==="\r"){n("Invalid code point in port: "+y)}else{o.call(this)}break;case"relative path start":if(y==="\\"){n("'\\' not allowed in path.")}p="relative path";if("/"!==y&&"\\"!==y){continue}break;case"relative path":if(y===u||y==="/"||y==="\\"||!e&&(y==="?"||y==="#")){if(y==="\\"){n("\\ not allowed in relative path.")}var T;if(T=i[g.toLowerCase()]){g=T}if(g===".."){this._path.pop();if("/"!==y&&"\\"!==y){this._path.push("")}}else if(g==="."&&"/"!==y&&"\\"!==y){this._path.push("")}else if("."!==g){if(this._scheme==="file"&&this._path.length===0&&g.length===2&&f.test(g[0])&&g[1]==="|"){g=g[0]+":"}this._path.push(g)}g="";if(y==="?"){this._query="?";p="query"}else if(y==="#"){this._fragment="#";p="fragment"}}else if("\t"!==y&&"\n"!==y&&"\r"!==y){g+=c(y)}break;case"query":if(!e&&y==="#"){this._fragment="#";p="fragment"}else if(u!==y&&"\t"!==y&&"\n"!==y&&"\r"!==y){this._query+=h(y)}break;case"fragment":if(u!==y&&"\t"!==y&&"\n"!==y&&"\r"!==y){this._fragment+=y}break}v++}}function v(){this._scheme="";this._schemeData="";this._username="";this._password=null;this._host="";this._port="";this._path=[];this._query="";this._fragment="";this._isInvalid=false;this._isRelative=false}function g(t,e){if(e!==undefined&&!(e instanceof g)){e=new g(String(e))}this._url=t;v.call(this);var r=t.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");p.call(this,r,null,e)}g.prototype={toString:function(){return this.href},get href(){if(this._isInvalid){return this._url}var t="";if(""!==this._username||null!==this._password){t=this._username+(null!==this._password?":"+this._password:"")+"@"}return this.protocol+(this._isRelative?"//"+t+this.host:"")+this.pathname+this._query+this._fragment},set href(t){v.call(this);p.call(this,t)},get protocol(){return this._scheme+":"},set protocol(t){if(this._isInvalid){return}p.call(this,t+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(t){if(this._isInvalid||!this._isRelative){return}p.call(this,t,"host")},get hostname(){return this._host},set hostname(t){if(this._isInvalid||!this._isRelative){return}p.call(this,t,"hostname")},get port(){return this._port},set port(t){if(this._isInvalid||!this._isRelative){return}p.call(this,t,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(t){if(this._isInvalid||!this._isRelative){return}this._path=[];p.call(this,t,"relative path start")},get search(){return this._isInvalid||!this._query||this._query==="?"?"":this._query},set search(t){if(this._isInvalid||!this._isRelative){return}this._query="?";if(t[0]==="?"){t=t.slice(1)}p.call(this,t,"query")},get hash(){return this._isInvalid||!this._fragment||this._fragment==="#"?"":this._fragment},set hash(t){if(this._isInvalid){return}this._fragment="#";if(t[0]==="#"){t=t.slice(1)}p.call(this,t,"fragment")},get origin(){var t;if(this._isInvalid||!this._scheme){return""}switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null"}t=this.host;if(!t){return""}return this._scheme+"://"+t}};var m=e.URL;if(m){g.createObjectURL=function(t){return m.createObjectURL.apply(m,arguments)};g.revokeObjectURL=function(t){m.revokeObjectURL(t)}}e.URL=g})(e);t.FONT_IDENTITY_MATRIX=r;t.IDENTITY_MATRIX=K;t.OPS=f;t.VERBOSITY_LEVELS=u;t.UNSUPPORTED_FEATURES=S;t.AnnotationBorderStyleType=l;t.AnnotationFieldFlag=o;t.AnnotationFlag=s;t.AnnotationType=i;t.FontType=h;t.ImageKind=a;t.InvalidPDFException=R;t.MessageHandler=gt;t.MissingDataException=O;t.MissingPDFException=I;t.NotImplementedException=j;t.PageViewport=$;t.PasswordException=P;t.PasswordResponses=L;t.StatTimer=dt;t.StreamType=c;t.TextRenderingMode=n;t.UnexpectedResponseException=D;t.UnknownErrorException=E;t.Util=Z;t.XRefParseException=F;t.arrayByteLength=W;t.arraysToBytes=G;t.assert=x;t.bytesToString=U;t.createBlob=pt;t.createPromiseCapability=ft;t.createObjectURL=vt;t.deprecated=A;t.error=b;t.getLookupTableFactory=T;t.getVerbosityLevel=v;t.globalScope=e;t.info=g;t.isArray=ct;t.isArrayBuffer=ht;t.isBool=it;t.isEmptyObj=at;t.isInt=st;t.isNum=ot;t.isString=lt;t.isSpace=ut;t.isSameOrigin=k;t.createValidAbsoluteUrl=_;t.isLittleEndian=q;t.isEvalSupported=J;t.loadJpegStream=mt;t.log2=z;t.readInt8=H;t.readUint16=Y;t.readUint32=V;t.removeNullCharacters=N;t.setVerbosityLevel=p;t.shadow=C;t.string32=X;t.stringToBytes=B;t.stringToPDFString=et;t.stringToUTF8String=rt;t.utf8StringToString=nt;t.warn=m});(function(t,e){e(t.pdfjsDisplayDOMUtils={},t.pdfjsSharedUtil)})(this,function(t,e){var r=e.removeNullCharacters;var n=e.warn;var a=e.deprecated;var i=e.createValidAbsoluteUrl;var s="noopener noreferrer nofollow";var o=function t(){var e=["ms","Moz","Webkit","O"];var r=Object.create(null);function n(){}n.getProp=function t(n,a){if(arguments.length===1&&typeof r[n]==="string"){return r[n]}a=a||document.documentElement;var i=a.style,s,o;if(typeof i[n]==="string"){return r[n]=n}o=n.charAt(0).toUpperCase()+n.slice(1);for(var l=0,c=e.length;l<c;l++){s=e[l]+o;if(typeof i[s]==="string"){return r[n]=s}}return r[n]="undefined"};n.setProp=function t(e,r,n){var a=this.getProp(e);if(a!=="undefined"){r.style[a]=n}};return n}();var l;l=function t(){var e=document.createElement("canvas");e.width=e.height=1;var r=e.getContext("2d");var n=r.createImageData(1,1);return typeof n.data.buffer!=="undefined"};var c={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};var h=["","_self","_blank","_parent","_top"];function u(t,e){var n=e&&e.url;t.href=t.title=n?r(n):"";if(n){var a=e.target;if(typeof a==="undefined"){a=d("externalLinkTarget")}t.target=h[a];var i=e.rel;if(typeof i==="undefined"){i=d("externalLinkRel")}t.rel=i}}function f(t){var e=t.indexOf("#");var r=t.indexOf("?");var n=Math.min(e>0?e:t.length,r>0?r:t.length);return t.substring(t.lastIndexOf("/",n)+1,n)}function d(t){var r=e.globalScope.PDFJS;switch(t){case"pdfBug":return r?r.pdfBug:false;case"disableAutoFetch":return r?r.disableAutoFetch:false;case"disableStream":return r?r.disableStream:false;case"disableRange":return r?r.disableRange:false;case"disableFontFace":return r?r.disableFontFace:false;case"disableCreateObjectURL":return r?r.disableCreateObjectURL:false;case"disableWebGL":return r?r.disableWebGL:true;case"cMapUrl":return r?r.cMapUrl:null;case"cMapPacked":return r?r.cMapPacked:false;case"postMessageTransfers":return r?r.postMessageTransfers:true;case"workerSrc":return r?r.workerSrc:null;case"disableWorker":return r?r.disableWorker:false;case"maxImageSize":return r?r.maxImageSize:-1;case"imageResourcesPath":return r?r.imageResourcesPath:"";case"isEvalSupported":return r?r.isEvalSupported:true;case"externalLinkTarget":if(!r){return c.NONE}switch(r.externalLinkTarget){case c.NONE:case c.SELF:case c.BLANK:case c.PARENT:case c.TOP:return r.externalLinkTarget}n("PDFJS.externalLinkTarget is invalid: "+r.externalLinkTarget);r.externalLinkTarget=c.NONE;return c.NONE;case"externalLinkRel":return r?r.externalLinkRel:s;case"enableStats":return!!(r&&r.enableStats);default:throw new Error("Unknown default setting: "+t)}}function p(){var t=d("externalLinkTarget");switch(t){case c.NONE:return false;case c.SELF:case c.BLANK:case c.PARENT:case c.TOP:return true}}function v(t,e){a("isValidUrl(), please use createValidAbsoluteUrl() instead.");var r=e?"http://example.com":null;return i(t,r)!==null}t.CustomStyle=o;t.addLinkAttributes=u;t.isExternalLinkTargetSet=p;t.isValidUrl=v;t.getFilenameFromUrl=f;t.LinkTarget=c;t.hasCanvasTypedArrays=l;t.getDefaultSetting=d;t.DEFAULT_LINK_REL=s});(function(t,e){e(t.pdfjsDisplayFontLoader={},t.pdfjsSharedUtil)})(this,function(t,e){var r=e.assert;var n=e.bytesToString;var a=e.string32;var i=e.shadow;var s=e.warn;function o(t){this.docId=t;this.styleElement=null;this.nativeFontFaces=[];this.loadTestFontId=0;this.loadingContext={requests:[],nextRequestId:0}}o.prototype={insertRule:function t(e){var r=this.styleElement;if(!r){r=this.styleElement=document.createElement("style");r.id="PDFJS_FONT_STYLE_TAG_"+this.docId;document.documentElement.getElementsByTagName("head")[0].appendChild(r)}var n=r.sheet;n.insertRule(e,n.cssRules.length)},clear:function t(){var e=this.styleElement;if(e){e.parentNode.removeChild(e);e=this.styleElement=null}this.nativeFontFaces.forEach(function(t){document.fonts.delete(t)});this.nativeFontFaces.length=0}};var l=function(){return atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQ"+"AABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwA"+"AAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbm"+"FtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAA"+"AADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6A"+"ABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAA"+"MQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAA"+"AAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAA"+"AAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQ"+"AAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMA"+"AQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAA"+"EAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAA"+"AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAA"+"AAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgc"+"A/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWF"+"hYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQA"+"AAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAg"+"ABAAAAAAAAAAAD6AAAAAAAAA==")};Object.defineProperty(o.prototype,"loadTestFont",{get:function(){return i(this,"loadTestFont",l())},configurable:true});o.prototype.addNativeFontFace=function t(e){this.nativeFontFaces.push(e);document.fonts.add(e)};o.prototype.bind=function t(e,r){var n=[];var a=[];var i=[];var l=function(t){return t.loaded.catch(function(e){s('Failed to load font "'+t.family+'": '+e)})};var c=o.isFontLoadingAPISupported&&!o.isSyncFontLoadingSupported;for(var h=0,u=e.length;h<u;h++){var f=e[h];if(f.attached||f.loading===false){continue}f.attached=true;if(c){var d=f.createNativeFontFace();if(d){this.addNativeFontFace(d);i.push(l(d))}}else{var p=f.createFontFaceRule();if(p){this.insertRule(p);n.push(p);a.push(f)}}}var v=this.queueLoadingCallback(r);if(c){Promise.all(i).then(function(){v.complete()})}else if(n.length>0&&!o.isSyncFontLoadingSupported){this.prepareFontLoadEvent(n,a,v)}else{v.complete()}};o.prototype.queueLoadingCallback=function t(e){function n(){r(!s.end,"completeRequest() cannot be called twice");s.end=Date.now();while(a.requests.length>0&&a.requests[0].end){var t=a.requests.shift();setTimeout(t.callback,0)}}var a=this.loadingContext;var i="pdfjs-font-loading-"+a.nextRequestId++;var s={id:i,complete:n,callback:e,started:Date.now()};a.requests.push(s);return s};o.prototype.prepareFontLoadEvent=function t(e,r,n){function i(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|t.charCodeAt(e+3)&255}function o(t,e,r,n){var a=t.substr(0,e);var i=t.substr(e+r);return a+n+i}var l,c;var h=document.createElement("canvas");h.width=1;h.height=1;var u=h.getContext("2d");var f=0;function d(t,e){f++;if(f>30){s("Load test font never loaded.");e();return}u.font="30px "+t;u.fillText(".",0,20);var r=u.getImageData(0,0,1,1);if(r.data[3]>0){e();return}setTimeout(d.bind(null,t,e))}var p="lt"+Date.now()+this.loadTestFontId++;var v=this.loadTestFont;var g=976;v=o(v,g,p.length,p);var m=16;var A=1482184792;var b=i(v,m);for(l=0,c=p.length-3;l<c;l+=4){b=b-A+i(p,l)|0}if(l<p.length){b=b-A+i(p+"XXX",l)|0}v=o(v,m,4,a(b));var y="url(data:font/opentype;base64,"+btoa(v)+");";var x='@font-face { font-family:"'+p+'";src:'+y+"}";this.insertRule(x);var S=[];for(l=0,c=r.length;l<c;l++){S.push(r[l].loadedName)}S.push(p);var k=document.createElement("div");k.setAttribute("style","visibility: hidden;"+"width: 10px; height: 10px;"+"position: absolute; top: 0px; left: 0px;");for(l=0,c=S.length;l<c;++l){var w=document.createElement("span");w.textContent="Hi";w.style.fontFamily=S[l];k.appendChild(w)}document.body.appendChild(k);d(p,function(){document.body.removeChild(k);n.complete()})};o.isFontLoadingAPISupported=typeof document!=="undefined"&&!!document.fonts;var c=function t(){if(typeof navigator==="undefined"){return true}var e=false;var r=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);if(r&&r[1]>=14){e=true}return e};Object.defineProperty(o,"isSyncFontLoadingSupported",{get:function(){return i(o,"isSyncFontLoadingSupported",c())},enumerable:true,configurable:true});var h={get value(){return i(this,"value",e.isEvalSupported())}};var u=function t(){function e(t,e){this.compiledGlyphs=Object.create(null);for(var r in t){this[r]=t[r]}this.options=e}e.prototype={createNativeFontFace:function t(){if(!this.data){return null}if(this.options.disableFontFace){this.disableFontFace=true;return null}var e=new FontFace(this.loadedName,this.data,{});if(this.options.fontRegistry){this.options.fontRegistry.registerFont(this)}return e},createFontFaceRule:function t(){if(!this.data){return null}if(this.options.disableFontFace){this.disableFontFace=true;return null}var e=n(new Uint8Array(this.data));var r=this.loadedName;var a="url(data:"+this.mimetype+";base64,"+btoa(e)+");";var i='@font-face { font-family:"'+r+'";src:'+a+"}";if(this.options.fontRegistry){this.options.fontRegistry.registerFont(this,a)}return i},getPathGenerator:function t(e,r){if(!(r in this.compiledGlyphs)){var n=e.get(this.loadedName+"_path_"+r);var a,i,s;if(this.options.isEvalSupported&&h.value){var o,l="";for(i=0,s=n.length;i<s;i++){a=n[i];if(a.args!==undefined){o=a.args.join(",")}else{o=""}l+="c."+a.cmd+"("+o+");\n"}this.compiledGlyphs[r]=new Function("c","size",l)}else{this.compiledGlyphs[r]=function(t,e){for(i=0,s=n.length;i<s;i++){a=n[i];if(a.cmd==="scale"){a.args=[e,-e]}t[a.cmd].apply(t,a.args)}}}}return this.compiledGlyphs[r]}};return e}();t.FontFaceObject=u;t.FontLoader=o});(function(t,e){e(t.pdfjsDisplayMetadata={},t.pdfjsSharedUtil)})(this,function(t,e){var r=e.error;function n(t){return t.replace(/>\\376\\377([^<]+)/g,function(t,e){var r=e.replace(/\\([0-3])([0-7])([0-7])/g,function(t,e,r,n){return String.fromCharCode(e*64+r*8+n*1)});var n="";for(var a=0;a<r.length;a+=2){var i=r.charCodeAt(a)*256+r.charCodeAt(a+1);n+=i>=32&&i<127&&i!==60&&i!==62&&i!==38?String.fromCharCode(i):"&#x"+(65536+i).toString(16).substring(1)+";"}return">"+n})}function a(t){if(typeof t==="string"){t=n(t);var e=new DOMParser;t=e.parseFromString(t,"application/xml")}else if(!(t instanceof Document)){r("Metadata: Invalid metadata object")}this.metaDocument=t;this.metadata=Object.create(null);this.parse()}a.prototype={parse:function t(){var e=this.metaDocument;var r=e.documentElement;if(r.nodeName.toLowerCase()!=="rdf:rdf"){r=r.firstChild;while(r&&r.nodeName.toLowerCase()!=="rdf:rdf"){r=r.nextSibling}}var n=r?r.nodeName.toLowerCase():null;if(!r||n!=="rdf:rdf"||!r.hasChildNodes()){return}var a=r.childNodes,i,s,o,l,c,h,u;for(l=0,h=a.length;l<h;l++){i=a[l];if(i.nodeName.toLowerCase()!=="rdf:description"){continue}for(c=0,u=i.childNodes.length;c<u;c++){if(i.childNodes[c].nodeName.toLowerCase()!=="#text"){s=i.childNodes[c];o=s.nodeName.toLowerCase();this.metadata[o]=s.textContent.trim()}}}},get:function t(e){return this.metadata[e]||null},has:function t(e){return typeof this.metadata[e]!=="undefined"}};t.Metadata=a});(function(t,e){e(t.pdfjsDisplaySVG={},t.pdfjsSharedUtil)})(this,function(t,e){var r=e.FONT_IDENTITY_MATRIX;var n=e.IDENTITY_MATRIX;var a=e.ImageKind;var i=e.OPS;var s=e.Util;var o=e.isNum;var l=e.isArray;var c=e.warn;var h=e.createObjectURL;var u={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"};var f=function t(){var e=new Uint8Array([137,80,78,71,13,10,26,10]);var r=12;var n=new Int32Array(256);for(var i=0;i<256;i++){var s=i;for(var o=0;o<8;o++){if(s&1){s=3988292384^s>>1&2147483647}else{s=s>>1&2147483647}}n[i]=s}function l(t,e,r){var a=-1;for(var i=e;i<r;i++){var s=(a^t[i])&255;var o=n[s];a=a>>>8^o}return a^-1}function c(t,e,r,n){var a=n;var i=e.length;r[a]=i>>24&255;r[a+1]=i>>16&255;r[a+2]=i>>8&255;r[a+3]=i&255;a+=4;r[a]=t.charCodeAt(0)&255;r[a+1]=t.charCodeAt(1)&255;r[a+2]=t.charCodeAt(2)&255;r[a+3]=t.charCodeAt(3)&255;a+=4;r.set(e,a);a+=e.length;var s=l(r,n+4,a);r[a]=s>>24&255;r[a+1]=s>>16&255;r[a+2]=s>>8&255;r[a+3]=s&255}function u(t,e,r){var n=1;var a=0;for(var i=e;i<r;++i){n=(n+(t[i]&255))%65521;a=(a+n)%65521}return a<<16|n}function f(t,n,i){var s=t.width;var o=t.height;var l,f,d;var p=t.data;switch(n){case a.GRAYSCALE_1BPP:f=0;l=1;d=s+7>>3;break;case a.RGB_24BPP:f=2;l=8;d=s*3;break;case a.RGBA_32BPP:f=6;l=8;d=s*4;break;default:throw new Error("invalid format")}var v=new Uint8Array((1+d)*o);var g=0,m=0;var A,b;for(A=0;A<o;++A){v[g++]=0;v.set(p.subarray(m,m+d),g);m+=d;g+=d}if(n===a.GRAYSCALE_1BPP){g=0;for(A=0;A<o;A++){g++;for(b=0;b<d;b++){v[g++]^=255}}}var y=new Uint8Array([s>>24&255,s>>16&255,s>>8&255,s&255,o>>24&255,o>>16&255,o>>8&255,o&255,l,f,0,0,0]);var x=v.length;var S=65535;var k=Math.ceil(x/S);var w=new Uint8Array(2+x+k*5+4);var _=0;w[_++]=120;w[_++]=156;var C=0;while(x>S){w[_++]=0;w[_++]=255;w[_++]=255;w[_++]=0;w[_++]=0;w.set(v.subarray(C,C+S),_);_+=S;C+=S;x-=S}w[_++]=1;w[_++]=x&255;w[_++]=x>>8&255;w[_++]=~x&65535&255;w[_++]=(~x&65535)>>8&255;w.set(v.subarray(C),_);_+=v.length-C;var T=u(v,0,v.length);w[_++]=T>>24&255;w[_++]=T>>16&255;w[_++]=T>>8&255;w[_++]=T&255;var L=e.length+r*3+y.length+w.length;var P=new Uint8Array(L);var E=0;P.set(e,E);E+=e.length;c("IHDR",y,P,E);E+=r+y.length;c("IDATA",w,P,E);E+=r+w.length;c("IEND",new Uint8Array(0),P,E);return h(P,"image/png",i)}return function t(e,r){var n=e.kind===undefined?a.GRAYSCALE_1BPP:e.kind;return f(e,n,r)}}();var d=function t(){function e(){this.fontSizeScale=1;this.fontWeight=u.fontWeight;this.fontSize=0;this.textMatrix=n;this.fontMatrix=r;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRise=0;this.fillColor=u.fillColor;this.strokeColor="#000000";this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.lineJoin="";this.lineCap="";this.miterLimit=0;this.dashArray=[];this.dashPhase=0;this.dependencies=[];this.activeClipUrl=null;this.clipGroup=null;this.maskId=""}e.prototype={clone:function t(){return Object.create(this)},setCurrentPoint:function t(e,r){this.x=e;this.y=r}};return e}();var p=function t(){function e(t){var e=[];var r=[];var n=t.length;for(var a=0;a<n;a++){if(t[a].fn==="save"){e.push({fnId:92,fn:"group",items:[]});r.push(e);e=e[e.length-1].items;continue}if(t[a].fn==="restore"){e=r.pop()}else{e.push(t[a])}}return e}function a(t){if(t===(t|0)){return t.toString()}var e=t.toFixed(10);var r=e.length-1;if(e[r]!=="0"){return e}do{r--}while(e[r]==="0");return e.substr(0,e[r]==="."?r:r+1)}function p(t){if(t[4]===0&&t[5]===0){if(t[1]===0&&t[2]===0){if(t[0]===1&&t[3]===1){return""}return"scale("+a(t[0])+" "+a(t[3])+")"}if(t[0]===t[3]&&t[1]===-t[2]){var e=Math.acos(t[0])*180/Math.PI;return"rotate("+a(e)+")"}}else{if(t[0]===1&&t[1]===0&&t[2]===0&&t[3]===1){return"translate("+a(t[4])+" "+a(t[5])+")"}}return"matrix("+a(t[0])+" "+a(t[1])+" "+a(t[2])+" "+a(t[3])+" "+a(t[4])+" "+a(t[5])+")"}function v(t,e,r){this.current=new d;this.transformMatrix=n;this.transformStack=[];this.extraStack=[];this.commonObjs=t;this.objs=e;this.pendingEOFill=false;this.embedFonts=false;this.embeddedFonts=Object.create(null);this.cssStyle=null;this.forceDataSchema=!!r}var g="http://www.w3.org/2000/svg";var m="http://www.w3.org/XML/1998/namespace";var A="http://www.w3.org/1999/xlink";var b=["butt","round","square"];var y=["miter","round","bevel"];var x=0;var S=0;v.prototype={save:function t(){this.transformStack.push(this.transformMatrix);var e=this.current;this.extraStack.push(e);this.current=e.clone()},restore:function t(){this.transformMatrix=this.transformStack.pop();this.current=this.extraStack.pop();this.tgrp=null},group:function t(e){this.save();this.executeOpTree(e);this.restore()},loadDependencies:function t(e){var r=e.fnArray;var n=r.length;var a=e.argsArray;var s=this;for(var o=0;o<n;o++){if(i.dependency===r[o]){var l=a[o];for(var c=0,h=l.length;c<h;c++){var u=l[c];var f=u.substring(0,2)==="g_";var d;if(f){d=new Promise(function(t){s.commonObjs.get(u,t)})}else{d=new Promise(function(t){s.objs.get(u,t)})}this.current.dependencies.push(d)}}}return Promise.all(this.current.dependencies)},transform:function t(e,r,n,a,i,o){var l=[e,r,n,a,i,o];this.transformMatrix=s.transform(this.transformMatrix,l);this.tgrp=null},getSVG:function t(e,r){this.viewport=r;var a=this._initialize(r);return this.loadDependencies(e).then(function(){this.transformMatrix=n;var t=this.convertOpList(e);this.executeOpTree(t);return a}.bind(this))},convertOpList:function t(r){var n=r.argsArray;var a=r.fnArray;var s=a.length;var o=[];var l=[];for(var c in i){o[i[c]]=c}for(var h=0;h<s;h++){var u=a[h];l.push({fnId:u,fn:o[u],args:n[h]})}return e(l)},executeOpTree:function t(e){var r=e.length;for(var n=0;n<r;n++){var a=e[n].fn;var s=e[n].fnId;var o=e[n].args;switch(s|0){case i.beginText:this.beginText();break;case i.setLeading:this.setLeading(o);break;case i.setLeadingMoveText:this.setLeadingMoveText(o[0],o[1]);break;case i.setFont:this.setFont(o);break;case i.showText:this.showText(o[0]);break;case i.showSpacedText:this.showText(o[0]);break;case i.endText:this.endText();break;case i.moveText:this.moveText(o[0],o[1]);break;case i.setCharSpacing:this.setCharSpacing(o[0]);break;case i.setWordSpacing:this.setWordSpacing(o[0]);break;case i.setHScale:this.setHScale(o[0]);break;case i.setTextMatrix:this.setTextMatrix(o[0],o[1],o[2],o[3],o[4],o[5]);break;case i.setLineWidth:this.setLineWidth(o[0]);break;case i.setLineJoin:this.setLineJoin(o[0]);break;case i.setLineCap:this.setLineCap(o[0]);break;case i.setMiterLimit:this.setMiterLimit(o[0]);break;case i.setFillRGBColor:this.setFillRGBColor(o[0],o[1],o[2]);break;case i.setStrokeRGBColor:this.setStrokeRGBColor(o[0],o[1],o[2]);break;case i.setDash:this.setDash(o[0],o[1]);break;case i.setGState:this.setGState(o[0]);break;case i.fill:this.fill();break;case i.eoFill:this.eoFill();break;case i.stroke:this.stroke();break;case i.fillStroke:this.fillStroke();break;case i.eoFillStroke:this.eoFillStroke();break;case i.clip:this.clip("nonzero");break;case i.eoClip:this.clip("evenodd");break;case i.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case i.paintJpegXObject:this.paintJpegXObject(o[0],o[1],o[2]);break;case i.paintImageXObject:this.paintImageXObject(o[0]);break;case i.paintInlineImageXObject:this.paintInlineImageXObject(o[0]);break;case i.paintImageMaskXObject:this.paintImageMaskXObject(o[0]);break;case i.paintFormXObjectBegin:this.paintFormXObjectBegin(o[0],o[1]);break;case i.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case i.closePath:this.closePath();break;case i.closeStroke:this.closeStroke();break;case i.closeFillStroke:this.closeFillStroke();break;case i.nextLine:this.nextLine();break;case i.transform:this.transform(o[0],o[1],o[2],o[3],o[4],o[5]);break;case i.constructPath:this.constructPath(o[0],o[1]);break;case i.endPath:this.endPath();break;case 92:this.group(e[n].items);break;default:c("Unimplemented operator "+a);break}}},setWordSpacing:function t(e){this.current.wordSpacing=e},setCharSpacing:function t(e){this.current.charSpacing=e},nextLine:function t(){this.moveText(0,this.current.leading)},setTextMatrix:function t(e,r,n,i,s,o){var l=this.current;this.current.textMatrix=this.current.lineMatrix=[e,r,n,i,s,o];this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0;l.xcoords=[];l.tspan=document.createElementNS(g,"svg:tspan");l.tspan.setAttributeNS(null,"font-family",l.fontFamily);l.tspan.setAttributeNS(null,"font-size",a(l.fontSize)+"px");l.tspan.setAttributeNS(null,"y",a(-l.y));l.txtElement=document.createElementNS(g,"svg:text");l.txtElement.appendChild(l.tspan)},beginText:function t(){this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0;this.current.textMatrix=n;this.current.lineMatrix=n;this.current.tspan=document.createElementNS(g,"svg:tspan");this.current.txtElement=document.createElementNS(g,"svg:text");this.current.txtgrp=document.createElementNS(g,"svg:g");this.current.xcoords=[]},moveText:function t(e,r){var n=this.current;this.current.x=this.current.lineX+=e;this.current.y=this.current.lineY+=r;n.xcoords=[];n.tspan=document.createElementNS(g,"svg:tspan");n.tspan.setAttributeNS(null,"font-family",n.fontFamily);n.tspan.setAttributeNS(null,"font-size",a(n.fontSize)+"px");n.tspan.setAttributeNS(null,"y",a(-n.y))},showText:function t(e){var r=this.current;var n=r.font;var i=r.fontSize;if(i===0){return}var s=r.charSpacing;var l=r.wordSpacing;var c=r.fontDirection;var h=r.textHScale*c;var f=e.length;var d=n.vertical;var v=i*r.fontMatrix[0];var g=0,A;for(A=0;A<f;++A){var b=e[A];if(b===null){g+=c*l;continue}else if(o(b)){g+=-b*i*.001;continue}r.xcoords.push(r.x+g*h);var y=b.width;var x=b.fontChar;var S=y*v+s*c;g+=S;r.tspan.textContent+=x}if(d){r.y-=g*h}else{r.x+=g*h}r.tspan.setAttributeNS(null,"x",r.xcoords.map(a).join(" "));r.tspan.setAttributeNS(null,"y",a(-r.y));r.tspan.setAttributeNS(null,"font-family",r.fontFamily);r.tspan.setAttributeNS(null,"font-size",a(r.fontSize)+"px");if(r.fontStyle!==u.fontStyle){r.tspan.setAttributeNS(null,"font-style",r.fontStyle)}if(r.fontWeight!==u.fontWeight){r.tspan.setAttributeNS(null,"font-weight",r.fontWeight)}if(r.fillColor!==u.fillColor){r.tspan.setAttributeNS(null,"fill",r.fillColor)}r.txtElement.setAttributeNS(null,"transform",p(r.textMatrix)+" scale(1, -1)");r.txtElement.setAttributeNS(m,"xml:space","preserve");r.txtElement.appendChild(r.tspan);r.txtgrp.appendChild(r.txtElement);this._ensureTransformGroup().appendChild(r.txtElement)},setLeadingMoveText:function t(e,r){this.setLeading(-r);this.moveText(e,r)},addFontStyle:function t(e){if(!this.cssStyle){this.cssStyle=document.createElementNS(g,"svg:style");this.cssStyle.setAttributeNS(null,"type","text/css");this.defs.appendChild(this.cssStyle)}var r=h(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'+e.loadedName+'";'+" src: url("+r+"); }\n"},setFont:function t(e){var n=this.current;var i=this.commonObjs.get(e[0]);var s=e[1];this.current.font=i;if(this.embedFonts&&i.data&&!this.embeddedFonts[i.loadedName]){this.addFontStyle(i);this.embeddedFonts[i.loadedName]=i}n.fontMatrix=i.fontMatrix?i.fontMatrix:r;var o=i.black?i.bold?"bolder":"bold":i.bold?"bold":"normal";var l=i.italic?"italic":"normal";if(s<0){s=-s;n.fontDirection=-1}else{n.fontDirection=1}n.fontSize=s;n.fontFamily=i.loadedName;n.fontWeight=o;n.fontStyle=l;n.tspan=document.createElementNS(g,"svg:tspan");n.tspan.setAttributeNS(null,"y",a(-n.y));n.xcoords=[]},endText:function t(){},setLineWidth:function t(e){this.current.lineWidth=e},setLineCap:function t(e){this.current.lineCap=b[e]},setLineJoin:function t(e){this.current.lineJoin=y[e]},setMiterLimit:function t(e){this.current.miterLimit=e},setStrokeRGBColor:function t(e,r,n){var a=s.makeCssRgb(e,r,n);this.current.strokeColor=a},setFillRGBColor:function t(e,r,n){var a=s.makeCssRgb(e,r,n);this.current.fillColor=a;this.current.tspan=document.createElementNS(g,"svg:tspan");this.current.xcoords=[]},setDash:function t(e,r){this.current.dashArray=e;this.current.dashPhase=r},constructPath:function t(e,r){var n=this.current;var s=n.x,o=n.y;n.path=document.createElementNS(g,"svg:path");var l=[];var c=e.length;for(var h=0,u=0;h<c;h++){switch(e[h]|0){case i.rectangle:s=r[u++];o=r[u++];var f=r[u++];var d=r[u++];var p=s+f;var v=o+d;l.push("M",a(s),a(o),"L",a(p),a(o),"L",a(p),a(v),"L",a(s),a(v),"Z");break;case i.moveTo:s=r[u++];o=r[u++];l.push("M",a(s),a(o));break;case i.lineTo:s=r[u++];o=r[u++];l.push("L",a(s),a(o));break;case i.curveTo:s=r[u+4];o=r[u+5];l.push("C",a(r[u]),a(r[u+1]),a(r[u+2]),a(r[u+3]),a(s),a(o));u+=6;break;case i.curveTo2:s=r[u+2];o=r[u+3];l.push("C",a(s),a(o),a(r[u]),a(r[u+1]),a(r[u+2]),a(r[u+3]));u+=4;break;case i.curveTo3:s=r[u+2];o=r[u+3];l.push("C",a(r[u]),a(r[u+1]),a(s),a(o),a(s),a(o));u+=4;break;case i.closePath:l.push("Z");break}}n.path.setAttributeNS(null,"d",l.join(" "));n.path.setAttributeNS(null,"stroke-miterlimit",a(n.miterLimit));n.path.setAttributeNS(null,"stroke-linecap",n.lineCap);n.path.setAttributeNS(null,"stroke-linejoin",n.lineJoin);n.path.setAttributeNS(null,"stroke-width",a(n.lineWidth)+"px");n.path.setAttributeNS(null,"stroke-dasharray",n.dashArray.map(a).join(" "));n.path.setAttributeNS(null,"stroke-dashoffset",a(n.dashPhase)+"px");n.path.setAttributeNS(null,"fill","none");this._ensureTransformGroup().appendChild(n.path);n.element=n.path;n.setCurrentPoint(s,o)},endPath:function t(){},clip:function t(e){var r=this.current;var n="clippath"+x;x++;var a=document.createElementNS(g,"svg:clipPath");a.setAttributeNS(null,"id",n);a.setAttributeNS(null,"transform",p(this.transformMatrix));var i=r.element.cloneNode();if(e==="evenodd"){i.setAttributeNS(null,"clip-rule","evenodd")}else{i.setAttributeNS(null,"clip-rule","nonzero")}a.appendChild(i);this.defs.appendChild(a);if(r.activeClipUrl){r.clipGroup=null;this.extraStack.forEach(function(t){t.clipGroup=null})}r.activeClipUrl="url(#"+n+")";this.tgrp=null},closePath:function t(){var e=this.current;var r=e.path.getAttributeNS(null,"d");r+="Z";e.path.setAttributeNS(null,"d",r)},setLeading:function t(e){this.current.leading=-e},setTextRise:function t(e){this.current.textRise=e},setHScale:function t(e){this.current.textHScale=e/100},setGState:function t(e){for(var r=0,n=e.length;r<n;r++){var a=e[r];var i=a[0];var s=a[1];switch(i){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"Font":this.setFont(s);break;default:c("Unimplemented graphic state "+i);break}}},fill:function t(){var e=this.current;e.element.setAttributeNS(null,"fill",e.fillColor)},stroke:function t(){var e=this.current;e.element.setAttributeNS(null,"stroke",e.strokeColor);e.element.setAttributeNS(null,"fill","none")},eoFill:function t(){var e=this.current;e.element.setAttributeNS(null,"fill",e.fillColor);e.element.setAttributeNS(null,"fill-rule","evenodd")},fillStroke:function t(){this.stroke();this.fill()},eoFillStroke:function t(){this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fillStroke()},closeStroke:function t(){this.closePath();this.stroke()},closeFillStroke:function t(){this.closePath();this.fillStroke()},paintSolidColorImageMask:function t(){var e=this.current;var r=document.createElementNS(g,"svg:rect");r.setAttributeNS(null,"x","0");r.setAttributeNS(null,"y","0");r.setAttributeNS(null,"width","1px");r.setAttributeNS(null,"height","1px");r.setAttributeNS(null,"fill",e.fillColor);this._ensureTransformGroup().appendChild(r)},paintJpegXObject:function t(e,r,n){var i=this.objs.get(e);var s=document.createElementNS(g,"svg:image");s.setAttributeNS(A,"xlink:href",i.src);s.setAttributeNS(null,"width",i.width+"px");s.setAttributeNS(null,"height",i.height+"px");s.setAttributeNS(null,"x","0");s.setAttributeNS(null,"y",a(-n));s.setAttributeNS(null,"transform","scale("+a(1/r)+" "+a(-1/n)+")");this._ensureTransformGroup().appendChild(s)},paintImageXObject:function t(e){var r=this.objs.get(e);if(!r){c("Dependent image isn't ready yet");return}this.paintInlineImageXObject(r)},paintInlineImageXObject:function t(e,r){var n=e.width;var i=e.height;var s=f(e,this.forceDataSchema);var o=document.createElementNS(g,"svg:rect");o.setAttributeNS(null,"x","0");o.setAttributeNS(null,"y","0");o.setAttributeNS(null,"width",a(n));o.setAttributeNS(null,"height",a(i));this.current.element=o;this.clip("nonzero");var l=document.createElementNS(g,"svg:image");l.setAttributeNS(A,"xlink:href",s);l.setAttributeNS(null,"x","0");l.setAttributeNS(null,"y",a(-i));l.setAttributeNS(null,"width",a(n)+"px");l.setAttributeNS(null,"height",a(i)+"px");l.setAttributeNS(null,"transform","scale("+a(1/n)+" "+a(-1/i)+")");if(r){r.appendChild(l)}else{this._ensureTransformGroup().appendChild(l)}},paintImageMaskXObject:function t(e){var r=this.current;var n=e.width;var i=e.height;var s=r.fillColor;r.maskId="mask"+S++;var o=document.createElementNS(g,"svg:mask");o.setAttributeNS(null,"id",r.maskId);var l=document.createElementNS(g,"svg:rect");l.setAttributeNS(null,"x","0");l.setAttributeNS(null,"y","0");l.setAttributeNS(null,"width",a(n));l.setAttributeNS(null,"height",a(i));l.setAttributeNS(null,"fill",s);l.setAttributeNS(null,"mask","url(#"+r.maskId+")");this.defs.appendChild(o);this._ensureTransformGroup().appendChild(l);this.paintInlineImageXObject(e,o)},paintFormXObjectBegin:function t(e,r){if(l(e)&&e.length===6){this.transform(e[0],e[1],e[2],e[3],e[4],e[5])}if(l(r)&&r.length===4){var n=r[2]-r[0];var i=r[3]-r[1];var s=document.createElementNS(g,"svg:rect");s.setAttributeNS(null,"x",r[0]);s.setAttributeNS(null,"y",r[1]);s.setAttributeNS(null,"width",a(n));s.setAttributeNS(null,"height",a(i));this.current.element=s;this.clip("nonzero");this.endPath()}},paintFormXObjectEnd:function t(){},_initialize:function t(e){var r=document.createElementNS(g,"svg:svg");r.setAttributeNS(null,"version","1.1");r.setAttributeNS(null,"width",e.width+"px");r.setAttributeNS(null,"height",e.height+"px");r.setAttributeNS(null,"preserveAspectRatio","none");r.setAttributeNS(null,"viewBox","0 0 "+e.width+" "+e.height);var n=document.createElementNS(g,"svg:defs");r.appendChild(n);this.defs=n;var a=document.createElementNS(g,"svg:g");a.setAttributeNS(null,"transform",p(e.transform));r.appendChild(a);this.svg=a;return r},_ensureClipGroup:function t(){if(!this.current.clipGroup){var e=document.createElementNS(g,"svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl);this.svg.appendChild(e);this.current.clipGroup=e}return this.current.clipGroup},_ensureTransformGroup:function t(){if(!this.tgrp){this.tgrp=document.createElementNS(g,"svg:g");this.tgrp.setAttributeNS(null,"transform",p(this.transformMatrix));if(this.current.activeClipUrl){this._ensureClipGroup().appendChild(this.tgrp)}else{this.svg.appendChild(this.tgrp)}}return this.tgrp}};return v}();t.SVGGraphics=p});(function(t,e){e(t.pdfjsDisplayAnnotationLayer={},t.pdfjsSharedUtil,t.pdfjsDisplayDOMUtils)})(this,function(t,e,r){var n=e.AnnotationBorderStyleType;var a=e.AnnotationType;var i=e.Util;var s=r.addLinkAttributes;var o=r.LinkTarget;var l=r.getFilenameFromUrl;var c=e.warn;var h=r.CustomStyle;var u=r.getDefaultSetting;function f(){}f.prototype={create:function t(e){var r=e.data.annotationType;switch(r){case a.LINK:return new p(e);case a.TEXT:return new v(e);case a.WIDGET:var n=e.data.fieldType;switch(n){case"Tx":return new m(e);case"Btn":if(e.data.radioButton){return new b(e)}else if(e.data.checkBox){return new A(e)}c("Unimplemented button widget annotation: pushbutton");break;case"Ch":return new y(e)}return new g(e);case a.POPUP:return new x(e);case a.HIGHLIGHT:return new k(e);case a.UNDERLINE:return new w(e);case a.SQUIGGLY:return new _(e);case a.STRIKEOUT:return new C(e);case a.FILEATTACHMENT:return new T(e);default:return new d(e)}}};var d=function t(){function e(t,e){this.isRenderable=e||false;this.data=t.data;this.layer=t.layer;this.page=t.page;this.viewport=t.viewport;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderInteractiveForms=t.renderInteractiveForms;if(e){this.container=this._createContainer()}}e.prototype={_createContainer:function t(){var e=this.data,r=this.page,a=this.viewport;var s=document.createElement("section");var o=e.rect[2]-e.rect[0];var l=e.rect[3]-e.rect[1];s.setAttribute("data-annotation-id",e.id);var u=i.normalizeRect([e.rect[0],r.view[3]-e.rect[1]+r.view[1],e.rect[2],r.view[3]-e.rect[3]+r.view[1]]);h.setProp("transform",s,"matrix("+a.transform.join(",")+")");h.setProp("transformOrigin",s,-u[0]+"px "+-u[1]+"px");if(e.borderStyle.width>0){s.style.borderWidth=e.borderStyle.width+"px";if(e.borderStyle.style!==n.UNDERLINE){o=o-2*e.borderStyle.width;l=l-2*e.borderStyle.width}var f=e.borderStyle.horizontalCornerRadius;var d=e.borderStyle.verticalCornerRadius;if(f>0||d>0){var p=f+"px / "+d+"px";h.setProp("borderRadius",s,p)}switch(e.borderStyle.style){case n.SOLID:s.style.borderStyle="solid";break;case n.DASHED:s.style.borderStyle="dashed";break;case n.BEVELED:c("Unimplemented border style: beveled");break;case n.INSET:c("Unimplemented border style: inset");break;case n.UNDERLINE:s.style.borderBottomStyle="solid";break;default:break}if(e.color){s.style.borderColor=i.makeCssRgb(e.color[0]|0,e.color[1]|0,e.color[2]|0)}else{s.style.borderWidth=0}}s.style.left=u[0]+"px";s.style.top=u[1]+"px";s.style.width=o+"px";s.style.height=l+"px";return s},_createPopup:function t(e,r,n){if(!r){r=document.createElement("div");r.style.height=e.style.height;r.style.width=e.style.width;e.appendChild(r)}var a=new S({container:e,trigger:r,color:n.color,title:n.title,contents:n.contents,hideWrapper:true});var i=a.render();i.style.left=e.style.width;e.appendChild(i)},render:function t(){throw new Error("Abstract method AnnotationElement.render called")}};return e}();var p=function t(){function e(t){d.call(this,t,true)}i.inherit(e,d,{render:function t(){this.container.className="linkAnnotation";var e=document.createElement("a");s(e,{url:this.data.url,target:this.data.newWindow?o.BLANK:undefined});if(!this.data.url){if(this.data.action){this._bindNamedAction(e,this.data.action)}else{this._bindLink(e,this.data.dest)}}this.container.appendChild(e);return this.container},_bindLink:function t(e,r){var n=this;e.href=this.linkService.getDestinationHash(r);e.onclick=function(){if(r){n.linkService.navigateTo(r)}return false};if(r){e.className="internalLink"}},_bindNamedAction:function t(e,r){var n=this;e.href=this.linkService.getAnchorUrl("");e.onclick=function(){n.linkService.executeNamedAction(r);return false};e.className="internalLink"}});return e}();var v=function t(){function e(t){var e=!!(t.data.hasPopup||t.data.title||t.data.contents);d.call(this,t,e)}i.inherit(e,d,{render:function t(){this.container.className="textAnnotation";var e=document.createElement("img");e.style.height=this.container.style.height;e.style.width=this.container.style.width;e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";e.alt="[{{type}} Annotation]";e.dataset.l10nId="text_annotation_type";e.dataset.l10nArgs=JSON.stringify({type:this.data.name});if(!this.data.hasPopup){this._createPopup(this.container,e,this.data)}this.container.appendChild(e);return this.container}});return e}();var g=function t(){function e(t,e){d.call(this,t,e)}i.inherit(e,d,{render:function t(){return this.container}});return e}();var m=function t(){var e=["left","center","right"];function r(t){var e=t.renderInteractiveForms||!t.data.hasAppearance&&!!t.data.fieldValue;g.call(this,t,e)}i.inherit(r,g,{render:function t(){this.container.className="textWidgetAnnotation";var r=null;if(this.renderInteractiveForms){if(this.data.multiLine){r=document.createElement("textarea");r.textContent=this.data.fieldValue}else{r=document.createElement("input");r.type="text";r.setAttribute("value",this.data.fieldValue)}r.disabled=this.data.readOnly;if(this.data.maxLen!==null){r.maxLength=this.data.maxLen}if(this.data.comb){var n=this.data.rect[2]-this.data.rect[0];var a=n/this.data.maxLen;r.classList.add("comb");r.style.letterSpacing="calc("+a+"px - 1ch)"}}else{r=document.createElement("div");r.textContent=this.data.fieldValue;r.style.verticalAlign="middle";r.style.display="table-cell";var i=null;if(this.data.fontRefName){i=this.page.commonObjs.getData(this.data.fontRefName)}this._setTextStyle(r,i)}if(this.data.textAlignment!==null){r.style.textAlign=e[this.data.textAlignment]}this.container.appendChild(r);return this.container},_setTextStyle:function t(e,r){var n=e.style;n.fontSize=this.data.fontSize+"px";n.direction=this.data.fontDirection<0?"rtl":"ltr";if(!r){return}n.fontWeight=r.black?r.bold?"900":"bold":r.bold?"bold":"normal";n.fontStyle=r.italic?"italic":"normal";var a=r.loadedName?'"'+r.loadedName+'", ':"";var i=r.fallbackName||"Helvetica, sans-serif";n.fontFamily=a+i}});return r}();var A=function t(){function e(t){g.call(this,t,t.renderInteractiveForms)}i.inherit(e,g,{render:function t(){this.container.className="buttonWidgetAnnotation checkBox";var e=document.createElement("input");e.disabled=this.data.readOnly;e.type="checkbox";if(this.data.fieldValue&&this.data.fieldValue!=="Off"){e.setAttribute("checked",true)}this.container.appendChild(e);return this.container}});return e}();var b=function t(){function e(t){g.call(this,t,t.renderInteractiveForms)}i.inherit(e,g,{render:function t(){this.container.className="buttonWidgetAnnotation radioButton";var e=document.createElement("input");e.disabled=this.data.readOnly;e.type="radio";e.name=this.data.fieldName;if(this.data.fieldValue===this.data.buttonValue){e.setAttribute("checked",true)}this.container.appendChild(e);return this.container}});return e}();var y=function t(){function e(t){g.call(this,t,t.renderInteractiveForms)}i.inherit(e,g,{render:function t(){this.container.className="choiceWidgetAnnotation";var e=document.createElement("select");e.disabled=this.data.readOnly;if(!this.data.combo){e.size=this.data.options.length;if(this.data.multiSelect){e.multiple=true}}for(var r=0,n=this.data.options.length;r<n;r++){var a=this.data.options[r];var i=document.createElement("option");i.textContent=a.displayValue;i.value=a.exportValue;if(this.data.fieldValue.indexOf(a.displayValue)>=0){i.setAttribute("selected",true)}e.appendChild(i)}this.container.appendChild(e);return this.container}});return e}();var x=function t(){function e(t){var e=!!(t.data.title||t.data.contents);d.call(this,t,e)}i.inherit(e,d,{render:function t(){this.container.className="popupAnnotation";var e='[data-annotation-id="'+this.data.parentId+'"]';var r=this.layer.querySelector(e);if(!r){return this.container}var n=new S({container:this.container,trigger:r,color:this.data.color,title:this.data.title,contents:this.data.contents});var a=parseFloat(r.style.left);var i=parseFloat(r.style.width);h.setProp("transformOrigin",this.container,-(a+i)+"px -"+r.style.top);this.container.style.left=a+i+"px";this.container.appendChild(n.render());return this.container}});return e}();var S=function t(){var e=.7;function r(t){this.container=t.container;this.trigger=t.trigger;this.color=t.color;this.title=t.title;this.contents=t.contents;this.hideWrapper=t.hideWrapper||false;this.pinned=false}r.prototype={render:function t(){var r=document.createElement("div");r.className="popupWrapper";this.hideElement=this.hideWrapper?r:this.container;this.hideElement.setAttribute("hidden",true);var n=document.createElement("div");n.className="popup";var a=this.color;if(a){var s=e*(255-a[0])+a[0];var o=e*(255-a[1])+a[1];var l=e*(255-a[2])+a[2];n.style.backgroundColor=i.makeCssRgb(s|0,o|0,l|0)}var c=this._formatContents(this.contents);var h=document.createElement("h1");h.textContent=this.title;this.trigger.addEventListener("click",this._toggle.bind(this));this.trigger.addEventListener("mouseover",this._show.bind(this,false));this.trigger.addEventListener("mouseout",this._hide.bind(this,false));n.addEventListener("click",this._hide.bind(this,true));n.appendChild(h);n.appendChild(c);r.appendChild(n);return r},_formatContents:function t(e){var r=document.createElement("p");var n=e.split(/(?:\r\n?|\n)/);for(var a=0,i=n.length;a<i;++a){var s=n[a];r.appendChild(document.createTextNode(s));if(a<i-1){r.appendChild(document.createElement("br"))}}return r},_toggle:function t(){if(this.pinned){this._hide(true)}else{this._show(true)}},_show:function t(e){if(e){this.pinned=true}if(this.hideElement.hasAttribute("hidden")){this.hideElement.removeAttribute("hidden");this.container.style.zIndex+=1}},_hide:function t(e){if(e){this.pinned=false}if(!this.hideElement.hasAttribute("hidden")&&!this.pinned){this.hideElement.setAttribute("hidden",true);this.container.style.zIndex-=1}}};return r}();var k=function t(){function e(t){var e=!!(t.data.hasPopup||t.data.title||t.data.contents);d.call(this,t,e)}i.inherit(e,d,{render:function t(){this.container.className="highlightAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}});return e}();var w=function t(){function e(t){var e=!!(t.data.hasPopup||t.data.title||t.data.contents);d.call(this,t,e)}i.inherit(e,d,{render:function t(){this.container.className="underlineAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}});return e}();var _=function t(){function e(t){var e=!!(t.data.hasPopup||t.data.title||t.data.contents);d.call(this,t,e)}i.inherit(e,d,{render:function t(){this.container.className="squigglyAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}});return e}();var C=function t(){function e(t){var e=!!(t.data.hasPopup||t.data.title||t.data.contents);d.call(this,t,e)}i.inherit(e,d,{render:function t(){this.container.className="strikeoutAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}});return e}();var T=function t(){function e(t){d.call(this,t,true);this.filename=l(t.data.file.filename);this.content=t.data.file.content}i.inherit(e,d,{render:function t(){this.container.className="fileAttachmentAnnotation";var e=document.createElement("div");e.style.height=this.container.style.height;e.style.width=this.container.style.width;e.addEventListener("dblclick",this._download.bind(this));if(!this.data.hasPopup&&(this.data.title||this.data.contents)){this._createPopup(this.container,e,this.data)}this.container.appendChild(e);return this.container},_download:function t(){if(!this.downloadManager){c("Download cannot be started due to unavailable download manager");return}this.downloadManager.downloadData(this.content,this.filename,"")}});return e}();var L=function t(){return{render:function t(e){var r=new f;for(var n=0,a=e.annotations.length;n<a;n++){var i=e.annotations[n];if(!i){continue}var s={data:i,layer:e.div,page:e.page,viewport:e.viewport,linkService:e.linkService,downloadManager:e.downloadManager,imageResourcesPath:e.imageResourcesPath||u("imageResourcesPath"),renderInteractiveForms:e.renderInteractiveForms||false};var o=r.create(s);if(o.isRenderable){e.div.appendChild(o.render())}}},update:function t(e){for(var r=0,n=e.annotations.length;r<n;r++){var a=e.annotations[r];var i=e.div.querySelector('[data-annotation-id="'+a.id+'"]');if(i){h.setProp("transform",i,"matrix("+e.viewport.transform.join(",")+")")}}e.div.removeAttribute("hidden")}}}();t.AnnotationLayer=L});(function(t,e){e(t.pdfjsDisplayTextLayer={},t.pdfjsSharedUtil,t.pdfjsDisplayDOMUtils)})(this,function(t,e,r){var n=e.Util;var a=e.createPromiseCapability;var i=r.CustomStyle;var s=r.getDefaultSetting;var o=function t(){var e=1e5;var r=/\S/;function o(t){return!r.test(t)}var l=["left: ",0,"px; top: ",0,"px; font-size: ",0,"px; font-family: ","",";"];function c(t,e,r){var a=document.createElement("div");var i={style:null,angle:0,canvasWidth:0,isWhitespace:false,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};t._textDivs.push(a);if(o(e.str)){i.isWhitespace=true;t._textDivProperties.set(a,i);return}var c=n.transform(t._viewport.transform,e.transform);var h=Math.atan2(c[1],c[0]);var u=r[e.fontName];if(u.vertical){h+=Math.PI/2}var f=Math.sqrt(c[2]*c[2]+c[3]*c[3]);var d=f;if(u.ascent){d=u.ascent*d}else if(u.descent){d=(1+u.descent)*d}var p;var v;if(h===0){p=c[4];v=c[5]-d}else{p=c[4]+d*Math.sin(h);v=c[5]-d*Math.cos(h)}l[1]=p;l[3]=v;l[5]=f;l[7]=u.fontFamily;i.style=l.join("");a.setAttribute("style",i.style);a.textContent=e.str;if(s("pdfBug")){a.dataset.fontName=e.fontName}if(h!==0){i.angle=h*(180/Math.PI)}if(e.str.length>1){if(u.vertical){i.canvasWidth=e.height*t._viewport.scale}else{i.canvasWidth=e.width*t._viewport.scale}}t._textDivProperties.set(a,i);if(t._enhanceTextSelection){var g=1,m=0;if(h!==0){g=Math.cos(h);m=Math.sin(h)}var A=(u.vertical?e.height:e.width)*t._viewport.scale;var b=f;var y,x;if(h!==0){y=[g,m,-m,g,p,v];x=n.getAxialAlignedBoundingBox([0,0,A,b],y)}else{x=[p,v,p+A,v+b]}t._bounds.push({left:x[0],top:x[1],right:x[2],bottom:x[3],div:a,size:[A,b],m:y})}}function h(t){if(t._canceled){return}var r=t._container;var n=t._textDivs;var a=t._capability;var s=n.length;if(s>e){t._renderingDone=true;a.resolve();return}var o=document.createElement("canvas");o.mozOpaque=true;var l=o.getContext("2d",{alpha:false});var c;var h;for(var u=0;u<s;u++){var f=n[u];var d=t._textDivProperties.get(f);if(d.isWhitespace){continue}var p=f.style.fontSize;var v=f.style.fontFamily;if(p!==c||v!==h){l.font=p+" "+v;c=p;h=v}var g=l.measureText(f.textContent).width;r.appendChild(f);var m="";if(d.canvasWidth!==0&&g>0){d.scale=d.canvasWidth/g;m="scaleX("+d.scale+")"}if(d.angle!==0){m="rotate("+d.angle+"deg) "+m}if(m!==""){d.originalTransform=m;i.setProp("transform",f,m)}t._textDivProperties.set(f,d)}t._renderingDone=true;a.resolve()}function u(t){var e=t._bounds;var r=t._viewport;var a=f(r.width,r.height,e);for(var i=0;i<a.length;i++){var s=e[i].div;var o=t._textDivProperties.get(s);if(o.angle===0){o.paddingLeft=e[i].left-a[i].left;o.paddingTop=e[i].top-a[i].top;o.paddingRight=a[i].right-e[i].right;o.paddingBottom=a[i].bottom-e[i].bottom;t._textDivProperties.set(s,o);continue}var l=a[i],c=e[i];var h=c.m,u=h[0],d=h[1];var p=[[0,0],[0,c.size[1]],[c.size[0],0],c.size];var v=new Float64Array(64);p.forEach(function(t,e){var r=n.applyTransform(t,h);v[e+0]=u&&(l.left-r[0])/u;v[e+4]=d&&(l.top-r[1])/d;v[e+8]=u&&(l.right-r[0])/u;v[e+12]=d&&(l.bottom-r[1])/d;v[e+16]=d&&(l.left-r[0])/-d;v[e+20]=u&&(l.top-r[1])/u;v[e+24]=d&&(l.right-r[0])/-d;v[e+28]=u&&(l.bottom-r[1])/u;v[e+32]=u&&(l.left-r[0])/-u;v[e+36]=d&&(l.top-r[1])/-d;v[e+40]=u&&(l.right-r[0])/-u;v[e+44]=d&&(l.bottom-r[1])/-d;v[e+48]=d&&(l.left-r[0])/d;v[e+52]=u&&(l.top-r[1])/-u;v[e+56]=d&&(l.right-r[0])/d;v[e+60]=u&&(l.bottom-r[1])/-u});var g=function(t,e,r){var n=0;for(var a=0;a<r;a++){var i=t[e++];if(i>0){n=n?Math.min(i,n):i}}return n};var m=1+Math.min(Math.abs(u),Math.abs(d));o.paddingLeft=g(v,32,16)/m;o.paddingTop=g(v,48,16)/m;o.paddingRight=g(v,0,16)/m;o.paddingBottom=g(v,16,16)/m;t._textDivProperties.set(s,o)}}function f(t,e,r){var n=r.map(function(t,e){return{x1:t.left,y1:t.top,x2:t.right,y2:t.bottom,index:e,x1New:undefined,x2New:undefined}});d(t,n);var a=new Array(r.length);n.forEach(function(t){var e=t.index;a[e]={left:t.x1New,top:0,right:t.x2New,bottom:0}});r.map(function(e,r){var i=a[r],s=n[r];s.x1=e.top;s.y1=t-i.right;s.x2=e.bottom;s.y2=t-i.left;s.index=r;s.x1New=undefined;s.x2New=undefined});d(e,n);n.forEach(function(t){var e=t.index;a[e].top=t.x1New;a[e].bottom=t.x2New});return a}function d(t,e){e.sort(function(t,e){return t.x1-e.x1||t.index-e.index});var r={x1:-Infinity,y1:-Infinity,x2:0,y2:Infinity,index:-1,x1New:0,x2New:0};var n=[{start:-Infinity,end:Infinity,boundary:r}];e.forEach(function(t){var e=0;while(e<n.length&&n[e].end<=t.y1){e++}var r=n.length-1;while(r>=0&&n[r].start>=t.y2){r--}var a,i;var s,o,l=-Infinity;for(s=e;s<=r;s++){a=n[s];i=a.boundary;var c;if(i.x2>t.x1){c=i.index>t.index?i.x1New:t.x1}else if(i.x2New===undefined){c=(i.x2+t.x1)/2}else{c=i.x2New}if(c>l){l=c}}t.x1New=l;for(s=e;s<=r;s++){a=n[s];i=a.boundary;if(i.x2New===undefined){if(i.x2>t.x1){if(i.index>t.index){i.x2New=i.x2}}else{i.x2New=l}}else if(i.x2New>l){i.x2New=Math.max(l,i.x2)}}var h=[],u=null;for(s=e;s<=r;s++){a=n[s];i=a.boundary;var f=i.x2>t.x2?i:t;if(u===f){h[h.length-1].end=a.end}else{h.push({start:a.start,end:a.end,boundary:f});u=f}}if(n[e].start<t.y1){h[0].start=t.y1;h.unshift({start:n[e].start,end:t.y1,boundary:n[e].boundary})}if(t.y2<n[r].end){h[h.length-1].end=t.y2;h.push({start:t.y2,end:n[r].end,boundary:n[r].boundary})}for(s=e;s<=r;s++){a=n[s];i=a.boundary;if(i.x2New!==undefined){continue}var d=false;for(o=e-1;!d&&o>=0&&n[o].start>=i.y1;o--){d=n[o].boundary===i}for(o=r+1;!d&&o<n.length&&n[o].end<=i.y2;o++){d=n[o].boundary===i}for(o=0;!d&&o<h.length;o++){d=h[o].boundary===i}if(!d){i.x2New=l}}Array.prototype.splice.apply(n,[e,r-e+1].concat(h))});n.forEach(function(e){var r=e.boundary;if(r.x2New===undefined){r.x2New=Math.max(t,r.x2)}})}function p(t,e,r,n,i){this._textContent=t;this._container=e;this._viewport=r;this._textDivs=n||[];this._textDivProperties=new WeakMap;this._renderingDone=false;this._canceled=false;this._capability=a();this._renderTimer=null;this._bounds=[];this._enhanceTextSelection=!!i}p.prototype={get promise(){return this._capability.promise},cancel:function t(){this._canceled=true;if(this._renderTimer!==null){clearTimeout(this._renderTimer);this._renderTimer=null}this._capability.reject("canceled")},_render:function t(e){var r=this._textContent.items;var n=this._textContent.styles;for(var a=0,i=r.length;a<i;a++){c(this,r[a],n)}if(!e){h(this)}else{var s=this;this._renderTimer=setTimeout(function(){h(s);s._renderTimer=null},e)}},expandTextDivs:function t(e){if(!this._enhanceTextSelection||!this._renderingDone){return}if(this._bounds!==null){u(this);this._bounds=null}for(var r=0,n=this._textDivs.length;r<n;r++){var a=this._textDivs[r];var s=this._textDivProperties.get(a);if(s.isWhitespace){continue}if(e){var o="",l="";if(s.scale!==1){o="scaleX("+s.scale+")"}if(s.angle!==0){o="rotate("+s.angle+"deg) "+o}if(s.paddingLeft!==0){l+=" padding-left: "+s.paddingLeft/s.scale+"px;";o+=" translateX("+-s.paddingLeft/s.scale+"px)"}if(s.paddingTop!==0){l+=" padding-top: "+s.paddingTop+"px;";o+=" translateY("+-s.paddingTop+"px)"}if(s.paddingRight!==0){l+=" padding-right: "+s.paddingRight/s.scale+"px;"}if(s.paddingBottom!==0){l+=" padding-bottom: "+s.paddingBottom+"px;"}if(l!==""){a.setAttribute("style",s.style+l)}if(o!==""){i.setProp("transform",a,o)}}else{a.style.padding=0;i.setProp("transform",a,s.originalTransform||"")}}}};function v(t){var e=new p(t.textContent,t.container,t.viewport,t.textDivs,t.enhanceTextSelection);e._render(t.timeout);return e}return v}();t.renderTextLayer=o});(function(t,e){e(t.pdfjsDisplayWebGL={},t.pdfjsSharedUtil,t.pdfjsDisplayDOMUtils)})(this,function(t,e,r){var n=e.shadow;var a=r.getDefaultSetting;var i=function t(){function e(t,e,r){var n=t.createShader(r);t.shaderSource(n,e);t.compileShader(n);var a=t.getShaderParameter(n,t.COMPILE_STATUS);if(!a){var i=t.getShaderInfoLog(n);throw new Error("Error during shader compilation: "+i)}return n}function r(t,r){return e(t,r,t.VERTEX_SHADER)}function i(t,r){return e(t,r,t.FRAGMENT_SHADER)}function s(t,e){var r=t.createProgram();for(var n=0,a=e.length;n<a;++n){t.attachShader(r,e[n])}t.linkProgram(r);var i=t.getProgramParameter(r,t.LINK_STATUS);if(!i){var s=t.getProgramInfoLog(r);throw new Error("Error during program linking: "+s)}return r}function o(t,e,r){t.activeTexture(r);var n=t.createTexture();t.bindTexture(t.TEXTURE_2D,n);t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE);t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE);t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.NEAREST);t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.NEAREST);t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,e);return n}var l,c;function h(){if(l){return}c=document.createElement("canvas");l=c.getContext("webgl",{premultipliedalpha:false})}var u="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ";var f="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ";var d=null;function p(){var t,e;h();t=c;c=null;e=l;l=null;var n=r(e,u);var a=i(e,f);var o=s(e,[n,a]);e.useProgram(o);var p={};p.gl=e;p.canvas=t;p.resolutionLocation=e.getUniformLocation(o,"u_resolution");p.positionLocation=e.getAttribLocation(o,"a_position");p.backdropLocation=e.getUniformLocation(o,"u_backdrop");p.subtypeLocation=e.getUniformLocation(o,"u_subtype");var v=e.getAttribLocation(o,"a_texCoord");var g=e.getUniformLocation(o,"u_image");var m=e.getUniformLocation(o,"u_mask");var A=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,A);e.bufferData(e.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),e.STATIC_DRAW);e.enableVertexAttribArray(v);e.vertexAttribPointer(v,2,e.FLOAT,false,0,0);e.uniform1i(g,0);e.uniform1i(m,1);d=p}function v(t,e,r){var n=t.width,a=t.height;if(!d){p()}var i=d,s=i.canvas,l=i.gl;s.width=n;s.height=a;l.viewport(0,0,l.drawingBufferWidth,l.drawingBufferHeight);l.uniform2f(i.resolutionLocation,n,a);if(r.backdrop){l.uniform4f(i.resolutionLocation,r.backdrop[0],r.backdrop[1],r.backdrop[2],1)}else{l.uniform4f(i.resolutionLocation,0,0,0,0)}l.uniform1i(i.subtypeLocation,r.subtype==="Luminosity"?1:0);var c=o(l,t,l.TEXTURE0);var h=o(l,e,l.TEXTURE1);var u=l.createBuffer();l.bindBuffer(l.ARRAY_BUFFER,u);l.bufferData(l.ARRAY_BUFFER,new Float32Array([0,0,n,0,0,a,0,a,n,0,n,a]),l.STATIC_DRAW);l.enableVertexAttribArray(i.positionLocation);l.vertexAttribPointer(i.positionLocation,2,l.FLOAT,false,0,0);l.clearColor(0,0,0,0);l.enable(l.BLEND);l.blendFunc(l.ONE,l.ONE_MINUS_SRC_ALPHA);l.clear(l.COLOR_BUFFER_BIT);l.drawArrays(l.TRIANGLES,0,6);l.flush();l.deleteTexture(c);l.deleteTexture(h);l.deleteBuffer(u);return s}var g="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ";var m="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ";var A=null;function b(){var t,e;h();t=c;c=null;e=l;l=null;var n=r(e,g);var a=i(e,m);var o=s(e,[n,a]);e.useProgram(o);var u={};u.gl=e;u.canvas=t;u.resolutionLocation=e.getUniformLocation(o,"u_resolution");u.scaleLocation=e.getUniformLocation(o,"u_scale");u.offsetLocation=e.getUniformLocation(o,"u_offset");u.positionLocation=e.getAttribLocation(o,"a_position");u.colorLocation=e.getAttribLocation(o,"a_color");A=u}function y(t,e,r,n,a){if(!A){b()}var i=A,s=i.canvas,o=i.gl;s.width=t;s.height=e;o.viewport(0,0,o.drawingBufferWidth,o.drawingBufferHeight);o.uniform2f(i.resolutionLocation,t,e);var l=0;var c,h,u;for(c=0,h=n.length;c<h;c++){switch(n[c].type){case"lattice":u=n[c].coords.length/n[c].verticesPerRow|0;l+=(u-1)*(n[c].verticesPerRow-1)*6;break;case"triangles":l+=n[c].coords.length;break}}var f=new Float32Array(l*2);var d=new Uint8Array(l*3);var p=a.coords,v=a.colors;var g=0,m=0;for(c=0,h=n.length;c<h;c++){var y=n[c],x=y.coords,S=y.colors;switch(y.type){case"lattice":var k=y.verticesPerRow;u=x.length/k|0;for(var w=1;w<u;w++){var _=w*k+1;for(var C=1;C<k;C++,_++){f[g]=p[x[_-k-1]];f[g+1]=p[x[_-k-1]+1];f[g+2]=p[x[_-k]];f[g+3]=p[x[_-k]+1];f[g+4]=p[x[_-1]];f[g+5]=p[x[_-1]+1];d[m]=v[S[_-k-1]];d[m+1]=v[S[_-k-1]+1];d[m+2]=v[S[_-k-1]+2];d[m+3]=v[S[_-k]];d[m+4]=v[S[_-k]+1];d[m+5]=v[S[_-k]+2];d[m+6]=v[S[_-1]];d[m+7]=v[S[_-1]+1];d[m+8]=v[S[_-1]+2];f[g+6]=f[g+2];f[g+7]=f[g+3];f[g+8]=f[g+4];f[g+9]=f[g+5];f[g+10]=p[x[_]];f[g+11]=p[x[_]+1];d[m+9]=d[m+3];d[m+10]=d[m+4];d[m+11]=d[m+5];d[m+12]=d[m+6];d[m+13]=d[m+7];d[m+14]=d[m+8];d[m+15]=v[S[_]];d[m+16]=v[S[_]+1];d[m+17]=v[S[_]+2];g+=12;m+=18}}break;case"triangles":for(var T=0,L=x.length;T<L;T++){f[g]=p[x[T]];f[g+1]=p[x[T]+1];d[m]=v[S[T]];d[m+1]=v[S[T]+1];d[m+2]=v[S[T]+2];g+=2;m+=3}break}}if(r){o.clearColor(r[0]/255,r[1]/255,r[2]/255,1)}else{o.clearColor(0,0,0,0)}o.clear(o.COLOR_BUFFER_BIT);var P=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,P);o.bufferData(o.ARRAY_BUFFER,f,o.STATIC_DRAW);o.enableVertexAttribArray(i.positionLocation);o.vertexAttribPointer(i.positionLocation,2,o.FLOAT,false,0,0);var E=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,E);o.bufferData(o.ARRAY_BUFFER,d,o.STATIC_DRAW);o.enableVertexAttribArray(i.colorLocation);o.vertexAttribPointer(i.colorLocation,3,o.UNSIGNED_BYTE,false,0,0);o.uniform2f(i.scaleLocation,a.scaleX,a.scaleY);o.uniform2f(i.offsetLocation,a.offsetX,a.offsetY);o.drawArrays(o.TRIANGLES,0,l);o.flush();o.deleteBuffer(P);o.deleteBuffer(E);return s}function x(){if(d&&d.canvas){d.canvas.width=0;d.canvas.height=0}if(A&&A.canvas){A.canvas.width=0;A.canvas.height=0}d=null;A=null}return{get isEnabled(){if(a("disableWebGL")){return false}var t=false;try{h();t=!!l}catch(t){}return n(this,"isEnabled",t)},composeSMask:v,drawFigures:y,clear:x}}();t.WebGLUtils=i});(function(t,e){e(t.pdfjsDisplayPatternHelper={},t.pdfjsSharedUtil,t.pdfjsDisplayWebGL)})(this,function(t,e,r){var n=e.Util;var a=e.info;var i=e.isArray;var s=e.error;var o=r.WebGLUtils;var l={};l.RadialAxial={fromIR:function t(e){var r=e[1];var n=e[2];var a=e[3];var i=e[4];var s=e[5];var o=e[6];return{type:"Pattern",getPattern:function t(e){var l;if(r==="axial"){l=e.createLinearGradient(a[0],a[1],i[0],i[1])}else if(r==="radial"){l=e.createRadialGradient(a[0],a[1],s,i[0],i[1],o)}for(var c=0,h=n.length;c<h;++c){var u=n[c];l.addColorStop(u[0],u[1])}return l}}}};var c=function t(){function e(t,e,r,n,a,i,s,o){var l=e.coords,c=e.colors;var h=t.data,u=t.width*4;var f;if(l[r+1]>l[n+1]){f=r;r=n;n=f;f=i;i=s;s=f}if(l[n+1]>l[a+1]){f=n;n=a;a=f;f=s;s=o;o=f}if(l[r+1]>l[n+1]){f=r;r=n;n=f;f=i;i=s;s=f}var d=(l[r]+e.offsetX)*e.scaleX;var p=(l[r+1]+e.offsetY)*e.scaleY;var v=(l[n]+e.offsetX)*e.scaleX;var g=(l[n+1]+e.offsetY)*e.scaleY;var m=(l[a]+e.offsetX)*e.scaleX;var A=(l[a+1]+e.offsetY)*e.scaleY;if(p>=A){return}var b=c[i],y=c[i+1],x=c[i+2];var S=c[s],k=c[s+1],w=c[s+2];var _=c[o],C=c[o+1],T=c[o+2];var L=Math.round(p),P=Math.round(A);var E,R,I,D;var j,O,F,M;var N;for(var U=L;U<=P;U++){if(U<g){N=U<p?0:p===g?1:(p-U)/(p-g);E=d-(d-v)*N;R=b-(b-S)*N;I=y-(y-k)*N;D=x-(x-w)*N}else{N=U>A?1:g===A?0:(g-U)/(g-A);E=v-(v-m)*N;R=S-(S-_)*N;I=k-(k-C)*N;D=w-(w-T)*N}N=U<p?0:U>A?1:(p-U)/(p-A);j=d-(d-m)*N;O=b-(b-_)*N;F=y-(y-C)*N;M=x-(x-T)*N;var B=Math.round(Math.min(E,j));var W=Math.round(Math.max(E,j));var G=u*U+B*4;for(var X=B;X<=W;X++){N=(E-X)/(E-j);N=N<0?0:N>1?1:N;h[G++]=R-(R-O)*N|0;h[G++]=I-(I-F)*N|0;h[G++]=D-(D-M)*N|0;h[G++]=255}}}function r(t,r,n){var a=r.coords;var i=r.colors;var o,l;switch(r.type){case"lattice":var c=r.verticesPerRow;var h=Math.floor(a.length/c)-1;var u=c-1;for(o=0;o<h;o++){var f=o*c;for(var d=0;d<u;d++,f++){e(t,n,a[f],a[f+1],a[f+c],i[f],i[f+1],i[f+c]);e(t,n,a[f+c+1],a[f+1],a[f+c],i[f+c+1],i[f+1],i[f+c])}}break;case"triangles":for(o=0,l=a.length;o<l;o+=3){e(t,n,a[o],a[o+1],a[o+2],i[o],i[o+1],i[o+2])}break;default:s("illigal figure");break}}function n(t,e,n,a,i,s,l){var c=1.1;var h=3e3;var u=2;var f=Math.floor(t[0]);var d=Math.floor(t[1]);var p=Math.ceil(t[2])-f;var v=Math.ceil(t[3])-d;var g=Math.min(Math.ceil(Math.abs(p*e[0]*c)),h);var m=Math.min(Math.ceil(Math.abs(v*e[1]*c)),h);var A=p/g;var b=v/m;var y={coords:n,colors:a,offsetX:-f,offsetY:-d,scaleX:1/A,scaleY:1/b};var x=g+u*2;var S=m+u*2;var k,w,_,C;if(o.isEnabled){k=o.drawFigures(g,m,s,i,y);w=l.getCanvas("mesh",x,S,false);w.context.drawImage(k,u,u);k=w.canvas}else{w=l.getCanvas("mesh",x,S,false);var T=w.context;var L=T.createImageData(g,m);if(s){var P=L.data;for(_=0,C=P.length;_<C;_+=4){P[_]=s[0];P[_+1]=s[1];P[_+2]=s[2];P[_+3]=255}}for(_=0;_<i.length;_++){r(L,i[_],y)}T.putImageData(L,u,u);k=w.canvas}return{canvas:k,offsetX:f-u*A,offsetY:d-u*b,scaleX:A,scaleY:b}}return n}();l.Mesh={fromIR:function t(e){var r=e[2];var a=e[3];var i=e[4];var s=e[5];var o=e[6];var l=e[8];return{type:"Pattern",getPattern:function t(e,h,u){var f;if(u){f=n.singularValueDecompose2dScale(e.mozCurrentTransform)}else{f=n.singularValueDecompose2dScale(h.baseTransform);if(o){var d=n.singularValueDecompose2dScale(o);f=[f[0]*d[0],f[1]*d[1]]}}var p=c(s,f,r,a,i,u?null:l,h.cachedCanvases);if(!u){e.setTransform.apply(e,h.baseTransform);if(o){e.transform.apply(e,o)}}e.translate(p.offsetX,p.offsetY);e.scale(p.scaleX,p.scaleY);return e.createPattern(p.canvas,"no-repeat")}}}};l.Dummy={fromIR:function t(){return{type:"Pattern",getPattern:function t(){return"hotpink"}}}};function h(t){var e=l[t[0]];if(!e){s("Unknown IR type: "+t[0])}return e.fromIR(t)}var u=function t(){var e={COLORED:1,UNCOLORED:2};var r=3e3;function o(t,e,r,n,a){this.operatorList=t[2];this.matrix=t[3]||[1,0,0,1,0,0];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.color=e;this.canvasGraphicsFactory=n;this.baseTransform=a;this.type="Pattern";this.ctx=r}o.prototype={createPatternCanvas:function t(e){var i=this.operatorList;var s=this.bbox;var o=this.xstep;var l=this.ystep;var c=this.paintType;var h=this.tilingType;var u=this.color;var f=this.canvasGraphicsFactory;a("TilingType: "+h);var d=s[0],p=s[1],v=s[2],g=s[3];var m=[d,p];var A=[d+o,p+l];var b=A[0]-m[0];var y=A[1]-m[1];var x=n.singularValueDecompose2dScale(this.matrix);var S=n.singularValueDecompose2dScale(this.baseTransform);var k=[x[0]*S[0],x[1]*S[1]];b=Math.min(Math.ceil(Math.abs(b*k[0])),r);y=Math.min(Math.ceil(Math.abs(y*k[1])),r);var w=e.cachedCanvases.getCanvas("pattern",b,y,true);var _=w.context;var C=f.createCanvasGraphics(_);C.groupLevel=e.groupLevel;this.setFillAndStrokeStyleToContext(_,c,u);this.setScale(b,y,o,l);this.transformToScale(C);var T=[1,0,0,1,-m[0],-m[1]];C.transform.apply(C,T);this.clipBbox(C,s,d,p,v,g);C.executeOperatorList(i);return w.canvas},setScale:function t(e,r,n,a){this.scale=[e/n,r/a]},transformToScale:function t(e){var r=this.scale;var n=[r[0],0,0,r[1],0,0];e.transform.apply(e,n)},scaleToContext:function t(){var e=this.scale;this.ctx.scale(1/e[0],1/e[1])},clipBbox:function t(e,r,n,a,s,o){if(r&&i(r)&&r.length===4){var l=s-n;var c=o-a;e.ctx.rect(n,a,l,c);e.clip();e.endPath()}},setFillAndStrokeStyleToContext:function t(r,a,i){switch(a){case e.COLORED:var o=this.ctx;r.fillStyle=o.fillStyle;r.strokeStyle=o.strokeStyle;break;case e.UNCOLORED:var l=n.makeCssRgb(i[0],i[1],i[2]);r.fillStyle=l;r.strokeStyle=l;break;default:s("Unsupported paint type: "+a)}},getPattern:function t(e,r){var n=this.createPatternCanvas(r);e=this.ctx;e.setTransform.apply(e,this.baseTransform);e.transform.apply(e,this.matrix);this.scaleToContext();return e.createPattern(n,"repeat")}};return o}();t.getShadingPatternFromIR=h;t.TilingPattern=u});(function(t,e){e(t.pdfjsDisplayCanvas={},t.pdfjsSharedUtil,t.pdfjsDisplayDOMUtils,t.pdfjsDisplayPatternHelper,t.pdfjsDisplayWebGL)})(this,function(t,e,r,n,a){var i=e.FONT_IDENTITY_MATRIX;var s=e.IDENTITY_MATRIX;var o=e.ImageKind;var l=e.OPS;var c=e.TextRenderingMode;var h=e.Uint32ArrayView;var u=e.Util;var f=e.assert;var d=e.info;var p=e.isNum;var v=e.isArray;var g=e.isLittleEndian;var m=e.error;var A=e.shadow;var b=e.warn;var y=n.TilingPattern;var x=n.getShadingPatternFromIR;var S=a.WebGLUtils;var k=r.hasCanvasTypedArrays;var w=16;var _=100;var C=4096;var T=.65;var L=true;var P=1e3;var E=16;var R={get value(){return A(R,"value",k())}};var I={get value(){return A(I,"value",g())}};function D(t,e){var r=document.createElement("canvas");r.width=t;r.height=e;return r}function j(t){if(!t.mozCurrentTransform){t._originalSave=t.save;t._originalRestore=t.restore;t._originalRotate=t.rotate;t._originalScale=t.scale;t._originalTranslate=t.translate;t._originalTransform=t.transform;t._originalSetTransform=t.setTransform;t._transformMatrix=t._transformMatrix||[1,0,0,1,0,0];t._transformStack=[];Object.defineProperty(t,"mozCurrentTransform",{get:function t(){return this._transformMatrix}});Object.defineProperty(t,"mozCurrentTransformInverse",{get:function t(){var e=this._transformMatrix;var r=e[0],n=e[1],a=e[2],i=e[3],s=e[4],o=e[5];var l=r*i-n*a;var c=n*a-r*i;return[i/l,n/c,a/c,r/l,(i*s-a*o)/c,(n*s-r*o)/l]}});t.save=function t(){var e=this._transformMatrix;this._transformStack.push(e);this._transformMatrix=e.slice(0,6);this._originalSave()};t.restore=function t(){var e=this._transformStack.pop();if(e){this._transformMatrix=e;this._originalRestore()}};t.translate=function t(e,r){var n=this._transformMatrix;n[4]=n[0]*e+n[2]*r+n[4];n[5]=n[1]*e+n[3]*r+n[5];this._originalTranslate(e,r)};t.scale=function t(e,r){var n=this._transformMatrix;n[0]=n[0]*e;n[1]=n[1]*e;n[2]=n[2]*r;n[3]=n[3]*r;this._originalScale(e,r)};t.transform=function e(r,n,a,i,s,o){var l=this._transformMatrix;this._transformMatrix=[l[0]*r+l[2]*n,l[1]*r+l[3]*n,l[0]*a+l[2]*i,l[1]*a+l[3]*i,l[0]*s+l[2]*o+l[4],l[1]*s+l[3]*o+l[5]];t._originalTransform(r,n,a,i,s,o)};t.setTransform=function e(r,n,a,i,s,o){this._transformMatrix=[r,n,a,i,s,o];t._originalSetTransform(r,n,a,i,s,o)};t.rotate=function t(e){var r=Math.cos(e);var n=Math.sin(e);var a=this._transformMatrix;this._transformMatrix=[a[0]*r+a[2]*n,a[1]*r+a[3]*n,a[0]*-n+a[2]*r,a[1]*-n+a[3]*r,a[4],a[5]];this._originalRotate(e)}}}var O=function t(){function e(){this.cache=Object.create(null)}e.prototype={getCanvas:function t(e,r,n,a){var i;if(this.cache[e]!==undefined){i=this.cache[e];i.canvas.width=r;i.canvas.height=n;i.context.setTransform(1,0,0,1,0,0)}else{var s=D(r,n);var o=s.getContext("2d");if(a){j(o)}this.cache[e]=i={canvas:s,context:o}}return i},clear:function(){for(var t in this.cache){var e=this.cache[t];e.canvas.width=0;e.canvas.height=0;delete this.cache[t]}}};return e}();function F(t){var e=1e3;var r=t.width,n=t.height;var a,i,s,o=r+1;var l=new Uint8Array(o*(n+1));var c=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]);var h=r+7&~7,u=t.data;var f=new Uint8Array(h*n),d=0,p;for(a=0,p=u.length;a<p;a++){var v=128,g=u[a];while(v>0){f[d++]=g&v?0:255;v>>=1}}var m=0;d=0;if(f[d]!==0){l[0]=1;++m}for(i=1;i<r;i++){if(f[d]!==f[d+1]){l[i]=f[d]?2:1;++m}d++}if(f[d]!==0){l[i]=2;++m}for(a=1;a<n;a++){d=a*h;s=a*o;if(f[d-h]!==f[d]){l[s]=f[d]?1:8;++m}var A=(f[d]?4:0)+(f[d-h]?8:0);for(i=1;i<r;i++){A=(A>>2)+(f[d+1]?4:0)+(f[d-h+1]?8:0);if(c[A]){l[s+i]=c[A];++m}d++}if(f[d-h]!==f[d]){l[s+i]=f[d]?2:4;++m}if(m>e){return null}}d=h*(n-1);s=a*o;if(f[d]!==0){l[s]=8;++m}for(i=1;i<r;i++){if(f[d]!==f[d+1]){l[s+i]=f[d]?4:8;++m}d++}if(f[d]!==0){l[s+i]=4;++m}if(m>e){return null}var b=new Int32Array([0,o,-1,0,-o,0,0,0,1]);var y=[];for(a=0;m&&a<=n;a++){var x=a*o;var S=x+r;while(x<S&&!l[x]){x++}if(x===S){continue}var k=[x%o,a];var w=l[x],_=x,C;do{var T=b[w];do{x+=T}while(!l[x]);C=l[x];if(C!==5&&C!==10){w=C;l[x]=0}else{w=C&51*w>>4;l[x]&=w>>2|w<<2}k.push(x%o);k.push(x/o|0);--m}while(_!==x);y.push(k);--a}var L=function(t){t.save();t.scale(1/r,-1/n);t.translate(0,-n);t.beginPath();for(var e=0,a=y.length;e<a;e++){var i=y[e];t.moveTo(i[0],i[1]);for(var s=2,o=i.length;s<o;s+=2){t.lineTo(i[s],i[s+1])}}t.fill();t.beginPath();t.restore()};return L}var M=function t(){function e(t){this.alphaIsShape=false;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=s;this.textMatrixScale=1;this.fontMatrix=i;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=c.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=false;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.resumeSMaskCtx=null;this.old=t}e.prototype={clone:function t(){return Object.create(this)},setCurrentPoint:function t(e,r){this.x=e;this.y=r}};return e}();var N=function t(){var e=15;var r=10;function n(t,e,r,n){this.ctx=t;this.current=new M;this.stateStack=[];this.pendingClip=null;this.pendingEOFill=false;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=r;this.imageLayer=n;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.cachedCanvases=new O;if(t){j(t)}this.cachedGetSinglePixelWidth=null}function a(t,e){if(typeof ImageData!=="undefined"&&e instanceof ImageData){t.putImageData(e,0,0);return}var r=e.height,n=e.width;var a=r%E;var i=(r-a)/E;var s=a===0?i:i+1;var l=t.createImageData(n,E);var c=0,u;var f=e.data;var d=l.data;var p,v,g,A;if(e.kind===o.GRAYSCALE_1BPP){var b=f.byteLength;var y=R.value?new Uint32Array(d.buffer):new h(d);var x=y.length;var S=n+7>>3;var k=4294967295;var w=I.value||!R.value?4278190080:255;for(p=0;p<s;p++){g=p<i?E:a;u=0;for(v=0;v<g;v++){var _=b-c;var C=0;var T=_>S?n:_*8-7;var L=T&~7;var P=0;var D=0;for(;C<L;C+=8){D=f[c++];y[u++]=D&128?k:w;y[u++]=D&64?k:w;y[u++]=D&32?k:w;y[u++]=D&16?k:w;y[u++]=D&8?k:w;y[u++]=D&4?k:w;y[u++]=D&2?k:w;y[u++]=D&1?k:w}for(;C<T;C++){if(P===0){D=f[c++];P=128}y[u++]=D&P?k:w;P>>=1}}while(u<x){y[u++]=0}t.putImageData(l,0,p*E)}}else if(e.kind===o.RGBA_32BPP){v=0;A=n*E*4;for(p=0;p<i;p++){d.set(f.subarray(c,c+A));c+=A;t.putImageData(l,0,v);v+=E}if(p<s){A=n*a*4;d.set(f.subarray(c,c+A));t.putImageData(l,0,v)}}else if(e.kind===o.RGB_24BPP){g=E;A=n*g;for(p=0;p<s;p++){if(p>=i){g=a;A=n*g}u=0;for(v=A;v--;){d[u++]=f[c++];d[u++]=f[c++];d[u++]=f[c++];d[u++]=255}t.putImageData(l,0,p*E)}}else{m("bad image kind: "+e.kind)}}function g(t,e){var r=e.height,n=e.width;var a=r%E;var i=(r-a)/E;var s=a===0?i:i+1;var o=t.createImageData(n,E);var l=0;var c=e.data;var h=o.data;for(var u=0;u<s;u++){var f=u<i?E:a;var d=3;for(var p=0;p<f;p++){var v=0;for(var g=0;g<n;g++){if(!v){var m=c[l++];v=128}h[d]=m&v?0:255;d+=4;v>>=1}}t.putImageData(o,0,u*E)}}function k(t,e){var r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"];for(var n=0,a=r.length;n<a;n++){var i=r[n];if(t[i]!==undefined){e[i]=t[i]}}if(t.setLineDash!==undefined){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function D(t,e,r,n){var a=t.length;for(var i=3;i<a;i+=4){var s=t[i];if(s===0){t[i-3]=e;t[i-2]=r;t[i-1]=n}else if(s<255){var o=255-s;t[i-3]=t[i-3]*s+e*o>>8;t[i-2]=t[i-2]*s+r*o>>8;t[i-1]=t[i-1]*s+n*o>>8}}}function N(t,e,r){var n=t.length;var a=1/255;for(var i=3;i<n;i+=4){var s=r?r[t[i]]:t[i];e[i]=e[i]*s*a|0}}function U(t,e,r){var n=t.length;for(var a=3;a<n;a+=4){var i=t[a-3]*77+t[a-2]*152+t[a-1]*28;e[a]=r?e[a]*r[i>>8]>>8:e[a]*i>>16}}function B(t,e,r,n,a,i,s){var o=!!i;var l=o?i[0]:0;var c=o?i[1]:0;var h=o?i[2]:0;var u;if(a==="Luminosity"){u=U}else{u=N}var f=1048576;var d=Math.min(n,Math.ceil(f/r));for(var p=0;p<n;p+=d){var v=Math.min(d,n-p);var g=t.getImageData(0,p,r,v);var m=e.getImageData(0,p,r,v);if(o){D(g.data,l,c,h)}u(g.data,m.data,s);t.putImageData(m,0,p)}}function W(t,e,r){var n=e.canvas;var a=e.context;t.setTransform(e.scaleX,0,0,e.scaleY,e.offsetX,e.offsetY);var i=e.backdrop||null;if(!e.transferMap&&S.isEnabled){var s=S.composeSMask(r.canvas,n,{subtype:e.subtype,backdrop:i});t.setTransform(1,0,0,1,0,0);t.drawImage(s,e.offsetX,e.offsetY);return}B(a,r,n.width,n.height,e.subtype,i,e.transferMap);t.drawImage(n,0,0)}var G=["butt","round","square"];var X=["miter","round","bevel"];var z={};var H={};n.prototype={beginDrawing:function t(e,r,n){var a=this.ctx.canvas.width;var i=this.ctx.canvas.height;this.ctx.save();this.ctx.fillStyle="rgb(255, 255, 255)";this.ctx.fillRect(0,0,a,i);this.ctx.restore();if(n){var s=this.cachedCanvases.getCanvas("transparent",a,i,true);this.compositeCtx=this.ctx;this.transparentCanvas=s.canvas;this.ctx=s.context;this.ctx.save();this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save();if(e){this.ctx.transform.apply(this.ctx,e)}this.ctx.transform.apply(this.ctx,r.transform);this.baseTransform=this.ctx.mozCurrentTransform.slice();if(this.imageLayer){this.imageLayer.beginLayout()}},executeOperatorList:function t(n,a,i,s){var o=n.argsArray;var c=n.fnArray;var h=a||0;var u=o.length;if(u===h){return h}var f=u-h>r&&typeof i==="function";var d=f?Date.now()+e:0;var p=0;var v=this.commonObjs;var g=this.objs;var m;while(true){if(s!==undefined&&h===s.nextBreakPoint){s.breakIt(h,i);return h}m=c[h];if(m!==l.dependency){this[m].apply(this,o[h])}else{var A=o[h];for(var b=0,y=A.length;b<y;b++){var x=A[b];var S=x[0]==="g"&&x[1]==="_";var k=S?v:g;if(!k.isResolved(x)){k.get(x,i);return h}}}h++;if(h===u){return h}if(f&&++p>r){if(Date.now()>d){i();return h}p=0}}},endDrawing:function t(){if(this.current.activeSMask!==null){this.endSMaskGroup()}this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}this.cachedCanvases.clear();S.clear();if(this.imageLayer){this.imageLayer.endLayout()}},setLineWidth:function t(e){this.current.lineWidth=e;this.ctx.lineWidth=e},setLineCap:function t(e){this.ctx.lineCap=G[e]},setLineJoin:function t(e){this.ctx.lineJoin=X[e]},setMiterLimit:function t(e){this.ctx.miterLimit=e},setDash:function t(e,r){var n=this.ctx;if(n.setLineDash!==undefined){n.setLineDash(e);n.lineDashOffset=r}},setRenderingIntent:function t(e){},setFlatness:function t(e){},setGState:function t(e){for(var r=0,n=e.length;r<n;r++){var a=e[r];var i=a[0];var s=a[1];switch(i){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=a[1];break;case"ca":this.current.fillAlpha=a[1];this.ctx.globalAlpha=a[1];break;case"BM":if(s&&s.name&&s.name!=="Normal"){var o=s.name.replace(/([A-Z])/g,function(t){return"-"+t.toLowerCase()}).substring(1);this.ctx.globalCompositeOperation=o;if(this.ctx.globalCompositeOperation!==o){b('globalCompositeOperation "'+o+'" is not supported')}}else{this.ctx.globalCompositeOperation="source-over"}break;case"SMask":if(this.current.activeSMask){if(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask){this.suspendSMaskGroup()}else{this.endSMaskGroup()}}this.current.activeSMask=s?this.tempSMask:null;if(this.current.activeSMask){this.beginSMaskGroup()}this.tempSMask=null;break}}},beginSMaskGroup:function t(){var e=this.current.activeSMask;var r=e.canvas.width;var n=e.canvas.height;var a="smaskGroupAt"+this.groupLevel;var i=this.cachedCanvases.getCanvas(a,r,n,true);var s=this.ctx;var o=s.mozCurrentTransform;this.ctx.save();var l=i.context;l.scale(1/e.scaleX,1/e.scaleY);l.translate(-e.offsetX,-e.offsetY);l.transform.apply(l,o);e.startTransformInverse=l.mozCurrentTransformInverse;k(s,l);this.ctx=l;this.setGState([["BM","Normal"],["ca",1],["CA",1]]);this.groupStack.push(s);this.groupLevel++},suspendSMaskGroup:function t(){var e=this.ctx;this.groupLevel--;this.ctx=this.groupStack.pop();W(this.ctx,this.current.activeSMask,e);this.ctx.restore();this.ctx.save();k(e,this.ctx);this.current.resumeSMaskCtx=e;var r=u.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,r);e.save();e.setTransform(1,0,0,1,0,0);e.clearRect(0,0,e.canvas.width,e.canvas.height);e.restore()},resumeSMaskGroup:function t(){var e=this.current.resumeSMaskCtx;var r=this.ctx;this.ctx=e;this.groupStack.push(r);this.groupLevel++},endSMaskGroup:function t(){var e=this.ctx;this.groupLevel--;this.ctx=this.groupStack.pop();W(this.ctx,this.current.activeSMask,e);this.ctx.restore();k(e,this.ctx);var r=u.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,r)},save:function t(){this.ctx.save();var e=this.current;this.stateStack.push(e);this.current=e.clone();this.current.resumeSMaskCtx=null},restore:function t(){if(this.current.resumeSMaskCtx){this.resumeSMaskGroup()}if(this.current.activeSMask!==null&&(this.stateStack.length===0||this.stateStack[this.stateStack.length-1].activeSMask!==this.current.activeSMask)){this.endSMaskGroup()}if(this.stateStack.length!==0){this.current=this.stateStack.pop();this.ctx.restore();this.pendingClip=null;this.cachedGetSinglePixelWidth=null}},transform:function t(e,r,n,a,i,s){this.ctx.transform(e,r,n,a,i,s);this.cachedGetSinglePixelWidth=null},constructPath:function t(e,r){var n=this.ctx;var a=this.current;var i=a.x,s=a.y;for(var o=0,c=0,h=e.length;o<h;o++){switch(e[o]|0){case l.rectangle:i=r[c++];s=r[c++];var u=r[c++];var f=r[c++];if(u===0){u=this.getSinglePixelWidth()}if(f===0){f=this.getSinglePixelWidth()}var d=i+u;var p=s+f;this.ctx.moveTo(i,s);this.ctx.lineTo(d,s);this.ctx.lineTo(d,p);this.ctx.lineTo(i,p);this.ctx.lineTo(i,s);this.ctx.closePath();break;case l.moveTo:i=r[c++];s=r[c++];n.moveTo(i,s);break;case l.lineTo:i=r[c++];s=r[c++];n.lineTo(i,s);break;case l.curveTo:i=r[c+4];s=r[c+5];n.bezierCurveTo(r[c],r[c+1],r[c+2],r[c+3],i,s);c+=6;break;case l.curveTo2:n.bezierCurveTo(i,s,r[c],r[c+1],r[c+2],r[c+3]);i=r[c+2];s=r[c+3];c+=4;break;case l.curveTo3:i=r[c+2];s=r[c+3];n.bezierCurveTo(r[c],r[c+1],i,s,i,s);c+=4;break;case l.closePath:n.closePath();break}}a.setCurrentPoint(i,s)},closePath:function t(){this.ctx.closePath()},stroke:function t(e){e=typeof e!=="undefined"?e:true;var r=this.ctx;var n=this.current.strokeColor;r.lineWidth=Math.max(this.getSinglePixelWidth()*T,this.current.lineWidth);r.globalAlpha=this.current.strokeAlpha;if(n&&n.hasOwnProperty("type")&&n.type==="Pattern"){r.save();r.strokeStyle=n.getPattern(r,this);r.stroke();r.restore()}else{r.stroke()}if(e){this.consumePath()}r.globalAlpha=this.current.fillAlpha},closeStroke:function t(){this.closePath();this.stroke()},fill:function t(e){e=typeof e!=="undefined"?e:true;var r=this.ctx;var n=this.current.fillColor;var a=this.current.patternFill;var i=false;if(a){r.save();if(this.baseTransform){r.setTransform.apply(r,this.baseTransform)}r.fillStyle=n.getPattern(r,this);i=true}if(this.pendingEOFill){if(r.mozFillRule!==undefined){r.mozFillRule="evenodd";r.fill();r.mozFillRule="nonzero"}else{r.fill("evenodd")}this.pendingEOFill=false}else{r.fill()}if(i){r.restore()}if(e){this.consumePath()}},eoFill:function t(){this.pendingEOFill=true;this.fill()},fillStroke:function t(){this.fill(false);this.stroke(false);this.consumePath()},eoFillStroke:function t(){this.pendingEOFill=true;this.fillStroke()},closeFillStroke:function t(){this.closePath();this.fillStroke()},closeEOFillStroke:function t(){this.pendingEOFill=true;this.closePath();this.fillStroke()},endPath:function t(){this.consumePath()},clip:function t(){this.pendingClip=z},eoClip:function t(){this.pendingClip=H},beginText:function t(){this.current.textMatrix=s;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0},endText:function t(){var e=this.pendingTextPaths;var r=this.ctx;if(e===undefined){r.beginPath();return}r.save();r.beginPath();for(var n=0;n<e.length;n++){var a=e[n];r.setTransform.apply(r,a.transform);r.translate(a.x,a.y);a.addToPath(r,a.fontSize)}r.restore();r.clip();r.beginPath();delete this.pendingTextPaths},setCharSpacing:function t(e){this.current.charSpacing=e},setWordSpacing:function t(e){this.current.wordSpacing=e},setHScale:function t(e){this.current.textHScale=e/100},setLeading:function t(e){this.current.leading=-e},setFont:function t(e,r){var n=this.commonObjs.get(e);var a=this.current;if(!n){m("Can't find font for "+e)}a.fontMatrix=n.fontMatrix?n.fontMatrix:i;if(a.fontMatrix[0]===0||a.fontMatrix[3]===0){b("Invalid font matrix for font "+e)}if(r<0){r=-r;a.fontDirection=-1}else{a.fontDirection=1}this.current.font=n;this.current.fontSize=r;if(n.isType3Font){return}var s=n.loadedName||"sans-serif";var o=n.black?"900":n.bold?"bold":"normal";var l=n.italic?"italic":"normal";var c='"'+s+'", '+n.fallbackName;var h=r<w?w:r>_?_:r;this.current.fontSizeScale=r/h;var u=l+" "+o+" "+h+"px "+c;this.ctx.font=u},setTextRenderingMode:function t(e){this.current.textRenderingMode=e},setTextRise:function t(e){this.current.textRise=e},moveText:function t(e,r){this.current.x=this.current.lineX+=e;this.current.y=this.current.lineY+=r},setLeadingMoveText:function t(e,r){this.setLeading(-r);this.moveText(e,r)},setTextMatrix:function t(e,r,n,a,i,s){this.current.textMatrix=[e,r,n,a,i,s];this.current.textMatrixScale=Math.sqrt(e*e+r*r);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0},nextLine:function t(){this.moveText(0,this.current.leading)},paintChar:function t(e,r,n){var a=this.ctx;var i=this.current;var s=i.font;var o=i.textRenderingMode;var l=i.fontSize/i.fontSizeScale;var h=o&c.FILL_STROKE_MASK;var u=!!(o&c.ADD_TO_PATH_FLAG);var f;if(s.disableFontFace||u){f=s.getPathGenerator(this.commonObjs,e)}if(s.disableFontFace){a.save();a.translate(r,n);a.beginPath();f(a,l);if(h===c.FILL||h===c.FILL_STROKE){a.fill()}if(h===c.STROKE||h===c.FILL_STROKE){a.stroke()}a.restore()}else{if(h===c.FILL||h===c.FILL_STROKE){a.fillText(e,r,n)}if(h===c.STROKE||h===c.FILL_STROKE){a.strokeText(e,r,n)}}if(u){var d=this.pendingTextPaths||(this.pendingTextPaths=[]);d.push({transform:a.mozCurrentTransform,x:r,y:n,fontSize:l,addToPath:f})}},get isFontSubpixelAAEnabled(){var t=document.createElement("canvas").getContext("2d");t.scale(1.5,1);t.fillText("I",0,10);var e=t.getImageData(0,0,10,10).data;var r=false;for(var n=3;n<e.length;n+=4){if(e[n]>0&&e[n]<255){r=true;break}}return A(this,"isFontSubpixelAAEnabled",r)},showText:function t(e){var r=this.current;var n=r.font;if(n.isType3Font){return this.showType3Text(e)}var a=r.fontSize;if(a===0){return}var i=this.ctx;var s=r.fontSizeScale;var o=r.charSpacing;var l=r.wordSpacing;var h=r.fontDirection;var u=r.textHScale*h;var f=e.length;var d=n.vertical;var v=d?1:-1;var g=n.defaultVMetrics;var m=a*r.fontMatrix[0];var A=r.textRenderingMode===c.FILL&&!n.disableFontFace;i.save();i.transform.apply(i,r.textMatrix);i.translate(r.x,r.y+r.textRise);if(r.patternFill){i.fillStyle=r.fillColor.getPattern(i,this)}if(h>0){i.scale(u,-1)}else{i.scale(u,1)}var b=r.lineWidth;var y=r.textMatrixScale;if(y===0||b===0){var x=r.textRenderingMode&c.FILL_STROKE_MASK;if(x===c.STROKE||x===c.FILL_STROKE){this.cachedGetSinglePixelWidth=null;b=this.getSinglePixelWidth()*T}}else{b/=y}if(s!==1){i.scale(s,s);b/=s}i.lineWidth=b;var S=0,k;for(k=0;k<f;++k){var w=e[k];if(p(w)){S+=v*w*a/1e3;continue}var _=false;var C=(w.isSpace?l:0)+o;var L=w.fontChar;var P=w.accent;var E,R,I,D;var j=w.width;if(d){var O,F,M;O=w.vmetric||g;F=w.vmetric?O[1]:j*.5;F=-F*m;M=O[2]*m;j=O?-O[0]:j;E=F/s;R=(S+M)/s}else{E=S/s;R=0}if(n.remeasure&&j>0){var N=i.measureText(L).width*1e3/a*s;if(j<N&&this.isFontSubpixelAAEnabled){var U=j/N;_=true;i.save();i.scale(U,1);E/=U}else if(j!==N){E+=(j-N)/2e3*a/s}}if(w.isInFont||n.missingFile){if(A&&!P){i.fillText(L,E,R)}else{this.paintChar(L,E,R);if(P){I=E+P.offset.x/s;D=R-P.offset.y/s;this.paintChar(P.fontChar,I,D)}}}var B=j*m+C*h;S+=B;if(_){i.restore()}}if(d){r.y-=S*u}else{r.x+=S*u}i.restore()},showType3Text:function t(e){var r=this.ctx;var n=this.current;var a=n.font;var s=n.fontSize;var o=n.fontDirection;var l=a.vertical?1:-1;var h=n.charSpacing;var f=n.wordSpacing;var d=n.textHScale*o;var v=n.fontMatrix||i;var g=e.length;var m=n.textRenderingMode===c.INVISIBLE;var A,y,x,S;if(m||s===0){return}this.cachedGetSinglePixelWidth=null;r.save();r.transform.apply(r,n.textMatrix);r.translate(n.x,n.y);r.scale(d,o);for(A=0;A<g;++A){y=e[A];if(p(y)){S=l*y*s/1e3;this.ctx.translate(S,0);n.x+=S*d;continue}var k=(y.isSpace?f:0)+h;var w=a.charProcOperatorList[y.operatorListId];if(!w){b('Type3 character "'+y.operatorListId+'" is not available');continue}this.processingType3=y;this.save();r.scale(s,s);r.transform.apply(r,v);this.executeOperatorList(w);this.restore();var _=u.applyTransform([y.width,0],v);x=_[0]*s+k;r.translate(x,0);n.x+=x*d}r.restore();this.processingType3=null},setCharWidth:function t(e,r){},setCharWidthAndBounds:function t(e,r,n,a,i,s){this.ctx.rect(n,a,i-n,s-a);this.clip();this.endPath()},getColorN_Pattern:function t(e){var r;if(e[0]==="TilingPattern"){var a=e[1];var i=this.baseTransform||this.ctx.mozCurrentTransform.slice();var s=this;var o={createCanvasGraphics:function(t){return new n(t,s.commonObjs,s.objs)}};r=new y(e,a,this.ctx,o,i)}else{r=x(e)}return r},setStrokeColorN:function t(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function t(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=true},setStrokeRGBColor:function t(e,r,n){var a=u.makeCssRgb(e,r,n);this.ctx.strokeStyle=a;this.current.strokeColor=a},setFillRGBColor:function t(e,r,n){var a=u.makeCssRgb(e,r,n);this.ctx.fillStyle=a;this.current.fillColor=a;this.current.patternFill=false},shadingFill:function t(e){var r=this.ctx;this.save();var n=x(e);r.fillStyle=n.getPattern(r,this,true);var a=r.mozCurrentTransformInverse;if(a){var i=r.canvas;var s=i.width;var o=i.height;var l=u.applyTransform([0,0],a);var c=u.applyTransform([0,o],a);var h=u.applyTransform([s,0],a);var f=u.applyTransform([s,o],a);var d=Math.min(l[0],c[0],h[0],f[0]);var p=Math.min(l[1],c[1],h[1],f[1]);var v=Math.max(l[0],c[0],h[0],f[0]);var g=Math.max(l[1],c[1],h[1],f[1]);this.ctx.fillRect(d,p,v-d,g-p)}else{this.ctx.fillRect(-1e10,-1e10,2e10,2e10)}this.restore()},beginInlineImage:function t(){m("Should not call beginInlineImage")},beginImageData:function t(){m("Should not call beginImageData")},paintFormXObjectBegin:function t(e,r){this.save();this.baseTransformStack.push(this.baseTransform);if(v(e)&&e.length===6){this.transform.apply(this,e)}this.baseTransform=this.ctx.mozCurrentTransform;if(v(r)&&r.length===4){var n=r[2]-r[0];var a=r[3]-r[1];this.ctx.rect(r[0],r[1],n,a);this.clip();this.endPath()}},paintFormXObjectEnd:function t(){this.restore();this.baseTransform=this.baseTransformStack.pop()},beginGroup:function t(e){this.save();var r=this.ctx;if(!e.isolated){d("TODO: Support non-isolated groups.")}if(e.knockout){b("Knockout groups not supported.")}var n=r.mozCurrentTransform;if(e.matrix){r.transform.apply(r,e.matrix)}f(e.bbox,"Bounding box is required.");var a=u.getAxialAlignedBoundingBox(e.bbox,r.mozCurrentTransform);var i=[0,0,r.canvas.width,r.canvas.height];a=u.intersect(a,i)||[0,0,0,0];var s=Math.floor(a[0]);var o=Math.floor(a[1]);var l=Math.max(Math.ceil(a[2])-s,1);var c=Math.max(Math.ceil(a[3])-o,1);var h=1,p=1;if(l>C){h=l/C;l=C}if(c>C){p=c/C;c=C}var v="groupAt"+this.groupLevel;if(e.smask){v+="_smask_"+this.smaskCounter++%2}var g=this.cachedCanvases.getCanvas(v,l,c,true);var m=g.context;m.scale(1/h,1/p);m.translate(-s,-o);m.transform.apply(m,n);if(e.smask){this.smaskStack.push({canvas:g.canvas,context:m,offsetX:s,offsetY:o,scaleX:h,scaleY:p,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null})}else{r.setTransform(1,0,0,1,0,0);r.translate(s,o);r.scale(h,p)}k(r,m);this.ctx=m;this.setGState([["BM","Normal"],["ca",1],["CA",1]]);this.groupStack.push(r);this.groupLevel++;this.current.activeSMask=null},endGroup:function t(e){this.groupLevel--;var r=this.ctx;this.ctx=this.groupStack.pop();if(this.ctx.imageSmoothingEnabled!==undefined){this.ctx.imageSmoothingEnabled=false}else{this.ctx.mozImageSmoothingEnabled=false}if(e.smask){this.tempSMask=this.smaskStack.pop()}else{this.ctx.drawImage(r.canvas,0,0)}this.restore()},beginAnnotations:function t(){this.save();this.current=new M;if(this.baseTransform){this.ctx.setTransform.apply(this.ctx,this.baseTransform)}},endAnnotations:function t(){this.restore()},beginAnnotation:function t(e,r,n){this.save();if(v(e)&&e.length===4){var a=e[2]-e[0];var i=e[3]-e[1];this.ctx.rect(e[0],e[1],a,i);this.clip();this.endPath()}this.transform.apply(this,r);this.transform.apply(this,n)},endAnnotation:function t(){this.restore()},paintJpegXObject:function t(e,r,n){var a=this.objs.get(e);if(!a){b("Dependent image isn't ready yet");return}this.save();var i=this.ctx;i.scale(1/r,-1/n);i.drawImage(a,0,0,a.width,a.height,0,-n,r,n);if(this.imageLayer){var s=i.mozCurrentTransformInverse;var o=this.getCanvasPosition(0,0);this.imageLayer.appendImage({objId:e,left:o[0],top:o[1],width:r/s[0],height:n/s[3]})}this.restore()},paintImageMaskXObject:function t(e){var r=this.ctx;var n=e.width,a=e.height;var i=this.current.fillColor;var s=this.current.patternFill;var o=this.processingType3;if(L&&o&&o.compiled===undefined){if(n<=P&&a<=P){o.compiled=F({data:e.data,width:n,height:a})}else{o.compiled=null}}if(o&&o.compiled){o.compiled(r);return}var l=this.cachedCanvases.getCanvas("maskCanvas",n,a);var c=l.context;c.save();g(c,e);c.globalCompositeOperation="source-in";c.fillStyle=s?i.getPattern(c,this):i;c.fillRect(0,0,n,a);c.restore();this.paintInlineImageXObject(l.canvas)},paintImageMaskXObjectRepeat:function t(e,r,n,a){var i=e.width;var s=e.height;var o=this.current.fillColor;var l=this.current.patternFill;var c=this.cachedCanvases.getCanvas("maskCanvas",i,s);var h=c.context;h.save();g(h,e);h.globalCompositeOperation="source-in";h.fillStyle=l?o.getPattern(h,this):o;h.fillRect(0,0,i,s);h.restore();var u=this.ctx;for(var f=0,d=a.length;f<d;f+=2){u.save();u.transform(r,0,0,n,a[f],a[f+1]);u.scale(1,-1);u.drawImage(c.canvas,0,0,i,s,0,-1,1,1);u.restore()}},paintImageMaskXObjectGroup:function t(e){var r=this.ctx;var n=this.current.fillColor;var a=this.current.patternFill;for(var i=0,s=e.length;i<s;i++){var o=e[i];var l=o.width,c=o.height;var h=this.cachedCanvases.getCanvas("maskCanvas",l,c);var u=h.context;u.save();g(u,o);u.globalCompositeOperation="source-in";u.fillStyle=a?n.getPattern(u,this):n;u.fillRect(0,0,l,c);u.restore();r.save();r.transform.apply(r,o.transform);r.scale(1,-1);r.drawImage(h.canvas,0,0,l,c,0,-1,1,1);r.restore()}},paintImageXObject:function t(e){var r=this.objs.get(e);if(!r){b("Dependent image isn't ready yet");return}this.paintInlineImageXObject(r)},paintImageXObjectRepeat:function t(e,r,n,a){var i=this.objs.get(e);if(!i){b("Dependent image isn't ready yet");return}var s=i.width;var o=i.height;var l=[];for(var c=0,h=a.length;c<h;c+=2){l.push({transform:[r,0,0,n,a[c],a[c+1]],x:0,y:0,w:s,h:o})}this.paintInlineImageXObjectGroup(i,l)},paintInlineImageXObject:function t(e){var r=e.width;var n=e.height;var i=this.ctx;this.save();i.scale(1/r,-1/n);var s=i.mozCurrentTransformInverse;var o=s[0],l=s[1];var c=Math.max(Math.sqrt(o*o+l*l),1);var h=s[2],u=s[3];var f=Math.max(Math.sqrt(h*h+u*u),1);var d,p;if(e instanceof HTMLElement||!e.data){d=e}else{p=this.cachedCanvases.getCanvas("inlineImage",r,n);var v=p.context;a(v,e);d=p.canvas}var g=r,m=n;var A="prescale1";while(c>2&&g>1||f>2&&m>1){var b=g,y=m;if(c>2&&g>1){b=Math.ceil(g/2);c/=g/b}if(f>2&&m>1){y=Math.ceil(m/2);f/=m/y}p=this.cachedCanvases.getCanvas(A,b,y);v=p.context;v.clearRect(0,0,b,y);v.drawImage(d,0,0,g,m,0,0,b,y);d=p.canvas;g=b;m=y;A=A==="prescale1"?"prescale2":"prescale1"}i.drawImage(d,0,0,g,m,0,-n,r,n);if(this.imageLayer){var x=this.getCanvasPosition(0,-n);this.imageLayer.appendImage({imgData:e,left:x[0],top:x[1],width:r/s[0],height:n/s[3]})}this.restore()},paintInlineImageXObjectGroup:function t(e,r){var n=this.ctx;var i=e.width;var s=e.height;var o=this.cachedCanvases.getCanvas("inlineImage",i,s);var l=o.context;a(l,e);for(var c=0,h=r.length;c<h;c++){var u=r[c];n.save();n.transform.apply(n,u.transform);n.scale(1,-1);n.drawImage(o.canvas,u.x,u.y,u.w,u.h,0,-1,1,1);if(this.imageLayer){var f=this.getCanvasPosition(u.x,u.y);this.imageLayer.appendImage({imgData:e,left:f[0],top:f[1],width:i,height:s})}n.restore()}},paintSolidColorImageMask:function t(){this.ctx.fillRect(0,0,1,1)},paintXObject:function t(){b("Unsupported 'paintXObject' command.")},markPoint:function t(e){},markPointProps:function t(e,r){},beginMarkedContent:function t(e){},beginMarkedContentProps:function t(e,r){},endMarkedContent:function t(){},beginCompat:function t(){},endCompat:function t(){},consumePath:function t(){var e=this.ctx;if(this.pendingClip){if(this.pendingClip===H){if(e.mozFillRule!==undefined){e.mozFillRule="evenodd";e.clip();e.mozFillRule="nonzero"}else{e.clip("evenodd")}}else{e.clip()}this.pendingClip=null}e.beginPath()},getSinglePixelWidth:function t(e){if(this.cachedGetSinglePixelWidth===null){this.ctx.save();var r=this.ctx.mozCurrentTransformInverse;this.ctx.restore();this.cachedGetSinglePixelWidth=Math.sqrt(Math.max(r[0]*r[0]+r[1]*r[1],r[2]*r[2]+r[3]*r[3]))}return this.cachedGetSinglePixelWidth},getCanvasPosition:function t(e,r){var n=this.ctx.mozCurrentTransform;return[n[0]*e+n[2]*r+n[4],n[1]*e+n[3]*r+n[5]]}};for(var Y in l){n.prototype[l[Y]]=n.prototype[Y]}return n}();t.CanvasGraphics=N;t.createScratchCanvas=D});(function(t,e){e(t.pdfjsDisplayAPI={},t.pdfjsSharedUtil,t.pdfjsDisplayFontLoader,t.pdfjsDisplayCanvas,t.pdfjsDisplayMetadata,t.pdfjsDisplayDOMUtils)})(this,function(t,a,i,s,o,l,c){var h=a.InvalidPDFException;var u=a.MessageHandler;var f=a.MissingPDFException;var d=a.PageViewport;var p=a.PasswordResponses;var v=a.PasswordException;var g=a.StatTimer;var m=a.UnexpectedResponseException;var A=a.UnknownErrorException;var b=a.Util;var y=a.createPromiseCapability;var x=a.error;var S=a.deprecated;var k=a.getVerbosityLevel;var w=a.info;var _=a.isInt;var C=a.isArray;var T=a.isArrayBuffer;var L=a.isSameOrigin;var P=a.loadJpegStream;var E=a.stringToBytes;var R=a.globalScope;var I=a.warn;var D=i.FontFaceObject;var j=i.FontLoader;var O=s.CanvasGraphics;var F=s.createScratchCanvas;var M=o.Metadata;var N=l.getDefaultSetting;var U=65536;var B=false;var W;var G=false;var X=null;var z=false;if(typeof window==="undefined"){B=true;if(typeof require.ensure==="undefined"){require.ensure=require("node-ensure")}z=true}if(typeof __webpack_require__!=="undefined"){z=true}if(typeof requirejs!=="undefined"&&requirejs.toUrl){W=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js")}var H=typeof requirejs!=="undefined"&&requirejs.load;X=z?function(t){require.ensure([],function(){var e=require("./pdf.worker.js");t(e.WorkerMessageHandler)})}:H?function(t){requirejs(["pdfjs-dist/build/pdf.worker"],function(e){t(e.WorkerMessageHandler)})}:null;function Y(t,e,r,n){var a=new q;if(arguments.length>1){S("getDocument is called with pdfDataRangeTransport, "+"passwordCallback or progressCallback argument")}if(e){if(!(e instanceof J)){e=Object.create(e);e.length=t.length;e.initialData=t.initialData;if(!e.abort){e.abort=function(){}}}t=Object.create(t);t.range=e}a.onPassword=r||null;a.onProgress=n||null;var i;if(typeof t==="string"){i={url:t}}else if(T(t)){i={data:t}}else if(t instanceof J){i={range:t}}else{if(typeof t!=="object"){x("Invalid parameter in getDocument, need either Uint8Array, "+"string or a parameter object")}if(!t.url&&!t.data&&!t.range){x("Invalid parameter object: need either .data, .range or .url")}i=t}var s={};var o=null;var l=null;for(var c in i){if(c==="url"&&typeof window!=="undefined"){s[c]=new URL(i[c],window.location).href;continue}else if(c==="range"){o=i[c];continue}else if(c==="worker"){l=i[c];continue}else if(c==="data"&&!(i[c]instanceof Uint8Array)){var h=i[c];if(typeof h==="string"){s[c]=E(h)}else if(typeof h==="object"&&h!==null&&!isNaN(h.length)){s[c]=new Uint8Array(h)}else if(T(h)){s[c]=new Uint8Array(h)}else{x("Invalid PDF binary data: either typed array, string or "+"array-like object is expected in the data property.")}continue}s[c]=i[c]}s.rangeChunkSize=s.rangeChunkSize||U;if(!l){l=new Z;a._worker=l}var f=a.docId;l.promise.then(function(){if(a.destroyed){throw new Error("Loading aborted")}return V(l,s,o,f).then(function(t){if(a.destroyed){throw new Error("Loading aborted")}var e=new u(f,t,l.port);var r=new $(e,a,o);a._transport=r;e.send("Ready",null)})}).catch(a._capability.reject);return a}function V(t,e,r,n){if(t.destroyed){return Promise.reject(new Error("Worker was destroyed"))}e.disableAutoFetch=N("disableAutoFetch");e.disableStream=N("disableStream");e.chunkedViewerLoading=!!r;if(r){e.length=r.length;e.initialData=r.initialData}return t.messageHandler.sendWithPromise("GetDocRequest",{docId:n,source:e,disableRange:N("disableRange"),maxImageSize:N("maxImageSize"),cMapUrl:N("cMapUrl"),cMapPacked:N("cMapPacked"),disableFontFace:N("disableFontFace"),disableCreateObjectURL:N("disableCreateObjectURL"),postMessageTransfers:N("postMessageTransfers")&&!G,docBaseUrl:e.docBaseUrl}).then(function(e){if(t.destroyed){throw new Error("Worker was destroyed")}return e})}var q=function t(){var e=0;function r(){this._capability=y();this._transport=null;this._worker=null;this.docId="d"+e++;this.destroyed=false;this.onPassword=null;this.onProgress=null;this.onUnsupportedFeature=null}r.prototype={get promise(){return this._capability.promise},destroy:function(){this.destroyed=true;var t=!this._transport?Promise.resolve():this._transport.destroy();return t.then(function(){this._transport=null;if(this._worker){this._worker.destroy();this._worker=null}}.bind(this))},then:function t(e,r){return this.promise.then.apply(this.promise,arguments)}};return r}();var J=function t(){function e(t,e){this.length=t;this.initialData=e;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._readyCapability=y()}e.prototype={addRangeListener:function t(e){this._rangeListeners.push(e)},addProgressListener:function t(e){this._progressListeners.push(e)},addProgressiveReadListener:function t(e){this._progressiveReadListeners.push(e)},onDataRange:function t(e,r){var n=this._rangeListeners;for(var a=0,i=n.length;a<i;++a){n[a](e,r)}},onDataProgress:function t(e){this._readyCapability.promise.then(function(){var t=this._progressListeners;for(var r=0,n=t.length;r<n;++r){t[r](e)}}.bind(this))},onDataProgressiveRead:function t(e){this._readyCapability.promise.then(function(){var t=this._progressiveReadListeners;for(var r=0,n=t.length;r<n;++r){t[r](e)}}.bind(this))},transportReady:function t(){this._readyCapability.resolve()},requestDataRange:function t(e,r){throw new Error("Abstract method PDFDataRangeTransport.requestDataRange")},abort:function t(){}};return e}();var Q=function t(){function e(t,e,r){this.pdfInfo=t;this.transport=e;this.loadingTask=r}e.prototype={get numPages(){return this.pdfInfo.numPages},get fingerprint(){return this.pdfInfo.fingerprint},getPage:function t(e){return this.transport.getPage(e)},getPageIndex:function t(e){return this.transport.getPageIndex(e)},getDestinations:function t(){return this.transport.getDestinations()},getDestination:function t(e){return this.transport.getDestination(e)},getPageLabels:function t(){return this.transport.getPageLabels()},getAttachments:function t(){return this.transport.getAttachments()},getJavaScript:function t(){return this.transport.getJavaScript()},getOutline:function t(){return this.transport.getOutline()},getMetadata:function t(){return this.transport.getMetadata()},getData:function t(){return this.transport.getData()},getDownloadInfo:function t(){return this.transport.downloadInfoCapability.promise},getStats:function t(){return this.transport.getStats()},cleanup:function t(){this.transport.startCleanup()},destroy:function t(){return this.loadingTask.destroy()}};return e}();var K=function t(){function e(t,e,r){this.pageIndex=t;this.pageInfo=e;this.transport=r;this.stats=new g;this.stats.enabled=N("enableStats");this.commonObjs=r.commonObjs;this.objs=new tt;this.cleanupAfterRender=false;this.pendingCleanup=false;this.intentStates=Object.create(null);this.destroyed=false}e.prototype={get pageNumber(){return this.pageIndex+1},get rotate(){return this.pageInfo.rotate},get ref(){return this.pageInfo.ref},get userUnit(){return this.pageInfo.userUnit},get view(){return this.pageInfo.view},getViewport:function t(e,r){if(arguments.length<2){r=this.rotate}return new d(this.view,e,r,0,0)},getAnnotations:function t(e){var r=e&&e.intent||null;if(!this.annotationsPromise||this.annotationsIntent!==r){this.annotationsPromise=this.transport.getAnnotations(this.pageIndex,r);this.annotationsIntent=r}return this.annotationsPromise},render:function t(e){var r=this.stats;r.time("Overall");this.pendingCleanup=false;var n=e.intent==="print"?"print":"display";var a=e.renderInteractiveForms===true?true:false;if(!this.intentStates[n]){this.intentStates[n]=Object.create(null)}var i=this.intentStates[n];if(!i.displayReadyCapability){i.receivingOperatorList=true;i.displayReadyCapability=y();i.operatorList={fnArray:[],argsArray:[],lastChunk:false};this.stats.time("Page Request");this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageNumber-1,intent:n,renderInteractiveForms:a})}var s=new rt(c,e,this.objs,this.commonObjs,i.operatorList,this.pageNumber);s.useRequestAnimationFrame=n!=="print";if(!i.renderTasks){i.renderTasks=[]}i.renderTasks.push(s);var o=s.task;if(e.continueCallback){S("render is used with continueCallback parameter");o.onContinue=e.continueCallback}var l=this;i.displayReadyCapability.promise.then(function t(e){if(l.pendingCleanup){c();return}r.time("Rendering");s.initializeGraphics(e);s.operatorListChanged()},function t(e){c(e)});function c(t){var e=i.renderTasks.indexOf(s);if(e>=0){i.renderTasks.splice(e,1)}if(l.cleanupAfterRender){l.pendingCleanup=true}l._tryCleanup();if(t){s.capability.reject(t)}else{s.capability.resolve()}r.timeEnd("Rendering");r.timeEnd("Overall")}return o},getOperatorList:function t(){function e(){if(n.operatorList.lastChunk){n.opListReadCapability.resolve(n.operatorList);var t=n.renderTasks.indexOf(a);if(t>=0){n.renderTasks.splice(t,1)}}}var r="oplist";if(!this.intentStates[r]){this.intentStates[r]=Object.create(null)}var n=this.intentStates[r];var a;if(!n.opListReadCapability){a={};a.operatorListChanged=e;n.receivingOperatorList=true;n.opListReadCapability=y();n.renderTasks=[];n.renderTasks.push(a);n.operatorList={fnArray:[],argsArray:[],lastChunk:false};this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageIndex,intent:r})}return n.opListReadCapability.promise},getTextContent:function t(e){return this.transport.messageHandler.sendWithPromise("GetTextContent",{pageIndex:this.pageNumber-1,normalizeWhitespace:e&&e.normalizeWhitespace===true?true:false,combineTextItems:e&&e.disableCombineTextItems===true?false:true})},_destroy:function t(){this.destroyed=true;this.transport.pageCache[this.pageIndex]=null;var e=[];Object.keys(this.intentStates).forEach(function(t){if(t==="oplist"){return}var r=this.intentStates[t];r.renderTasks.forEach(function(t){var r=t.capability.promise.catch(function(){});e.push(r);t.cancel()})},this);this.objs.clear();this.annotationsPromise=null;this.pendingCleanup=false;return Promise.all(e)},destroy:function(){S("page destroy method, use cleanup() instead");this.cleanup()},cleanup:function t(){this.pendingCleanup=true;this._tryCleanup()},_tryCleanup:function t(){if(!this.pendingCleanup||Object.keys(this.intentStates).some(function(t){var e=this.intentStates[t];return e.renderTasks.length!==0||e.receivingOperatorList},this)){return}Object.keys(this.intentStates).forEach(function(t){delete this.intentStates[t]},this);this.objs.clear();this.annotationsPromise=null;this.pendingCleanup=false},_startRenderPage:function t(e,r){var n=this.intentStates[r];if(n.displayReadyCapability){n.displayReadyCapability.resolve(e)}},_renderPageChunk:function t(e,r){var n=this.intentStates[r];var a,i;for(a=0,i=e.length;a<i;a++){n.operatorList.fnArray.push(e.fnArray[a]);n.operatorList.argsArray.push(e.argsArray[a])}n.operatorList.lastChunk=e.lastChunk;for(a=0;a<n.renderTasks.length;a++){n.renderTasks[a].operatorListChanged()}if(e.lastChunk){n.receivingOperatorList=false;this._tryCleanup()}}};return e}();var Z=function t(){var e=0;function r(){if(typeof W!=="undefined"){return W}if(N("workerSrc")){return N("workerSrc")}if(n){return n.replace(/\.js$/i,".worker.js")}x("No PDFJS.workerSrc specified")}var a;function i(){var t;if(a){return a.promise}a=y();var e=X||function(t){b.loadScript(r(),function(){t(window.pdfjsDistBuildPdfWorker.WorkerMessageHandler)})};e(a.resolve);return a.promise}function s(t){this._listeners=[];this._defer=t;this._deferred=Promise.resolve(undefined)}s.prototype={postMessage:function(t,e){function r(t){if(typeof t!=="object"||t===null){return t}if(n.has(t)){return n.get(t)}var a;var i;if((i=t.buffer)&&T(i)){var s=e&&e.indexOf(i)>=0;if(t===i){a=t}else if(s){a=new t.constructor(i,t.byteOffset,t.byteLength)}else{a=new t.constructor(t)}n.set(t,a);return a}a=C(t)?[]:{};n.set(t,a);for(var o in t){var l,c=t;while(!(l=Object.getOwnPropertyDescriptor(c,o))){c=Object.getPrototypeOf(c)}if(typeof l.value==="undefined"||typeof l.value==="function"){continue}a[o]=r(l.value)}return a}if(!this._defer){this._listeners.forEach(function(e){e.call(this,{data:t})},this);return}var n=new WeakMap;var a={data:r(t)};this._deferred.then(function(){this._listeners.forEach(function(t){t.call(this,a)},this)}.bind(this))},addEventListener:function(t,e){this._listeners.push(e)},removeEventListener:function(t,e){var r=this._listeners.indexOf(e);this._listeners.splice(r,1)},terminate:function(){this._listeners=[]}};function o(t){var e="importScripts('"+t+"');";return URL.createObjectURL(new Blob([e]))}function l(t){this.name=t;this.destroyed=false;this._readyCapability=y();this._port=null;this._webWorker=null;this._messageHandler=null;this._initialize()}l.prototype={get promise(){return this._readyCapability.promise},get port(){return this._port},get messageHandler(){return this._messageHandler},_initialize:function t(){if(!B&&!N("disableWorker")&&typeof Worker!=="undefined"){var e=r();try{if(!L(window.location.href,e)){e=o(new URL(e,window.location).href)}var n=new Worker(e);var a=new u("main","worker",n);var i=function(){n.removeEventListener("error",s);a.destroy();n.terminate();if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"))}else{this._setupFakeWorker()}}.bind(this);var s=function(t){if(!this._webWorker){i()}}.bind(this);n.addEventListener("error",s);a.on("test",function t(e){n.removeEventListener("error",s);if(this.destroyed){i();return}var r=e&&e.supportTypedArray;if(r){this._messageHandler=a;this._port=n;this._webWorker=n;if(!e.supportTransfers){G=true}this._readyCapability.resolve();a.send("configure",{verbosity:k()})}else{this._setupFakeWorker();a.destroy();n.terminate()}}.bind(this));a.on("console_log",function(t){console.log.apply(console,t)});a.on("console_error",function(t){console.error.apply(console,t)});a.on("ready",function(t){n.removeEventListener("error",s);if(this.destroyed){i();return}try{l()}catch(t){this._setupFakeWorker()}}.bind(this));var l=function(){var t=N("postMessageTransfers")&&!G;var e=new Uint8Array([t?255:0]);try{a.send("test",e,[e.buffer])}catch(t){w("Cannot use postMessage transfers");e[0]=0;a.send("test",e)}};l();return}catch(t){w("The worker has been disabled.")}}this._setupFakeWorker()},_setupFakeWorker:function t(){if(!B&&!N("disableWorker")){I("Setting up fake worker.");B=true}i().then(function(t){if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}var r=Uint8Array!==Float32Array;var n=new s(r);this._port=n;var a="fake"+e++;var i=new u(a+"_worker",a,n);t.setup(i,n);var o=new u(a,a+"_worker",n);this._messageHandler=o;this._readyCapability.resolve()}.bind(this))},destroy:function t(){this.destroyed=true;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}};return l}();var $=function t(){function e(t,e,r){this.messageHandler=t;this.loadingTask=e;this.pdfDataRangeTransport=r;this.commonObjs=new tt;this.fontLoader=new j(e.docId);this.destroyed=false;this.destroyCapability=null;this._passwordCapability=null;this.pageCache=[];this.pagePromises=[];this.downloadInfoCapability=y();this.setupMessageHandler()}e.prototype={destroy:function t(){if(this.destroyCapability){return this.destroyCapability.promise}this.destroyed=true;this.destroyCapability=y();if(this._passwordCapability){this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"))}var e=[];this.pageCache.forEach(function(t){if(t){e.push(t._destroy())}});this.pageCache=[];this.pagePromises=[];var r=this;var n=this.messageHandler.sendWithPromise("Terminate",null);e.push(n);Promise.all(e).then(function(){r.fontLoader.clear();if(r.pdfDataRangeTransport){r.pdfDataRangeTransport.abort();r.pdfDataRangeTransport=null}if(r.messageHandler){r.messageHandler.destroy();r.messageHandler=null}r.destroyCapability.resolve()},this.destroyCapability.reject);return this.destroyCapability.promise},setupMessageHandler:function t(){var e=this.messageHandler;var r=this.loadingTask;var n=this.pdfDataRangeTransport;if(n){n.addRangeListener(function(t,r){e.send("OnDataRange",{begin:t,chunk:r})});n.addProgressListener(function(t){e.send("OnDataProgress",{loaded:t})});n.addProgressiveReadListener(function(t){e.send("OnDataRange",{chunk:t})});e.on("RequestDataRange",function t(e){n.requestDataRange(e.begin,e.end)},this)}e.on("GetDoc",function t(e){var r=e.pdfInfo;this.numPages=e.pdfInfo.numPages;var n=this.loadingTask;var a=new Q(r,this,n);this.pdfDocument=a;n._capability.resolve(a)},this);e.on("PasswordRequest",function t(e){this._passwordCapability=y();if(r.onPassword){var n=function(t){this._passwordCapability.resolve({password:t})}.bind(this);r.onPassword(n,e.code)}else{this._passwordCapability.reject(new v(e.message,e.code))}return this._passwordCapability.promise},this);e.on("PasswordException",function t(e){r._capability.reject(new v(e.message,e.code))},this);e.on("InvalidPDF",function t(e){this.loadingTask._capability.reject(new h(e.message))},this);e.on("MissingPDF",function t(e){this.loadingTask._capability.reject(new f(e.message))},this);e.on("UnexpectedResponse",function t(e){this.loadingTask._capability.reject(new m(e.message,e.status))},this);e.on("UnknownError",function t(e){this.loadingTask._capability.reject(new A(e.message,e.details))},this);e.on("DataLoaded",function t(e){this.downloadInfoCapability.resolve(e)},this);e.on("PDFManagerReady",function t(e){if(this.pdfDataRangeTransport){this.pdfDataRangeTransport.transportReady()}},this);e.on("StartRenderPage",function t(e){if(this.destroyed){return}var r=this.pageCache[e.pageIndex];r.stats.timeEnd("Page Request");r._startRenderPage(e.transparency,e.intent)},this);e.on("RenderPageChunk",function t(e){if(this.destroyed){return}var r=this.pageCache[e.pageIndex];r._renderPageChunk(e.operatorList,e.intent)},this);e.on("commonobj",function t(e){if(this.destroyed){return}var r=e[0];var n=e[1];if(this.commonObjs.hasData(r)){return}switch(n){case"Font":var a=e[2];if("error"in a){var i=a.error;I("Error during font loading: "+i);this.commonObjs.resolve(r,i);break}var s=null;if(N("pdfBug")&&R.FontInspector&&R["FontInspector"].enabled){s={registerFont:function(t,e){R["FontInspector"].fontAdded(t,e)}}}var o=new D(a,{isEvalSuported:N("isEvalSupported"),disableFontFace:N("disableFontFace"),fontRegistry:s});this.fontLoader.bind([o],function t(e){this.commonObjs.resolve(r,o)}.bind(this));break;case"FontPath":this.commonObjs.resolve(r,e[2]);break;default:x("Got unknown common object type "+n)}},this);e.on("obj",function t(e){if(this.destroyed){return}var r=e[0];var n=e[1];var a=e[2];var i=this.pageCache[n];var s;if(i.objs.hasData(r)){return}switch(a){case"JpegStream":s=e[3];P(r,s,i.objs);break;case"Image":s=e[3];i.objs.resolve(r,s);var o=8e6;if(s&&"data"in s&&s.data.length>o){i.cleanupAfterRender=true}break;default:x("Got unknown object type "+a)}},this);e.on("DocProgress",function t(e){if(this.destroyed){return}var r=this.loadingTask;if(r.onProgress){r.onProgress({loaded:e.loaded,total:e.total})}},this);e.on("PageError",function t(e){if(this.destroyed){return}var r=this.pageCache[e.pageNum-1];var n=r.intentStates[e.intent];if(n.displayReadyCapability){n.displayReadyCapability.reject(e.error)}else{x(e.error)}if(n.operatorList){n.operatorList.lastChunk=true;for(var a=0;a<n.renderTasks.length;a++){n.renderTasks[a].operatorListChanged()}}},this);e.on("UnsupportedFeature",function t(e){if(this.destroyed){return}var r=e.featureId;var n=this.loadingTask;if(n.onUnsupportedFeature){n.onUnsupportedFeature(r)}nt.notify(r)},this);e.on("JpegDecode",function(t){if(this.destroyed){return Promise.reject(new Error("Worker was destroyed"))}var e=t[0];var r=t[1];if(r!==3&&r!==1){return Promise.reject(new Error("Only 3 components or 1 component can be returned"))}return new Promise(function(t,n){var a=new Image;a.onload=function(){var e=a.width;var n=a.height;var i=e*n;var s=i*4;var o=new Uint8Array(i*r);var l=F(e,n);var c=l.getContext("2d");c.drawImage(a,0,0);var h=c.getImageData(0,0,e,n).data;var u,f;if(r===3){for(u=0,f=0;u<s;u+=4,f+=3){o[f]=h[u];o[f+1]=h[u+1];o[f+2]=h[u+2]}}else if(r===1){for(u=0,f=0;u<s;u+=4,f++){o[f]=h[u]}}t({data:o,width:e,height:n})};a.onerror=function(){n(new Error("JpegDecode failed to load image"))};a.src=e})},this)},getData:function t(){return this.messageHandler.sendWithPromise("GetData",null)},getPage:function t(e,r){if(!_(e)||e<=0||e>this.numPages){return Promise.reject(new Error("Invalid page request"))}var n=e-1;if(n in this.pagePromises){return this.pagePromises[n]}var a=this.messageHandler.sendWithPromise("GetPage",{pageIndex:n}).then(function(t){if(this.destroyed){throw new Error("Transport destroyed")}var e=new K(n,t,this);this.pageCache[n]=e;return e}.bind(this));this.pagePromises[n]=a;return a},getPageIndex:function t(e){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:e}).catch(function(t){return Promise.reject(new Error(t))})},getAnnotations:function t(e,r){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:r})},getDestinations:function t(){return this.messageHandler.sendWithPromise("GetDestinations",null)},getDestination:function t(e){return this.messageHandler.sendWithPromise("GetDestination",{id:e})},getPageLabels:function t(){return this.messageHandler.sendWithPromise("GetPageLabels",null)},getAttachments:function t(){return this.messageHandler.sendWithPromise("GetAttachments",null)},getJavaScript:function t(){return this.messageHandler.sendWithPromise("GetJavaScript",null)},getOutline:function t(){return this.messageHandler.sendWithPromise("GetOutline",null)},getMetadata:function t(){return this.messageHandler.sendWithPromise("GetMetadata",null).then(function t(e){return{info:e[0],metadata:e[1]?new M(e[1]):null}})},getStats:function t(){return this.messageHandler.sendWithPromise("GetStats",null)},startCleanup:function t(){this.messageHandler.sendWithPromise("Cleanup",null).then(function t(){for(var e=0,r=this.pageCache.length;e<r;e++){var n=this.pageCache[e];if(n){n.cleanup()}}this.commonObjs.clear();this.fontLoader.clear()}.bind(this))}};return e}();var tt=function t(){function e(){this.objs=Object.create(null)}e.prototype={ensureObj:function t(e){if(this.objs[e]){return this.objs[e]}var r={capability:y(),data:null,resolved:false};this.objs[e]=r;return r},get:function t(e,r){if(r){this.ensureObj(e).capability.promise.then(r);return null}var n=this.objs[e];if(!n||!n.resolved){x("Requesting object that isn't resolved yet "+e)}return n.data},resolve:function t(e,r){var n=this.ensureObj(e);n.resolved=true;n.data=r;n.capability.resolve(r)},isResolved:function t(e){var r=this.objs;if(!r[e]){return false}return r[e].resolved},hasData:function t(e){return this.isResolved(e)},getData:function t(e){var r=this.objs;if(!r[e]||!r[e].resolved){return null}return r[e].data},clear:function t(){this.objs=Object.create(null)}};return e}();var et=function t(){function e(t){this._internalRenderTask=t;this.onContinue=null}e.prototype={get promise(){return this._internalRenderTask.capability.promise},cancel:function t(){this._internalRenderTask.cancel()},then:function t(e,r){return this.promise.then.apply(this.promise,arguments)}};return e}();var rt=function t(){function e(t,e,r,n,a,i){this.callback=t;this.params=e;this.objs=r;this.commonObjs=n;this.operatorListIdx=null;this.operatorList=a;this.pageNumber=i;this.running=false;this.graphicsReadyCallback=null;this.graphicsReady=false;this.useRequestAnimationFrame=false;this.cancelled=false;this.capability=y();this.task=new et(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this)}e.prototype={initializeGraphics:function t(e){if(this.cancelled){return}if(N("pdfBug")&&R.StepperManager&&R.StepperManager.enabled){this.stepper=R.StepperManager.create(this.pageNumber-1);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}var r=this.params;this.gfx=new O(r.canvasContext,this.commonObjs,this.objs,r.imageLayer);this.gfx.beginDrawing(r.transform,r.viewport,e);this.operatorListIdx=0;this.graphicsReady=true;if(this.graphicsReadyCallback){this.graphicsReadyCallback()}},cancel:function t(){this.running=false;this.cancelled=true;this.callback("cancelled")},operatorListChanged:function t(){if(!this.graphicsReady){if(!this.graphicsReadyCallback){this.graphicsReadyCallback=this._continueBound}return}if(this.stepper){this.stepper.updateOperatorList(this.operatorList)}if(this.running){return}this._continue()},_continue:function t(){this.running=true;if(this.cancelled){return}if(this.task.onContinue){this.task.onContinue(this._scheduleNextBound)}else{this._scheduleNext()}},_scheduleNext:function t(){if(this.useRequestAnimationFrame&&typeof window!=="undefined"){window.requestAnimationFrame(this._nextBound)}else{Promise.resolve(undefined).then(this._nextBound)}},_next:function t(){if(this.cancelled){return}this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=false;if(this.operatorList.lastChunk){this.gfx.endDrawing();this.callback()}}}};return e}();var nt=function t(){var e=[];return{listen:function(t){S("Global UnsupportedManager.listen is used: "+" use PDFDocumentLoadingTask.onUnsupportedFeature instead");e.push(t)},notify:function(t){for(var r=0,n=e.length;r<n;r++){e[r](t)}}}}();if(typeof e!=="undefined"){t.version=e}if(typeof r!=="undefined"){t.build=r}t.getDocument=Y;t.PDFDataRangeTransport=J;t.PDFWorker=Z;t.PDFDocumentProxy=Q;t.PDFPageProxy=K;t._UnsupportedManager=nt});(function(t,e){e(t.pdfjsDisplayGlobal={},t.pdfjsSharedUtil,t.pdfjsDisplayDOMUtils,t.pdfjsDisplayAPI,t.pdfjsDisplayAnnotationLayer,t.pdfjsDisplayTextLayer,t.pdfjsDisplayMetadata,t.pdfjsDisplaySVG)})(this,function(t,n,a,i,s,o,l,c){var h=n.globalScope;var u=n.deprecated;var f=n.warn;var d=a.LinkTarget;var p=a.DEFAULT_LINK_REL;var v=typeof window==="undefined";if(!h.PDFJS){h.PDFJS={}}var g=h.PDFJS;if(typeof e!=="undefined"){g.version=e}if(typeof r!=="undefined"){g.build=r}g.pdfBug=false;if(g.verbosity!==undefined){n.setVerbosityLevel(g.verbosity)}delete g.verbosity;Object.defineProperty(g,"verbosity",{get:function(){return n.getVerbosityLevel()},set:function(t){n.setVerbosityLevel(t)},enumerable:true,configurable:true});g.VERBOSITY_LEVELS=n.VERBOSITY_LEVELS;g.OPS=n.OPS;g.UNSUPPORTED_FEATURES=n.UNSUPPORTED_FEATURES;g.isValidUrl=a.isValidUrl;g.shadow=n.shadow;g.createBlob=n.createBlob;g.createObjectURL=function t(e,r){return n.createObjectURL(e,r,g.disableCreateObjectURL)};Object.defineProperty(g,"isLittleEndian",{configurable:true,get:function t(){var e=n.isLittleEndian();return n.shadow(g,"isLittleEndian",e)}});g.removeNullCharacters=n.removeNullCharacters;g.PasswordResponses=n.PasswordResponses;g.PasswordException=n.PasswordException;g.UnknownErrorException=n.UnknownErrorException;g.InvalidPDFException=n.InvalidPDFException;g.MissingPDFException=n.MissingPDFException;g.UnexpectedResponseException=n.UnexpectedResponseException;g.Util=n.Util;g.PageViewport=n.PageViewport;g.createPromiseCapability=n.createPromiseCapability;g.maxImageSize=g.maxImageSize===undefined?-1:g.maxImageSize;g.cMapUrl=g.cMapUrl===undefined?null:g.cMapUrl;g.cMapPacked=g.cMapPacked===undefined?false:g.cMapPacked;g.disableFontFace=g.disableFontFace===undefined?false:g.disableFontFace;g.imageResourcesPath=g.imageResourcesPath===undefined?"":g.imageResourcesPath;g.disableWorker=g.disableWorker===undefined?false:g.disableWorker;g.workerSrc=g.workerSrc===undefined?null:g.workerSrc;g.disableRange=g.disableRange===undefined?false:g.disableRange;g.disableStream=g.disableStream===undefined?false:g.disableStream;g.disableAutoFetch=g.disableAutoFetch===undefined?false:g.disableAutoFetch;g.pdfBug=g.pdfBug===undefined?false:g.pdfBug;g.postMessageTransfers=g.postMessageTransfers===undefined?true:g.postMessageTransfers;g.disableCreateObjectURL=g.disableCreateObjectURL===undefined?false:g.disableCreateObjectURL;g.disableWebGL=g.disableWebGL===undefined?true:g.disableWebGL;g.externalLinkTarget=g.externalLinkTarget===undefined?d.NONE:g.externalLinkTarget;g.externalLinkRel=g.externalLinkRel===undefined?p:g.externalLinkRel;g.isEvalSupported=g.isEvalSupported===undefined?true:g.isEvalSupported;var m=g.openExternalLinksInNewWindow;delete g.openExternalLinksInNewWindow;Object.defineProperty(g,"openExternalLinksInNewWindow",{get:function(){return g.externalLinkTarget===d.BLANK},set:function(t){if(t){u("PDFJS.openExternalLinksInNewWindow, please use "+'"PDFJS.externalLinkTarget = PDFJS.LinkTarget.BLANK" instead.')}if(g.externalLinkTarget!==d.NONE){f("PDFJS.externalLinkTarget is already initialized");return}g.externalLinkTarget=t?d.BLANK:d.NONE},enumerable:true,configurable:true});if(m){g.openExternalLinksInNewWindow=m}g.getDocument=i.getDocument;g.PDFDataRangeTransport=i.PDFDataRangeTransport;g.PDFWorker=i.PDFWorker;Object.defineProperty(g,"hasCanvasTypedArrays",{configurable:true,get:function t(){var e=a.hasCanvasTypedArrays();return n.shadow(g,"hasCanvasTypedArrays",e)}});g.CustomStyle=a.CustomStyle;g.LinkTarget=d;g.addLinkAttributes=a.addLinkAttributes;g.getFilenameFromUrl=a.getFilenameFromUrl;g.isExternalLinkTargetSet=a.isExternalLinkTargetSet;g.AnnotationLayer=s.AnnotationLayer;g.renderTextLayer=o.renderTextLayer;g.Metadata=l.Metadata;g.SVGGraphics=c.SVGGraphics;g.UnsupportedManager=i._UnsupportedManager;t.globalScope=h;t.isWorker=v;t.PDFJS=h.PDFJS})}).call(a);t.PDFJS=a.pdfjsDisplayGlobal.PDFJS;t.build=a.pdfjsDisplayAPI.build;t.version=a.pdfjsDisplayAPI.version;t.getDocument=a.pdfjsDisplayAPI.getDocument;t.PDFDataRangeTransport=a.pdfjsDisplayAPI.PDFDataRangeTransport;t.PDFWorker=a.pdfjsDisplayAPI.PDFWorker;t.renderTextLayer=a.pdfjsDisplayTextLayer.renderTextLayer;t.AnnotationLayer=a.pdfjsDisplayAnnotationLayer.AnnotationLayer;t.CustomStyle=a.pdfjsDisplayDOMUtils.CustomStyle;t.PasswordResponses=a.pdfjsSharedUtil.PasswordResponses;t.InvalidPDFException=a.pdfjsSharedUtil.InvalidPDFException;t.MissingPDFException=a.pdfjsSharedUtil.MissingPDFException;t.SVGGraphics=a.pdfjsDisplaySVG.SVGGraphics;t.UnexpectedResponseException=a.pdfjsSharedUtil.UnexpectedResponseException;t.OPS=a.pdfjsSharedUtil.OPS;t.UNSUPPORTED_FEATURES=a.pdfjsSharedUtil.UNSUPPORTED_FEATURES;t.isValidUrl=a.pdfjsDisplayDOMUtils.isValidUrl;t.createValidAbsoluteUrl=a.pdfjsSharedUtil.createValidAbsoluteUrl;t.createObjectURL=a.pdfjsSharedUtil.createObjectURL;t.removeNullCharacters=a.pdfjsSharedUtil.removeNullCharacters;t.shadow=a.pdfjsSharedUtil.shadow;t.createBlob=a.pdfjsSharedUtil.createBlob;t.getFilenameFromUrl=a.pdfjsDisplayDOMUtils.getFilenameFromUrl;t.addLinkAttributes=a.pdfjsDisplayDOMUtils.addLinkAttributes});