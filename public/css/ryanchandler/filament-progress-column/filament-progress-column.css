*, ::before, ::after {
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  
}

.filament-tables-progress-column .flex {
  display: flex
}

.filament-tables-progress-column .h-2\.5 {
  height: 0.625rem
}

.filament-tables-progress-column .h-2 {
  height: 0.5rem
}

.filament-tables-progress-column .h-2\.5 {
  height: 0.625rem
}

.filament-tables-progress-column .h-2 {
  height: 0.5rem
}

.filament-tables-progress-column .h-2\.5 {
  height: 0.625rem
}

.filament-tables-progress-column .h-2 {
  height: 0.5rem
}

.filament-tables-progress-column .h-2\.5 {
  height: 0.625rem
}

.filament-tables-progress-column .h-2 {
  height: 0.5rem
}

.filament-tables-progress-column .h-2\.5 {
  height: 0.625rem
}

.filament-tables-progress-column .h-2 {
  height: 0.5rem
}

.filament-tables-progress-column .h-2\.5 {
  height: 0.625rem
}

.filament-tables-progress-column .h-2 {
  height: 0.5rem
}

.filament-tables-progress-column .h-2\.5 {
  height: 0.625rem
}

.filament-tables-progress-column .h-2 {
  height: 0.5rem
}

.filament-tables-progress-column .w-full {
  width: 100%
}

.filament-tables-progress-column .items-center {
  align-items: center
}

.filament-tables-progress-column .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
}

.filament-tables-progress-column .rounded-full {
  border-radius: 9999px
}

.filament-tables-progress-column .bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity))
}

.filament-tables-progress-column .px-4 {
  padding-left: 1rem;
  padding-right: 1rem
}

.filament-tables-progress-column .text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem
}

.filament-tables-progress-column .text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity))
}

.filament-tables-progress-column .dark .dark\:bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity))
}

.filament-tables-progress-column .dark .dark\:bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity))
}

.filament-tables-progress-column .dark .dark\:bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity))
}

.filament-tables-progress-column .dark .dark\:bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity))
}

.filament-tables-progress-column .dark .dark\:bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity))
}

.filament-tables-progress-column .dark .dark\:bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity))
}

.filament-tables-progress-column .dark .dark\:text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity))
}