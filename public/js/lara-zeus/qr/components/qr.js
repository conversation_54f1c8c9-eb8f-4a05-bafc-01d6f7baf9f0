var ce=Object.create;var Y=Object.defineProperty;var le=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var fe=Object.getPrototypeOf,he=Object.prototype.hasOwnProperty;var Z=(v,e)=>()=>(e||v((e={exports:{}}).exports,e),e.exports);var de=(v,e,y,x)=>{if(e&&typeof e=="object"||typeof e=="function")for(let m of se(e))!he.call(v,m)&&m!==y&&Y(v,m,{get:()=>e[m],enumerable:!(x=le(e,m))||x.enumerable});return v};var ee=(v,e,y)=>(y=v!=null?ce(fe(v)):{},de(e||!v||!v.__esModule?Y(y,"default",{value:v,enumerable:!0}):y,v));var ne=Z((te,W)=>{(function(v){"use strict";var e=oe(),y=ie(),x=ae(),m=ue(),V={imagePlaceholder:void 0,cacheBust:!1},E={toSvg:s,toPng:S,toJpeg:p,toBlob:P,toPixelData:f,impl:{fontFaces:x,images:m,util:e,inliner:y,options:{}}};typeof W<"u"?W.exports=E:v.domtoimage=E;function s(r,n){return n=n||{},G(n),Promise.resolve(r).then(function(a){return H(a,n.filter,!0)}).then(q).then(N).then(o).then(function(a){return I(a,n.width||e.width(r),n.height||e.height(r))});function o(a){return n.bgcolor&&(a.style.backgroundColor=n.bgcolor),n.width&&(a.style.width=n.width+"px"),n.height&&(a.style.height=n.height+"px"),n.style&&Object.keys(n.style).forEach(function(h){a.style[h]=n.style[h]}),a}}function f(r,n){return D(r,n||{}).then(function(o){return o.getContext("2d").getImageData(0,0,e.width(r),e.height(r)).data})}function S(r,n){return D(r,n||{}).then(function(o){return o.toDataURL()})}function p(r,n){return n=n||{},D(r,n).then(function(o){return o.toDataURL("image/jpeg",n.quality||1)})}function P(r,n){return D(r,n||{}).then(e.canvasToBlob)}function G(r){typeof r.imagePlaceholder>"u"?E.impl.options.imagePlaceholder=V.imagePlaceholder:E.impl.options.imagePlaceholder=r.imagePlaceholder,typeof r.cacheBust>"u"?E.impl.options.cacheBust=V.cacheBust:E.impl.options.cacheBust=r.cacheBust}function D(r,n){return s(r,n).then(e.makeImage).then(e.delay(100)).then(function(a){var h=o(r);return h.getContext("2d").drawImage(a,0,0),h});function o(a){var h=document.createElement("canvas");if(h.width=n.width||e.width(a),h.height=n.height||e.height(a),n.bgcolor){var l=h.getContext("2d");l.fillStyle=n.bgcolor,l.fillRect(0,0,h.width,h.height)}return h}}function H(r,n,o){if(!o&&n&&!n(r))return Promise.resolve();return Promise.resolve(r).then(a).then(function(i){return h(r,i,n)}).then(function(i){return l(r,i)});function a(i){return i instanceof HTMLCanvasElement?e.makeImage(i.toDataURL()):i.cloneNode(!1)}function h(i,u,T){var L=i.childNodes;if(L.length===0)return Promise.resolve(u);return w(u,e.asArray(L),T).then(function(){return u});function w(F,U,b){var R=Promise.resolve();return U.forEach(function(O){R=R.then(function(){return H(O,b)}).then(function(C){C&&F.appendChild(C)})}),R}}function l(i,u){if(!(u instanceof Element))return u;return Promise.resolve().then(T).then(L).then(w).then(F).then(function(){return u});function T(){U(window.getComputedStyle(i),u.style);function U(b,R){b.cssText?R.cssText=b.cssText:O(b,R);function O(C,j){e.asArray(C).forEach(function(t){j.setProperty(t,C.getPropertyValue(t),C.getPropertyPriority(t))})}}}function L(){[":before",":after"].forEach(function(b){U(b)});function U(b){var R=window.getComputedStyle(i,b),O=R.getPropertyValue("content");if(O===""||O==="none")return;var C=e.uid();u.className=u.className+" "+C;var j=document.createElement("style");j.appendChild(t(C,b,R)),u.appendChild(j);function t(c,g,d){var A="."+c+":"+g,B=d.cssText?_(d):$(d);return document.createTextNode(A+"{"+B+"}");function _(k){var M=k.getPropertyValue("content");return k.cssText+" content: "+M+";"}function $(k){return e.asArray(k).map(M).join("; ")+";";function M(X){return X+": "+k.getPropertyValue(X)+(k.getPropertyPriority(X)?" !important":"")}}}}}function w(){i instanceof HTMLTextAreaElement&&(u.innerHTML=i.value),i instanceof HTMLInputElement&&u.setAttribute("value",i.value)}function F(){u instanceof SVGElement&&(u.setAttribute("xmlns","http://www.w3.org/2000/svg"),u instanceof SVGRectElement&&["width","height"].forEach(function(U){var b=u.getAttribute(U);b&&u.style.setProperty(U,b)}))}}}function q(r){return x.resolveAll().then(function(n){var o=document.createElement("style");return r.appendChild(o),o.appendChild(document.createTextNode(n)),r})}function N(r){return m.inlineAll(r).then(function(){return r})}function I(r,n,o){return Promise.resolve(r).then(function(a){return a.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),new XMLSerializer().serializeToString(a)}).then(e.escapeXhtml).then(function(a){return'<foreignObject x="0" y="0" width="100%" height="100%">'+a+"</foreignObject>"}).then(function(a){return'<svg xmlns="http://www.w3.org/2000/svg" width="'+n+'" height="'+o+'">'+a+"</svg>"}).then(function(a){return"data:image/svg+xml;charset=utf-8,"+a})}function oe(){return{escape:F,parseExtension:n,mimeType:o,dataAsUrl:w,isDataUrl:a,canvasToBlob:l,resolveUrl:i,getAndEncode:L,uid:u(),delay:U,asArray:b,escapeXhtml:R,makeImage:T,width:O,height:C};function r(){var t="application/font-woff",c="image/jpeg";return{woff:t,woff2:t,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:c,jpeg:c,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml"}}function n(t){var c=/\.([^\.\/]*?)$/g.exec(t);return c?c[1]:""}function o(t){var c=n(t).toLowerCase();return r()[c]||""}function a(t){return t.search(/^(data:)/)!==-1}function h(t){return new Promise(function(c){for(var g=window.atob(t.toDataURL().split(",")[1]),d=g.length,A=new Uint8Array(d),B=0;B<d;B++)A[B]=g.charCodeAt(B);c(new Blob([A],{type:"image/png"}))})}function l(t){return t.toBlob?new Promise(function(c){t.toBlob(c)}):h(t)}function i(t,c){var g=document.implementation.createHTMLDocument(),d=g.createElement("base");g.head.appendChild(d);var A=g.createElement("a");return g.body.appendChild(A),d.href=c,A.href=t,A.href}function u(){var t=0;return function(){return"u"+c()+t++;function c(){return("0000"+(Math.random()*Math.pow(36,4)<<0).toString(36)).slice(-4)}}}function T(t){return new Promise(function(c,g){var d=new Image;d.onload=function(){c(d)},d.onerror=g,d.src=t})}function L(t){var c=3e4;return E.impl.options.cacheBust&&(t+=(/\?/.test(t)?"&":"?")+new Date().getTime()),new Promise(function(g){var d=new XMLHttpRequest;d.onreadystatechange=_,d.ontimeout=$,d.responseType="blob",d.timeout=c,d.open("GET",t,!0),d.send();var A;if(E.impl.options.imagePlaceholder){var B=E.impl.options.imagePlaceholder.split(/,/);B&&B[1]&&(A=B[1])}function _(){if(d.readyState===4){if(d.status!==200){A?g(A):k("cannot fetch resource: "+t+", status: "+d.status);return}var M=new FileReader;M.onloadend=function(){var X=M.result.split(/,/)[1];g(X)},M.readAsDataURL(d.response)}}function $(){A?g(A):k("timeout of "+c+"ms occured while fetching resource: "+t)}function k(M){console.error(M),g("")}})}function w(t,c){return"data:"+c+";base64,"+t}function F(t){return t.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1")}function U(t){return function(c){return new Promise(function(g){setTimeout(function(){g(c)},t)})}}function b(t){for(var c=[],g=t.length,d=0;d<g;d++)c.push(t[d]);return c}function R(t){return t.replace(/#/g,"%23").replace(/\n/g,"%0A")}function O(t){var c=j(t,"border-left-width"),g=j(t,"border-right-width");return t.scrollWidth+c+g}function C(t){var c=j(t,"border-top-width"),g=j(t,"border-bottom-width");return t.scrollHeight+c+g}function j(t,c){var g=window.getComputedStyle(t).getPropertyValue(c);return parseFloat(g.replace("px",""))}}function ie(){var r=/url\(['"]?([^'"]+?)['"]?\)/g;return{inlineAll:h,shouldProcess:n,impl:{readUrls:o,inline:a}};function n(l){return l.search(r)!==-1}function o(l){for(var i=[],u;(u=r.exec(l))!==null;)i.push(u[1]);return i.filter(function(T){return!e.isDataUrl(T)})}function a(l,i,u,T){return Promise.resolve(i).then(function(w){return u?e.resolveUrl(w,u):w}).then(T||e.getAndEncode).then(function(w){return e.dataAsUrl(w,e.mimeType(i))}).then(function(w){return l.replace(L(i),"$1"+w+"$3")});function L(w){return new RegExp(`(url\\(['"]?)(`+e.escape(w)+`)(['"]?\\))`,"g")}}function h(l,i,u){if(T())return Promise.resolve(l);return Promise.resolve(l).then(o).then(function(L){var w=Promise.resolve(l);return L.forEach(function(F){w=w.then(function(U){return a(U,F,i,u)})}),w});function T(){return!n(l)}}}function ae(){return{resolveAll:r,impl:{readAll:n}};function r(){return n(document).then(function(o){return Promise.all(o.map(function(a){return a.resolve()}))}).then(function(o){return o.join(`
`)})}function n(){return Promise.resolve(e.asArray(document.styleSheets)).then(a).then(o).then(function(l){return l.map(h)});function o(l){return l.filter(function(i){return i.type===CSSRule.FONT_FACE_RULE}).filter(function(i){return y.shouldProcess(i.style.getPropertyValue("src"))})}function a(l){var i=[];return l.forEach(function(u){try{e.asArray(u.cssRules||[]).forEach(i.push.bind(i))}catch(T){console.log("Error while reading CSS rules from "+u.href,T.toString())}}),i}function h(l){return{resolve:function(){var u=(l.parentStyleSheet||{}).href;return y.inlineAll(l.cssText,u)},src:function(){return l.style.getPropertyValue("src")}}}}}function ue(){return{inlineAll:n,impl:{newImage:r}};function r(o){return{inline:a};function a(h){return e.isDataUrl(o.src)?Promise.resolve():Promise.resolve(o.src).then(h||e.getAndEncode).then(function(l){return e.dataAsUrl(l,e.mimeType(o.src))}).then(function(l){return new Promise(function(i,u){o.onload=i,o.onerror=u,o.src=l})})}}function n(o){if(!(o instanceof Element))return Promise.resolve(o);return a(o).then(function(){return o instanceof HTMLImageElement?r(o).inline():Promise.all(e.asArray(o.childNodes).map(function(h){return n(h)}))});function a(h){var l=h.style.getPropertyValue("background");return l?y.inlineAll(l).then(function(i){h.style.setProperty("background",i,h.style.getPropertyPriority("background"))}).then(function(){return h}):Promise.resolve(h)}}}})(te)});var re=Z((J,z)=>{(function(v,e){typeof define=="function"&&define.amd?define([],e):typeof J<"u"?e():(e(),v.FileSaver={})})(J,function(){"use strict";function v(s,f){return typeof f>"u"?f={autoBom:!1}:typeof f!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),f={autoBom:!f}),f.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(s.type)?new Blob(["\uFEFF",s],{type:s.type}):s}function e(s,f,S){var p=new XMLHttpRequest;p.open("GET",s),p.responseType="blob",p.onload=function(){E(p.response,f,S)},p.onerror=function(){console.error("could not download file")},p.send()}function y(s){var f=new XMLHttpRequest;f.open("HEAD",s,!1);try{f.send()}catch{}return 200<=f.status&&299>=f.status}function x(s){try{s.dispatchEvent(new MouseEvent("click"))}catch{var f=document.createEvent("MouseEvents");f.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),s.dispatchEvent(f)}}var m=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:void 0,V=m.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),E=m.saveAs||(typeof window!="object"||window!==m?function(){}:"download"in HTMLAnchorElement.prototype&&!V?function(s,f,S){var p=m.URL||m.webkitURL,P=document.createElement("a");f=f||s.name||"download",P.download=f,P.rel="noopener",typeof s=="string"?(P.href=s,P.origin===location.origin?x(P):y(P.href)?e(s,f,S):x(P,P.target="_blank")):(P.href=p.createObjectURL(s),setTimeout(function(){p.revokeObjectURL(P.href)},4e4),setTimeout(function(){x(P)},0))}:"msSaveOrOpenBlob"in navigator?function(s,f,S){if(f=f||s.name||"download",typeof s!="string")navigator.msSaveOrOpenBlob(v(s,S),f);else if(y(s))e(s,f,S);else{var p=document.createElement("a");p.href=s,p.target="_blank",setTimeout(function(){x(p)})}}:function(s,f,S,p){if(p=p||open("","_blank"),p&&(p.document.title=p.document.body.innerText="downloading..."),typeof s=="string")return e(s,f,S);var P=s.type==="application/octet-stream",G=/constructor/i.test(m.HTMLElement)||m.safari,D=/CriOS\/[\d]+/.test(navigator.userAgent);if((D||P&&G||V)&&typeof FileReader<"u"){var H=new FileReader;H.onloadend=function(){var I=H.result;I=D?I:I.replace(/^data:[^;]*;/,"data:attachment/file;"),p?p.location.href=I:location=I,p=null},H.readAsDataURL(s)}else{var q=m.URL||m.webkitURL,N=q.createObjectURL(s);p?p.location=N:location.href=N,p=null,setTimeout(function(){q.revokeObjectURL(N)},4e4)}});m.saveAs=E.saveAs=E,typeof z<"u"&&(z.exports=E)})});var K=ee(ne(),1),Q=ee(re(),1);function me({state:v}){return{state:v,init:function(){},download:function(e,y){var x=this.$refs.qr;y==="svg"?K.default.toSvg(x).then(function(m){(0,Q.saveAs)(m,e+".svg")}).catch(function(m){console.error("oops, something went wrong!",m)}):K.default.toPng(x).then(function(m){(0,Q.saveAs)(m,e+".png")}).catch(function(m){console.error("oops, something went wrong!",m)})}}}export{me as default};
