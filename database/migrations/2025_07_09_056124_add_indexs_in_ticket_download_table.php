<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('comments')) {
            Schema::table('comments', function (Blueprint $table) {
                if (!$this->hasIndex('comments', 'user_id_ticket_id_idx')) {
                    $table->index( ['user_id', 'ticket_id']);
                }
            });
        }
        if (Schema::hasTable('downloads')) {
            Schema::table('downloads', function (Blueprint $table) {
                if (!$this->hasIndex('downloads', 'user_id_campaign_id_idx')) {
                    $table->index( ['user_id', 'campaign_id']);
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        
    }
    
    /**
     * Check if an index exists on a table
     */
    private function hasIndex(string $table, string $index): bool
    {
        try {
            $indexes = Schema::getConnection()->getDoctrineSchemaManager()
                ->listTableIndexes($table);
            return array_key_exists($index, $indexes);
        } catch (\Exception $e) {
            return false;
        }
    }
};
