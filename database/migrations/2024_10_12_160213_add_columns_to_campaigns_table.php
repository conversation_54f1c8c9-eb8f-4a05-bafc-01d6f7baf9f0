<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->renameColumn('name', 'topic');
            $table->text('title')->nullable()->after("status");
            $table->integer('page_length')->after("status");
            $table->integer('required_word_length')->nullable()->after("status");
            $table->integer('total_word_length')->nullable()->after("status");
            $table->foreignIdFor(\App\Models\EbookFormat::class, 'ebook_format_id')->index()->after("user_id");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->renameColumn('topic', 'name');
            $table->dropColumn('title');
            $table->dropColumn('page_length');
            $table->dropColumn('required_word_length');
            $table->dropColumn('total_word_length');
            // $table->dropForeign('campaigns_ebook_format_id_foreign');
        });
    }
};
