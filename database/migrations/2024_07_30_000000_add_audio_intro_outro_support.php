<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add audio enhancement columns to campaigns table
        Schema::table('campaigns', function (Blueprint $table) {
            // These will be stored in the meta JSON column, but we can add dedicated columns if needed
            // For now, we'll use the existing meta column structure
        });

        // Add audio enhancement columns to chapters table for storing intro/outro metadata
        Schema::table('chapters', function (Blueprint $table) {
            // These will be stored in the meta JSON column as well
            // The meta column will store audio_models with enhanced structure:
            // {
            //   "audio_models": {
            //     "alloy": {
            //       "s3_path": "...",
            //       "generated_at": "...",
            //       "file_size": null,
            //       "has_intro": true,
            //       "intro_text": "Welcome to Chapter 1...",
            //       "has_outro": false,
            //       "outro_text": null
            //     }
            //   }
            // }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No changes needed as we're using existing meta columns
    }
};
