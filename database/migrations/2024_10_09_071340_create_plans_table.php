<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('type')->nullable();
            $table->integer('credits')->default(0);
            $table->float('amount')->nullable();
            $table->text('description')->nullable();
            $table->json('permissions')->nullable();
            $table->tinyInteger('show')->nullable();
            $table->string('wplus_code')->nullable();
            $table->boolean('is_unlimited')->default(false);
            $table->timestamps();

            $table->index(['slug']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
