<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::table('sections', function (Blueprint $table) {
        //     $table->integer('order')->default(0)->after('chapter_id');
        // });

        // // Update existing sections to have proper order values
        // $chapters = \App\Models\Chapter::with('sections')->get();
        // foreach ($chapters as $chapter) {
        //     $sections = $chapter->sections()->orderBy('id')->get();
        //     foreach ($sections as $index => $section) {
        //         $section->update(['order' => $index + 1]);
        //     }
        // }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sections', function (Blueprint $table) {
            $table->dropColumn('order');
        });
    }
};
