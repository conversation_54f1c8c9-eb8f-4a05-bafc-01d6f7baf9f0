<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ebook_formats', function (Blueprint $table) {
            // Add background opacity field
            $table->integer('background_opacity')->default(70);

            // Add margin-related fields if they don't exist
            if (!Schema::hasColumn('ebook_formats', 'margin_top')) {
                $table->string('margin_top')->nullable();
            }
            if (!Schema::hasColumn('ebook_formats', 'margin_bottom')) {
                $table->string('margin_bottom')->nullable();
            }
            if (!Schema::hasColumn('ebook_formats', 'margin_left')) {
                $table->string('margin_left')->nullable();
            }
            if (!Schema::hasColumn('ebook_formats', 'margin_right')) {
                $table->string('margin_right')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ebook_formats', function (Blueprint $table) {
            // Drop background opacity field
            $table->dropColumn('background_opacity');

            // Drop margin-related fields if they exist
            if (Schema::hasColumn('ebook_formats', 'margin_top')) {
                $table->dropColumn('margin_top');
            }
            if (Schema::hasColumn('ebook_formats', 'margin_bottom')) {
                $table->dropColumn('margin_bottom');
            }
            if (Schema::hasColumn('ebook_formats', 'margin_left')) {
                $table->dropColumn('margin_left');
            }
            if (Schema::hasColumn('ebook_formats', 'margin_right')) {
                $table->dropColumn('margin_right');
            }
        });
    }
};
