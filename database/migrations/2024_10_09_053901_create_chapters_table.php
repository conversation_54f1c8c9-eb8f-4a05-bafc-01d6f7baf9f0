<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chapters', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ebook_id')->index();
            $table->text('title');
            $table->text('intro')->nullable();
            $table->integer('chapter_number');
            $table->integer('chapter_total_words')->nullable();
            $table->enum('status', ['PENDING', 'IN PROGRESS', 'DONE', 'ERROR', 'FAILED'])->default('PENDING');
            $table->json('meta')->nullable();
            $table->timestamps();
            $table->foreign('ebook_id')->references('id')->on('ebooks')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chapters');
    }
};
