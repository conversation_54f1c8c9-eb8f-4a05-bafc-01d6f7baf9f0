<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('research_campaign_entries', function (Blueprint $table) {
            $table->id();
            $table->text("entry");
            $table->json("entry_content")->nullable();
            $table->boolean("favorite")->default(false);
            $table->foreignIdFor(\App\Models\ResearchEbookCampaign::class)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('research_campaign_entries');
    }
};
