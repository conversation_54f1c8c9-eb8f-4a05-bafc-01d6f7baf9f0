<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ebooks', function (Blueprint $table) {
            $table->id();
            $table->text('topic');
            $table->text('title');
            $table->string('audience')->nullable();
            $table->string('tone_style')->nullable();
            $table->string('language');
            $table->json('keywords')->nullable();
            $table->integer('page_length');
            $table->integer('required_word_length')->nullable();
            $table->integer('total_word_length')->nullable();
            $table->enum('status', ['PENDING', 'IN PROGRESS', 'DONE', 'PUBLISHED', 'ERROR', 'PUBLISHING ERROR', 'FAILED'])->default('PENDING');
            $table->json('meta')->nullable();
            $table->timestamps();
            $table->foreignIdFor(\App\Models\EbookFormat::class, 'ebook_format_id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ebooks');
    }
};
