<?php

use App\Models\Plan;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('payment_method', 20);
            $table->string('status', 35)->index()->nullable();
            $table->json('meta')->nullable();
            $table->foreignIdFor(User::class);
            $table->foreignIdFor(Plan::class);
            $table->timestamp('subscribed_at')->nullable();
            $table->timestamp('renew_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->string('wplus_purchase_id')->nullable();
            $table->string('wplus_txnid')->nullable();
            $table->string('wplus_sale_id')->nullable();
            $table->integer('quantity')->nullable();
            $table->timestamps();

            $table->index(['plan_id', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
