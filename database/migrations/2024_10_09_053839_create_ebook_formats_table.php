<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ebook_formats', function (Blueprint $table) {
            $table->id();
            $table->string('font')->default("Arial");
            $table->float('font_size')->default(12);
            $table->integer('heading')->default(28);
            $table->integer('sub_heading')->default(24);
            $table->float('line_space')->default(1.5);
            $table->integer('paragraph_space')->default(12);
            $table->string('page_size')->default("A4");
            $table->json('margins')->nullable();
            $table->string('text_align')->default("left");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ebook_formats');
    }
};
