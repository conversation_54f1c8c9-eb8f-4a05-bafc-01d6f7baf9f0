<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all sections that have image_source in meta but no img tag in intro
        $sections = DB::table('sections')
            ->whereNotNull('meta')
            ->whereRaw("JSON_EXTRACT(meta, '$.image_source') IS NOT NULL")
            ->whereNotNull('intro')
            ->where('intro', '!=', '')
            ->get();

        foreach ($sections as $section) {
            // Parse the meta JSON to get image_source
            $meta = json_decode($section->meta, true);

            if (!isset($meta['image_source']) || empty($meta['image_source'])) {
                continue;
            }

            $imageUrl = $meta['image_source'];
            $intro = $section->intro;

            // Check if intro already contains an img tag
            if (strpos($intro, '<img') !== false) {
                echo "Section ID {$section->id}: Skipping - already has img tag\n";
                continue;
            }

            // Get section title for alt text (strip HTML tags)
            $altText = strip_tags($section->title ?? 'Section Image');

            // Add img tag at the end of intro
            $updatedIntro = $intro . "\n<img src=\"{$imageUrl}\" alt=\"{$altText}\">";

            // Update the section
            DB::table('sections')
                ->where('id', $section->id)
                ->update(['intro' => $updatedIntro]);

            echo "Section ID {$section->id}: Added image tag to intro\n";
        }

        echo "Migration completed successfully!\n";
    }
};
