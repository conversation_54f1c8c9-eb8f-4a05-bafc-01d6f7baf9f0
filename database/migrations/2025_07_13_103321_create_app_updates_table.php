<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_updates', function (Blueprint $table) {
            $table->id();
            $table->string('version')->unique(); // e.g., "2.1.0"
            $table->string('title'); // e.g., "Major Feature Update"
            $table->text('summary'); // Brief description for emails
            $table->longText('content'); // Full changelog content (markdown supported)
            $table->enum('type', ['major', 'minor', 'patch'])->default('minor');
            $table->enum('category', ['features', 'fixes', 'improvements', 'security'])->default('features');
            $table->boolean('is_published')->default(false);
            $table->boolean('send_email_notification')->default(false);
            $table->timestamp('email_sent_at')->nullable();
            $table->json('metadata')->nullable(); // For additional data like images, links, etc.
            $table->timestamp('released_at')->nullable();
            $table->timestamps();

            $table->index(['is_published', 'released_at']);
            $table->index(['type', 'category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_updates');
    }
};
