<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_update_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->boolean('email_major_updates')->default(true);
            $table->boolean('email_minor_updates')->default(false);
            $table->boolean('email_patch_updates')->default(false);
            $table->boolean('email_security_updates')->default(true);
            $table->json('categories')->nullable(); // Array of categories user wants to receive
            $table->string('unsubscribe_token')->unique()->nullable(); // For unsubscribe links
            $table->timestamp('last_email_sent_at')->nullable();
            $table->timestamps();

            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_update_preferences');
    }
};
