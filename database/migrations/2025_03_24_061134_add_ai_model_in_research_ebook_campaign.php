<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('research_ebook_campaigns', function (Blueprint $table) {
            $table->string('ai_model')->after('status')->default(\App\Enum\AIModelEnum::OPEN_AI->value);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('research_ebook_campaigns', function (Blueprint $table) {
            $table->dropColumn('ai_model');
        });
    }
};
