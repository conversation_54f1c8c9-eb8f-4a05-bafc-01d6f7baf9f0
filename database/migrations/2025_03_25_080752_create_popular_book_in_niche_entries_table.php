<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('popular_book_in_niche_entries', function (Blueprint $table) {
            $table->id();
            $table->text("author");
            $table->text("summary");
            $table->json("key_takeways")->nullable();
            $table->boolean("favorite")->default(false);
            $table->foreignIdFor(\App\Models\ResearchEbookCampaign::class)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('popular_book_in_niche_entries');
    }
};
