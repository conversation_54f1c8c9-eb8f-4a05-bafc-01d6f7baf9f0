<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing campaigns that have the old field name
        DB::table('campaigns')
            ->whereNotNull('form')
            ->chunkById(100, function ($campaigns) {
                foreach ($campaigns as $campaign) {
                    $form = json_decode($campaign->form, true);
                    if (!isset($form['generate_cover_image'])) {
                        $form['generate_cover_image'] = isset($form['generate_ai_cover_image']) && $form['generate_ai_cover_image'] ? 'ai_generate' : 'custom_upload';
                        unset($form['generate_ai_cover_image']);
                        
                        DB::table('campaigns')
                            ->where('id', $campaign->id)
                            ->update(['form' => json_encode($form)]);
                    }
                }
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        
    }
};
