<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add soft deletes to downloads table
        Schema::table('downloads', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to chapters table
        Schema::table('chapters', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to sections table
        Schema::table('sections', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to campaign_reviews table if it exists
        if (Schema::hasTable('campaign_reviews')) {
            Schema::table('campaign_reviews', function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove soft deletes from downloads table
        Schema::table('downloads', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from chapters table
        Schema::table('chapters', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from sections table
        Schema::table('sections', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from campaign_reviews table if it exists
        if (Schema::hasTable('campaign_reviews')) {
            Schema::table('campaign_reviews', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }
    }
};
