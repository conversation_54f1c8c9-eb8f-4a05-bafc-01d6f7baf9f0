<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_ai_models', function (Blueprint $table) {
            $table->id();
            $table->string('ai_model');
            $table->string('api_key');
            $table->boolean('has_gpt4')->default(false);
            $table->boolean('quota_exceeded')->default(false);
            $table->foreignIdFor(\App\Models\User::class)->constrained();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_ai_models');
    }
};
