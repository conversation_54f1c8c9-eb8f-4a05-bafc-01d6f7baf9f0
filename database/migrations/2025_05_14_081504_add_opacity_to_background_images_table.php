<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('background_images', function (Blueprint $table) {
            $table->float('opacity')->default(1.0)->comment('Opacity value between 0 and 1, default is 1 (fully opaque)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('background_images', function (Blueprint $table) {
            $table->dropColumn('opacity');
        });
    }
};
