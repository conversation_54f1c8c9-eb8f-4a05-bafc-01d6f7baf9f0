<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_ai_models', function (Blueprint $table) {
            $table->boolean('has_gpt_image_1')->default(false)->after('has_gpt4');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_ai_models', function (Blueprint $table) {
            $table->dropColumn('has_gpt_image_1');
        });
    }
};
