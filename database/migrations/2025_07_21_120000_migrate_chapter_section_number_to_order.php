<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate data from chapter_section_number to order field
        // Only update where chapter_section_number is not null and order is 0 or null
        DB::statement("
            UPDATE sections 
            SET `order` = chapter_section_number 
            WHERE chapter_section_number IS NOT NULL 
            AND chapter_section_number > 0
            AND (`order` = 0 OR `order` IS NULL)
        ");
        
        // For any remaining sections with order = 0 or null, set them based on their position within the chapter
        $chapters = DB::table('chapters')->get();
        
        foreach ($chapters as $chapter) {
            $sections = DB::table('sections')
                ->where('chapter_id', $chapter->id)
                ->where(function($query) {
                    $query->where('order', 0)->orWhereNull('order');
                })
                ->orderBy('id')
                ->get();
            
            foreach ($sections as $index => $section) {
                DB::table('sections')
                    ->where('id', $section->id)
                    ->update(['order' => $index + 1]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Optionally, we could reverse this by copying order back to chapter_section_number
        // But since we're moving away from chapter_section_number, we'll leave this empty
    }
};
