<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('templates', function (Blueprint $table) {
            // Add margin-related fields
            $table->string('margin_top')->nullable();
            $table->string('margin_bottom')->nullable();
            $table->string('margin_left')->nullable();
            $table->string('margin_right')->nullable();

            // Add background opacity field
            $table->integer('background_opacity')->default(70);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('templates', function (Blueprint $table) {
            // Drop margin-related fields
            $table->dropColumn('margin_top');
            $table->dropColumn('margin_bottom');
            $table->dropColumn('margin_left');
            $table->dropColumn('margin_right');

            // Drop background opacity field
            $table->dropColumn('background_opacity');
        });
    }
};
