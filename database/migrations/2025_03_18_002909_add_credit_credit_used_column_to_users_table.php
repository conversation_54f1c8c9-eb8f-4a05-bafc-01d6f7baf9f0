<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn("users", "credits")) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('credits');
            });
        }
        if (Schema::hasColumn("users", "credits_used")) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('credits_used');
            });
        }

        Schema::table('users', function (Blueprint $table) {
            $table->double('credits')->after('slug')->default(0);
            $table->double('credits_used')->after('credits')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn("users", "credits")) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('credits');
            });
        }
        if (Schema::hasColumn("users", "credits_used")) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('credits_used');
            });
        }
    }
};
