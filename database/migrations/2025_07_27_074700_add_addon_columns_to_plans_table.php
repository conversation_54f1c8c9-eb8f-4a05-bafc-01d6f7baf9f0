<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Add page limit column (nullable for backward compatibility)
            $table->integer('page_limit')->nullable()->after('credits');

            // Add includes_all_addons column (default true for backward compatibility)
            $table->boolean('includes_all_addons')->default(false)->after('page_limit');

            // Add effective_date column to mark new pricing model start date
            $table->date('effective_date')->nullable()->after('includes_all_addons');

            // Add indexes for performance
            $table->index(['includes_all_addons']);
            $table->index(['effective_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropIndex(['includes_all_addons']);
            $table->dropIndex(['effective_date']);
            $table->dropColumn(['page_limit', 'includes_all_addons', 'effective_date']);
        });
    }
};
