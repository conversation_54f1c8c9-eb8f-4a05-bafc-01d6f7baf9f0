<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('popular_book_in_niche_entries', function (Blueprint $table) {
            $table->string("book_name")->nullable()->after("author");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('popular_book_in_niche_entries', function (Blueprint $table) {
            $table->dropColumn("book_name");
        });
    }
};
