<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('campaigns')) {
            Schema::table('campaigns', function (Blueprint $table) {
                // Index for status queries (RetryStuckCampaignsCommand)
                if (!$this->hasIndex('campaigns', 'campaigns_status_updated_at_scheduler_retry_index')) {
                    $table->index(['status', 'updated_at', 'scheduler_retry'], 'campaigns_status_updated_at_scheduler_retry_index');
                }
                
                // Index for user campaigns
                if (!$this->hasIndex('campaigns', 'campaigns_user_id_status_index')) {
                    $table->index(['user_id', 'status']);
                }
                
                // Index for created_at queries
                if (!$this->hasIndex('campaigns', 'campaigns_created_at_index')) {
                    $table->index('created_at');
                }
            });
        }

        // Users table indexes
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                // Index for role-based queries
                if (!$this->hasIndex('users', 'users_role_index')) {
                    $table->index('role');
                }
                
                // Index for email verification
                if (!$this->hasIndex('users', 'users_email_verified_at_index')) {
                    $table->index('email_verified_at');
                }
            });
        }

        // Subscriptions table indexes
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                // Composite index for user subscription queries
                if (!$this->hasIndex('subscriptions', 'subscriptions_user_id_status_index')) {
                    $table->index(['user_id', 'status']);
                }
                
                // Index for plan-based queries
                if (!$this->hasIndex('subscriptions', 'subscriptions_plan_id_index')) {
                    $table->index('plan_id');
                }
                
                // Index for date-based queries
                if (!$this->hasIndex('subscriptions', 'subscriptions_created_at_index')) {
                    $table->index('created_at');
                }
            });
        }

        // Credit logs table indexes
        if (Schema::hasTable('credit_logs')) {
            Schema::table('credit_logs', function (Blueprint $table) {
                // Composite index for user credit history
                if (!$this->hasIndex('credit_logs', 'credit_logs_user_id_created_at_index')) {
                    $table->index(['user_id', 'created_at']);
                }
                
                // Index for action-based queries
                if (!$this->hasIndex('credit_logs', 'credit_logs_action_index')) {
                    $table->index('action');
                }
                
                // Index for action_type queries
                if (!$this->hasIndex('credit_logs', 'credit_logs_action_type_index')) {
                    $table->index('action_type');
                }
            });
        }

        // Chapters table indexes
        if (Schema::hasTable('chapters')) {
            Schema::table('chapters', function (Blueprint $table) {
                // Index for campaign chapters
                if (!$this->hasIndex('chapters', 'chapters_campaign_id_index')) {
                    $table->index('campaign_id');
                }
            });
        }

        // Sections table indexes
        if (Schema::hasTable('sections')) {
            Schema::table('sections', function (Blueprint $table) {
                // Index for chapter sections
                if (!$this->hasIndex('sections', 'sections_chapter_id_index')) {
                    $table->index('chapter_id');
                }
                
                // Index for campaign sections
                if (!$this->hasIndex('sections', 'sections_campaign_id_index')) {
                    $table->index('campaign_id');
                }
            });
        }

        // Tickets table indexes (if exists)
        if (Schema::hasTable('tickets')) {
            Schema::table('tickets', function (Blueprint $table) {
                // Index for user tickets
                if (!$this->hasIndex('tickets', 'tickets_user_id_index')) {
                    $table->index('user_id');
                }
                
                // Index for status-based queries
                if (!$this->hasIndex('tickets', 'tickets_status_index')) {
                    $table->index('status');
                }
                
                // Index for priority-based queries
                if (!$this->hasIndex('tickets', 'tickets_priority_index')) {
                    $table->index('priority');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the indexes in reverse order
        $tables = [
            'campaigns' => [
                'campaigns_status_updated_at_scheduler_retry_index',
                'campaigns_user_id_status_index',
                'campaigns_created_at_index'
            ],
            'users' => [
                'users_role_index',
                'users_email_verified_at_index'
            ],
            'subscriptions' => [
                'subscriptions_user_id_status_index',
                'subscriptions_plan_id_index',
                'subscriptions_created_at_index'
            ],
            'credit_logs' => [
                'credit_logs_user_id_created_at_index',
                'credit_logs_action_index',
                'credit_logs_action_type_index'
            ],
            'chapters' => [
                'chapters_campaign_id_index',
            ],
            'sections' => [
                'sections_chapter_id_index',
                'sections_campaign_id_index',
            ],
            'tickets' => [
                'tickets_user_id_index',
                'tickets_status_index',
                'tickets_priority_index'
            ],
            'comments' => [
                'comments_ticket_id_created_at_index',
                'comments_user_id_index'
            ]
        ];

        foreach ($tables as $table => $indexes) {
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $table) use ($indexes) {
                    foreach ($indexes as $index) {
                        if ($this->hasIndex($table->getTable(), $index)) {
                            $table->dropIndex($index);
                        }
                    }
                });
            }
        }
    }

    /**
     * Check if an index exists on a table
     */
    private function hasIndex(string $table, string $index): bool
    {
        try {
            $indexes = Schema::getConnection()->getDoctrineSchemaManager()
                ->listTableIndexes($table);
            return array_key_exists($index, $indexes);
        } catch (\Exception $e) {
            return false;
        }
    }
};
