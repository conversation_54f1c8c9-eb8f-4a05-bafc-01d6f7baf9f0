<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ebook_formats', function (Blueprint $table) {
            // Change background_opacity from integer to float
            $table->float('background_opacity', 8, 2)->default(1.0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ebook_formats', function (Blueprint $table) {
            // Change background_opacity back to integer
            $table->integer('background_opacity')->default(70)->change();
        });
    }
};
