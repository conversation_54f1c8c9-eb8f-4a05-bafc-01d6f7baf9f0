<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('status')->default(\App\Enum\CampaignStatusEnum::PENDING->value);
            $table->text('url')->nullable();
            $table->json('form')->nullable();
            $table->json('meta')->nullable();
            $table->integer('limit')->nullable();
            $table->integer('limit_per_day')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('campaigns');
    }
};
