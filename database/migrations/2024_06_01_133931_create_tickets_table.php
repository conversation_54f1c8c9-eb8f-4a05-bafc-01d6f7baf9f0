<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('tickets')) {
            Schema::create('tickets', function (Blueprint $table) {
                $table->id();
                $table->string('identifier', 10)->unique();
                $table->string('ticketable_type')->nullable();
                $table->bigInteger('ticketable_id')->nullable();
                $table->bigInteger('user_id');
                $table->bigInteger('assigned_to_id')->nullable();
                $table->unsignedBigInteger('last_comment_user_id')->nullable();
                $table->string('last_comment_user_name')->nullable();
                $table->tinyInteger('status')->default(0);
                $table->tinyInteger('priority')->default(0);
                $table->string('title');
                $table->text('content');
                $table->unsignedBigInteger('campaign_id')->nullable();

                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
