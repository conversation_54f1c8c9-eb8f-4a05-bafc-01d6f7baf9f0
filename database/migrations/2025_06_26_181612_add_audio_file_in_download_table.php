<?php

use App\Models\Campaign;
use App\Models\Download;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $audioArray = [];
        $campaigns = Campaign::whereNotNull('meta->audio')->get();
        
        foreach ($campaigns as $campaign) {
            $audioFileName = $campaign->meta['audio'];
            $audioArray[] = [
                "type" => 'mp3',
                "path" => 'audio/'.$audioFileName,
                "cover_image" => null,
                "file_version" => 1,
                "user_id" => $campaign->user_id,
                "campaign_id" => $campaign->id,
            ];
        }
        Download::insert($audioArray);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webhooks', function (Blueprint $table) {
            $table->dropColumn('plan_id');
            $table->dropColumn('subscription_id');
        });
    }
};
