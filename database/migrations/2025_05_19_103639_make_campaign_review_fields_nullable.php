<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaign_reviews', function (Blueprint $table) {
            // Make name field nullable
            $table->string('name')->nullable()->change();

            // Make email field nullable
            $table->string('email')->nullable()->change();

            // Make rating field nullable
            $table->integer('rating')->unsigned()->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaign_reviews', function (Blueprint $table) {
           
        });
    }
};
