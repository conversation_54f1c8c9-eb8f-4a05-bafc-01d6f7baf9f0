<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sections', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('chapter_id')->index();
            $table->text('title');
            $table->text('intro')->nullable();
            $table->json('key_points')->nullable();
            $table->longText('body')->nullable();
            $table->integer('section_total_words')->nullable();
            $table->enum('status', ['PENDING', 'IN PROGRESS', 'DONE', 'ERROR', 'FAILED'])->default('PENDING');
            $table->json('meta')->nullable();
            $table->timestamps();

            $table->foreign('chapter_id')->references('id')->on('chapters')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sections');
    }
};
