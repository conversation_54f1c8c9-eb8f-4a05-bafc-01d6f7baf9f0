<?php

namespace Database\Seeders;

use App\Models\RoadmapItem;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class RoadmapItemSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $roadmapItems = array_merge(
            $this->getFeatureRequestItems(),
            $this->getInProgressItems(),
            $this->getCompletedItems(),
            $this->getInfrastructureItems()
        );

        foreach ($roadmapItems as $item) {
            RoadmapItem::create($item);
        }
    }

    /**
     * Get feature request roadmap items
     */
    private function getFeatureRequestItems(): array
    {
        return [
            // Feature Requests - High Priority Items
            [
                'title' => 'Create Crosswords, KidsBooks, Puzzles, Coloring Books Feature',
                'summary' => 'Implement comprehensive book creation tools for educational and entertainment content including crosswords, children\'s books, puzzles, and coloring books.',
                'content' => "## Overview\n\nDevelop specialized content creation tools for:\n\n- **Crossword Puzzles**: Interactive crossword generator with clue management\n- **Children's Books**: Age-appropriate content with illustrations and simple language\n- **Puzzle Books**: Various puzzle types (word search, sudoku, logic puzzles)\n- **Coloring Books**: Line art generation and printable formats\n\n## Features\n\n- Template-based creation system\n- Age-appropriate content filtering\n- Print-ready formatting\n- Interactive digital versions\n- Bulk generation capabilities\n\n## Technical Implementation\n\n- Custom content generators for each type\n- SVG-based illustration system\n- PDF export functionality\n- Template management system",
                'status' => 'planned',
                'priority' => 'high',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addMonths(3),
                'is_published' => true,
                'metadata' => ['complexity' => 'high', 'team_size' => 4, 'estimated_hours' => 320],
            ],
            [
                'title' => 'Publish eBooks on Amazon KDP, Google Books, Apple Books',
                'summary' => 'Direct integration with major publishing platforms for seamless book distribution and publishing.',
                'content' => "## Publishing Platforms\n\n- **Amazon Kindle Direct Publishing (KDP)**\n- **Google Play Books**\n- **Apple Books**\n- **Barnes & Noble Press**\n- **Kobo Writing Life**\n\n## Features\n\n- One-click publishing to multiple platforms\n- Automated metadata management\n- Cover image optimization for each platform\n- Pricing and royalty management\n- Sales analytics integration\n- ISBN assignment and management\n\n## Technical Requirements\n\n- API integrations with publishing platforms\n- Format conversion (EPUB, MOBI, PDF)\n- Metadata standardization\n- Authentication and authorization systems\n- Progress tracking and status updates",
                'status' => 'planned',
                'priority' => 'critical',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(3),
                'is_published' => true,
                'metadata' => ['api_integrations' => ['kdp', 'google_books'], 'estimated_hours' => 240],
            ],
            [
                'title' => 'Implement Lulu Feature',
                'summary' => 'Integration with Lulu self-publishing platform for print-on-demand services and global distribution.',
                'content' => "## Lulu Integration Features\n\n- **Print-on-Demand**: Automatic book printing and shipping\n- **Global Distribution**: Worldwide availability through Lulu's network\n- **Format Support**: Paperback, hardcover, and digital formats\n- **Quality Control**: Automated proofing and quality checks\n\n## Implementation Details\n\n- Lulu API integration for order management\n- Print specification optimization\n- Cover design templates for print formats\n- Shipping and fulfillment tracking\n- Revenue and royalty reporting\n\n## Benefits\n\n- No upfront printing costs\n- Global reach without inventory management\n- Professional print quality\n- Automated fulfillment process",
                'status' => 'planned',
                'priority' => 'medium',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addMonths(2),
                'is_published' => true,
                'metadata' => ['integration_type' => 'api', 'estimated_hours' => 160],
            ],
            // Add-On Features
            [
                'title' => 'Add-On: Kids Books & Animated Storybooks Generator',
                'summary' => 'Specialized generator for children\'s literature with animated elements and interactive features.',
                'content' => "## Kids Books Features\n\n- **Age-Appropriate Content**: Content filtering for different age groups (0-3, 4-7, 8-12)\n- **Interactive Elements**: Clickable objects, sound effects, simple animations\n- **Educational Content**: Learning objectives integration, vocabulary building\n- **Illustration System**: AI-generated child-friendly illustrations\n- **Read-Aloud Feature**: Text-to-speech with character voices\n\n## Animated Storybooks\n\n- **Simple Animations**: Page transitions, character movements\n- **Interactive Storytelling**: Choose-your-own-adventure elements\n- **Multimedia Integration**: Background music, sound effects\n- **Export Formats**: Interactive PDF, EPUB3, web-based reader\n\n## Technical Implementation\n\n- Animation framework integration\n- Audio processing and synchronization\n- Interactive element scripting\n- Child-safe content validation",
                'status' => 'planned',
                'priority' => 'high',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addMonths(1),
                'is_published' => true,
                'metadata' => ['target_audience' => 'children', 'complexity' => 'high', 'estimated_hours' => 280],
            ],
            [
                'title' => 'Add-On: Write Book from Document (PDF, Word, TXT Import)',
                'summary' => 'Import and convert existing documents into structured eBooks with AI enhancement and formatting.',
                'content' => "## Supported Formats\n\n- **PDF Files**: Text extraction with layout preservation\n- **Microsoft Word**: .docx and .doc format support\n- **PLR Content**: Private Label Rights content processing\n- **Plain Text**: .txt file import with smart formatting\n- **Rich Text Format**: .rtf file support\n- **Markdown**: .md file import\n\n## Processing Features\n\n- **Content Analysis**: AI-powered content structure detection\n- **Chapter Detection**: Automatic chapter and section identification\n- **Format Conversion**: Smart formatting and styling application\n- **Content Enhancement**: Grammar correction, style improvement\n- **Duplicate Detection**: Identify and handle duplicate content\n- **Metadata Extraction**: Author, title, and other metadata detection\n\n## AI Enhancement\n\n- Content reorganization suggestions\n- Writing style consistency improvements\n- Missing content identification\n- Table of contents generation",
                'status' => 'planned',
                'priority' => 'high',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(4),
                'is_published' => true,
                'metadata' => ['file_formats' => ['pdf', 'docx', 'txt', 'rtf'], 'estimated_hours' => 200],
            ],
            [
                'title' => 'Short Report / Mini eBook Mode (5-10 Pages)',
                'summary' => 'Streamlined creation mode for short-form content like reports, guides, and mini eBooks.',
                'content' => "## Short Content Features\n\n- **Quick Creation Mode**: Simplified interface for rapid content creation\n- **Template Library**: Pre-designed templates for reports, guides, whitepapers\n- **Focused Content**: 5-10 page limit with concentrated information\n- **Professional Formatting**: Business report styling and layouts\n- **Export Options**: PDF, Word, PowerPoint presentation formats\n\n## Use Cases\n\n- **Business Reports**: Market analysis, project summaries\n- **Educational Guides**: How-to guides, tutorials\n- **Marketing Materials**: Lead magnets, promotional content\n- **Research Summaries**: Academic and industry research\n- **Policy Documents**: Company policies, procedures\n\n## Features\n\n- Rapid content generation (under 30 minutes)\n- Professional templates and styling\n- Charts and graphs integration\n- Executive summary generation\n- Citation and reference management",
                'status' => 'planned',
                'priority' => 'medium',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(5),
                'is_published' => true,
                'metadata' => ['content_length' => 'short', 'target_time' => '30_minutes', 'estimated_hours' => 120],
            ],
            [
                'title' => 'Stripe Integration for Upgrade Functionality',
                'summary' => 'Implement comprehensive payment processing system with Stripe for subscription management and upgrades.',
                'content' => "## Payment Features\n\n- **Subscription Management**: Monthly and annual billing cycles\n- **Plan Upgrades/Downgrades**: Seamless plan transitions with prorated billing\n- **Payment Methods**: Credit cards, digital wallets, bank transfers\n- **International Support**: Multi-currency and regional payment methods\n- **Invoice Generation**: Automated billing and receipt generation\n\n## Stripe Integration\n\n- **Secure Payment Processing**: PCI-compliant payment handling\n- **Webhook Management**: Real-time payment status updates\n- **Customer Portal**: Self-service billing management\n- **Analytics Integration**: Revenue tracking and reporting\n- **Failed Payment Handling**: Automatic retry and dunning management\n\n## Technical Implementation\n\n- Stripe API integration\n- Subscription lifecycle management\n- Payment security and compliance\n- Database synchronization\n- Error handling and logging",
                'status' => 'planned',
                'priority' => 'critical',
                'category' => 'infrastructure',
                'estimated_date' => Carbon::now()->addWeeks(4),
                'is_published' => true,
                'metadata' => ['payment_provider' => 'stripe', 'estimated_hours' => 160],
            ],
            [
                'title' => 'Multi-Platform eBook Publishing',
                'summary' => 'Direct integration with Amazon KDP, Google Books, Apple Books, and other major publishing platforms.',
                'content' => "## Publishing Platforms\n\n- Amazon Kindle Direct Publishing (KDP)\n- Google Play Books\n- Apple Books\n- Barnes & Noble Press\n\n## Key Features\n\n- One-click publishing to multiple platforms\n- Automated metadata management\n- Cover optimization for each platform\n- Sales analytics integration\n\n## Current Progress\n\n- KDP integration: 80% complete\n- Google Books API: In development\n- Metadata standardization: Complete",
                'status' => 'planned',
                'priority' => 'critical',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(4),
                'is_published' => true,
                'metadata' => ['api_integrations' => ['kdp', 'google_books'], 'estimated_hours' => 240],
            ],
            [
                'title' => 'Document Import & Conversion',
                'summary' => 'Import existing documents (PDF, Word, PLR, TXT) and convert them into structured eBooks with AI enhancement.',
                'content' => "## Supported Formats\n\n- PDF files with text extraction\n- Microsoft Word (.docx, .doc)\n- Plain text (.txt) files\n- Rich Text Format (.rtf)\n- PLR content processing\n\n## AI Enhancement\n\n- Content structure detection\n- Chapter identification\n- Grammar and style improvements\n- Metadata extraction\n\n## Processing Features\n\n- Smart formatting application\n- Duplicate content detection\n- Table of contents generation",
                'status' => 'planned',
                'priority' => 'high',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(10),
                'is_published' => true,
                'metadata' => ['file_formats' => ['pdf', 'docx', 'txt', 'rtf'], 'estimated_hours' => 200],
            ],
            [
                'title' => 'AI Cover Design System',
                'summary' => 'Advanced cover design with AI-generated artwork, professional templates, and drag-and-drop customization.',
                'content' => "## Design Features\n\n- AI-generated custom artwork\n- Professional template library\n- Advanced typography system\n- Automatic color palette suggestions\n- Genre-specific designs\n\n## Customization Tools\n\n- Drag-and-drop editor\n- Stock photo integration\n- Brand consistency tools\n- Multi-format optimization\n\n## Technical Implementation\n\n- Canvas-based design editor\n- AI image generation integration\n- Real-time preview functionality",
                'status' => 'planned',
                'priority' => 'high',
                'category' => 'improvements',
                'estimated_date' => Carbon::now()->addWeeks(10),
                'is_published' => true,
                'metadata' => ['feature_type' => 'design', 'estimated_hours' => 180],
            ],
        ];
    }

    /**
     * Get in-progress roadmap items
     */
    private function getInProgressItems(): array
    {
        return [
            [
                'title' => 'Campaign Delete Feature',
                'summary' => 'Implementation of soft delete with 15-day grace period before permanent deletion for campaigns.',
                'content' => "## Delete Process\n\n- Soft delete immediately when user requests deletion\n- Visible in 'Recently Deleted' section for 15 days\n- Automatic hard delete after 15 days\n- Option to restore during grace period\n\n## Features\n\n- Audit trail of deletion/restoration\n- Notification before permanent deletion\n- Bulk restore capability\n- Admin override for immediate hard delete\n\n## Technical Implementation\n\n- Database soft delete flag with timestamp\n- Scheduled job for permanent cleanup\n- Separate UI section for deleted items\n- Backend API endpoints for restore functionality",
                'status' => 'planned',
                'priority' => 'medium',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(1),
                'is_published' => true,
                'metadata' => [
                    'progress' => 80,
                    'feature_type' => 'campaign_management',
                    'estimated_hours' => 40,
                ],
            ],
            [
                'title' => 'Audiobook Accent Selection',
                'summary' => 'Voice accent selection for audiobook narration supporting UK, US, Australian, and other English variants.',
                'content' => "## Supported Accents\n\n- UK English\n- US English\n- Australian English\n- Canadian English\n- Irish and Scottish variants\n\n## Features\n\n- Voice preview before generation\n- Pitch and speed adjustments\n- Consistent accent throughout book\n- Integration with major TTS providers\n\n## Technical Implementation\n\n- Advanced text-to-speech integration\n- Custom voice model training\n- Real-time accent switching",
                'status' => 'in_progress',
                'priority' => 'high',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(1),
                'is_published' => true,
                'metadata' => ['progress' => 70, 'feature_type' => 'audiobook', 'estimated_hours' => 80],
            ],
            
            [
                'title' => 'Custom Domain Sharing',
                'summary' => 'Allow users to share eBooks using custom domains for professional branding and white-label solutions.',
                'content' => "## Domain Features\n\n- DNS-based domain verification\n- Automatic SSL certificate provisioning\n- Subdomain support\n- Custom branding options\n\n## User Experience\n\n- Simple domain setup wizard\n- Real-time verification status\n- Branded sharing links\n- Professional appearance\n\n## Technical Requirements\n\n- Automated DNS management\n- CDN integration\n- SSL/TLS automation\n- Domain health monitoring",
                'status' => 'in_progress',
                'priority' => 'high',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addWeeks(3),
                'is_published' => true,
                'metadata' => ['progress' => 25, 'feature_type' => 'sharing', 'estimated_hours' => 140],
            ],
            
            [
                'title' => 'eBook Conclusion Generator',
                'summary' => 'Automated conclusion generation and professional book wrapping with summaries and call-to-actions.',
                'content' => "## Conclusion Features\n\n- AI-powered chapter summaries\n- Key points extraction\n- Call-to-action integration\n- Professional formatting\n- Customizable templates\n\n## Book Wrapping Elements\n\n- About the author section\n- Additional resources\n- Contact information\n- Copyright notices\n- Acknowledgments\n\n## Quality Assurance\n\n- Content coherence checking\n- Grammar consistency\n- Professional presentation standards",
                'status' => 'in_progress',
                'priority' => 'medium',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addDays(3),
                'is_published' => true,
                'metadata' => ['progress' => 85, 'feature_type' => 'content_generation', 'estimated_hours' => 60],
            ],
        ];
    }

    /**
     * Get completed roadmap items
     */
    private function getCompletedItems(): array
    {
        return [
            [
                'title' => 'Typography System Fixes',
                'summary' => 'Resolved font rendering issues across platforms and improved typography consistency.',
                'content' => "## Issues Resolved\n\n- Cross-platform font compatibility\n- Font licensing compliance\n- Performance optimization\n- Accessibility improvements\n- Format support for PDF and EPUB\n\n## Technical Solutions\n\n- Font subsetting for smaller file sizes\n- Fallback systems for unsupported fonts\n- CDN-based font delivery\n- Custom font upload support\n\n## Results\n\n- Consistent typography across all formats\n- Improved readability\n- Faster loading times\n- Better mobile compatibility",
                'status' => 'completed',
                'priority' => 'medium',
                'category' => 'fixes',
                'estimated_date' => Carbon::now()->subWeeks(3),
                'completed_date' => Carbon::now()->subWeeks(1),
                'is_published' => true,
                'metadata' => ['issue_type' => 'typography', 'platforms_affected' => ['web', 'mobile', 'pdf']],
            ],
        ];
    }

    /**
     * Get infrastructure and improvement roadmap items
     */
    private function getInfrastructureItems(): array
    {
        return [
            [
                'title' => 'Stripe Payment Integration',
                'summary' => 'Complete payment processing system with subscription management and upgrade functionality.',
                'content' => "## Payment Features\n\n- Subscription management (monthly/annual)\n- Plan upgrades and downgrades\n- Multiple payment methods\n- International currency support\n- Automated billing and receipts\n\n## Stripe Integration\n\n- Secure payment processing\n- Webhook management\n- Customer portal\n- Revenue analytics\n- Failed payment handling\n\n## Implementation\n\n- PCI-compliant payment handling\n- Subscription lifecycle management\n- Database synchronization\n- Comprehensive error handling",
                'status' => 'planned',
                'priority' => 'critical',
                'category' => 'infrastructure',
                'estimated_date' => Carbon::now()->addWeeks(6),
                'is_published' => true,
                'metadata' => ['payment_provider' => 'stripe', 'estimated_hours' => 160],
            ],
            [
                'title' => 'Random eBook Idea Generator',
                'summary' => 'Quick content generation tool for inspiration with batch generation of 10 unique eBook concepts.',
                'content' => "## Generation Features\n\n- Diverse topics across multiple genres\n- Batch generation of 10 concepts\n- Customizable parameters (genre, length, audience)\n- Creative prompts and unique angles\n- Instant outline generation\n\n## Content Categories\n\n- Fiction (mystery, romance, sci-fi, fantasy)\n- Non-fiction (how-to, business, educational)\n- Children's content\n- Specialized topics (technical, cookbooks)\n\n## User Experience\n\n- One-click generation\n- Filterable results\n- Save favorite concepts\n- Export summaries and outlines",
                'status' => 'planned',
                'priority' => 'low',
                'category' => 'features',
                'estimated_date' => Carbon::now()->addMonths(6),
                'is_published' => true,
                'metadata' => ['feature_type' => 'content_generation', 'batch_size' => 10, 'estimated_hours' => 100],
            ],
            [
                'title' => 'Enhanced Audio Format Support',
                'summary' => 'Expanded audiobook format support with multiple codecs and quality options for platform compatibility.',
                'content' => "## Supported Formats\n\n- High-quality: FLAC, WAV\n- Compressed: MP3, AAC, OGG\n- Streaming: HLS, DASH\n- Platform-specific: Audible AAX, Apple M4A\n- Multiple bitrate options (64-320 kbps)\n\n## Technical Features\n\n- Advanced audio encoding\n- Quality assessment\n- Batch conversion\n- Metadata embedding\n- Compression optimization\n\n## Platform Integration\n\n- Direct upload to audiobook platforms\n- Format-specific optimization\n- Quality validation\n- Automated conversion workflows",
                'status' => 'planned',
                'priority' => 'medium',
                'category' => 'improvements',
                'estimated_date' => Carbon::now()->addWeeks(8),
                'is_published' => true,
                'metadata' => ['feature_type' => 'audio', 'formats' => ['mp3', 'aac', 'flac', 'wav'], 'estimated_hours' => 120],
            ],
        ];
    }
}
