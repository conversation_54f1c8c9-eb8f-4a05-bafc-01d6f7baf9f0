<?php

namespace Database\Seeders;

use App\Enum\FeatureEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\Plan;

class NewPlanSeeder extends Seeder
{
    public function run(): void
    {
        // Main subscription plans with new addon-based pricing
        $mainPlans = [
            [
                'name' => 'Basic',
                'slug' => Str::slug('Basic'),
                'type' => 'monthly',
                'credits' => 1500,
                'amount' => 9,
                'description' => 'Basic plan with no addons included',
                'permissions' => null,
                'show' => 1,
                'wplus_code' => 'wso_ftlvc7',
                'is_unlimited' => false,
                'page_limit' => 40,
                'includes_all_addons' => false,
                'purchase_url' => 'https://warriorplus.com/o2/buy/bz4trh/jpqdh3/ftlvc7',
                'effective_date' => '2025-07-31',
            ],
            [
                'name' => 'Premium',
                'slug' => Str::slug('Premium'),
                'type' => 'monthly',
                'credits' => 7500,
                'amount' => 29,
                'description' => 'All addons included',
                'permissions' => [
                    'has_everything' => true,
                    'features' => []
                ],
                'show' => 1,
                'wplus_code' => 'wso_njdz56',
                'is_unlimited' => false,
                'page_limit' => 120,
                'includes_all_addons' => true,
                'purchase_url' => 'https://warriorplus.com/o2/buy/bz4trh/jpqdh3/njdz56',
                'effective_date' => '2025-07-31',
            ],
            [
                'name' => 'Pro plus',
                'slug' => Str::slug('Pro plus'),
                'type' => 'monthly',
                'credits' => 20000,
                'amount' => 49,
                'description' => 'All addons included',
                'permissions' => [
                    'has_everything' => true,
                    'features' => []
                ],
                'show' => 1,
                'wplus_code' => 'wso_y2kd8t',
                'is_unlimited' => false,
                'page_limit' => 180,
                'includes_all_addons' => true,
                'purchase_url' => 'https://warriorplus.com/o2/buy/bz4trh/jpqdh3/y2kd8t',
                'effective_date' => '2025-07-31',
            ],
            [
                'name' => 'Unlimited',
                'slug' => Str::slug('Unlimited'),
                'type' => 'monthly',
                'credits' => 99999,
                'amount' => 89,
                'description' => 'All addons included with unlimited pages',
                'permissions' => [
                    'has_everything' => true,
                    'features' => []
                ],
                'show' => 1,
                'wplus_code' => 'wso_m3tryt',
                'is_unlimited' => true,
                'page_limit' => null,
                'includes_all_addons' => true,
                'purchase_url' => 'https://warriorplus.com/o2/buy/bz4trh/jpqdh3/m3tryt',
                'effective_date' => '2025-07-31',
            ],
        ];

        // Update or create main plans
        foreach ($mainPlans as $plan) {
            Plan::updateOrCreate(
                ['slug' => $plan['slug']],
                $plan
            );
        }
    }
}
