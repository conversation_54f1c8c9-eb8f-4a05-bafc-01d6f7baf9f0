<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SiteSettingSeeder extends Seeder
{
    protected string $model = SiteSetting::class;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'name' => 'name',
                'payload' => 'AiEbook',
            ],
            [
                'name' => 'has_top_navigation',
                'payload' => false,
            ],
            [
                'name' => 'is_sidebar_collapse',
                'payload' => false,
            ],
            [
                'name' => 'theme_colors',
                'payload' => [
                    "gray" => "slate",
                    "info" => "amber",
                    "danger" => "red",
                    "primary" => "amber",
                    "success" => "emerald",
                    "warning" => "orange",
                    "secondary" => "slate"
                ],
            ],
            [
                'name' => 'features',
                'payload' => [
                    "medium" => true,
                    "reddit" => false,
                    "wordpress" => true,
                    "api_webhook" => true
                ],
            ],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(['name' => $setting['name']], $setting);
        }
    }
}
