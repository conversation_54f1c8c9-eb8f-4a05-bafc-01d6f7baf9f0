<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $users = [
            [
                'name' => 'Mosharrf Hossain',
                'email' => '<EMAIL>',
                'role' => 'super_admin',
                'password' => 'secret'
            ],
            [
                'name' => 'Mosharrf Hossain',
                'email' => '<EMAIL>',
                'role' => 'super_admin',
                'password' => '^9g4CG%bgr'
            ],
            [
                'name' => 'Mosharrf Hossain',
                'email' => '<EMAIL>',
                'role' => 'user',
                'password' => '^9g4CG%bgr'
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>min',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'password' => 'N!H8h4L6&z'
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'email' => '<EMAIL>',
                'role' => 'user',
                'password' => 'N!H8h4L6&z'
            ],
            [
                'name' => '<PERSON>ihan Mridha Admin',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'password' => 'w!kWZ^8GG9'
            ],
            [
                'name' => 'Salihan Mridha User',
                'email' => '<EMAIL>',
                'role' => 'user',
                'password' => 'w!kWZ^8GG9'
            ],
            [
                'name' => 'Masud Rana',
                'email' => '<EMAIL>',
                'role' => 'user',
                'password' => 'w2u!jU2pkE'
            ],
        ];

        foreach ($users as $user) {
            if (!User::where('email', $user['email'])->exists()) {
                $user = User::create([
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'password' => $user['password'] ?? bcrypt('secret'),
                    'email_verified_at' => now(),
                ]);
            }
        }

        $this->call(SiteSettingSeeder::class);
        $this->call(EbookFormatSeeder::class);
        $this->call(TemplateSeeder::class);
        $this->call(PlanSeeder::class);
    }
}
