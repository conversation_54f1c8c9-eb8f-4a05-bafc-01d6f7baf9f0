<?php

namespace Database\Seeders;

use App\Enum\FeatureEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\Plan;

class PlanSeeder extends Seeder
{
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Starter',
                'slug' => Str::slug('Starter'),
                'type' => 'monthly',
                'credits' => 5000,
                'amount' => 27,
                'description' => '',
                'permissions' => null,
                'show' => 1,
                'wplus_code' => 'wso_r9qctj',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Growth',
                'slug' => Str::slug('Growth'),
                'type' => 'monthly',
                'credits' => 12500,
                'amount' => 47,
                'description' => '',
                'permissions' => null,
                'show' => 1,
                'wplus_code' => 'wso_kllqyz',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Pro',
                'slug' => Str::slug('Pro'),
                'type' => 'monthly',
                'credits' => 25000,
                'amount' => 67,
                'description' => '',
                'permissions' => null,
                'show' => 1,
                'wplus_code' => 'wso_gxtk12',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Agency',
                'slug' => Str::slug('Agency'),
                'type' => 'monthly',
                'credits' => 99999,
                'amount' => 97,
                'description' => '',
                'permissions' => null,
                'show' => 1,
                'wplus_code' => 'wso_hdy3j7',
                'is_unlimited' => true,
            ],
            [
                'name' => 'Research Tools',
                'slug' => Str::slug('Research Tools'),
                'type' => 'monthly',
                'credits' => 0,
                'amount' => 29,
                'description' => '',
                'permissions' => [
                    "has_everything" => false,
                    "features" => [
                        FeatureEnum::RESEARCH_TOOLS->value,
                    ]
                ],
                'show' => 1,
                'wplus_code' => 'wso_wfxght',
                'is_unlimited' => false,
            ],
            [
                'name' => 'YouTube & Link Conversion',
                'slug' => Str::slug('YouTube & Link Conversion'),
                'type' => 'monthly',
                'credits' => 0,
                'amount' => 49,
                'description' => '',
                'permissions' => [
                    "has_everything" => false,
                    "features" => [
                        FeatureEnum::VIDEO_TO_EBOOK->value,
                    ]
                ],
                'show' => 1,
                'wplus_code' => 'wso_k1s7tp',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Flipbook & Lead Collection',
                'slug' => Str::slug('Flipbook & Lead Collection'),
                'type' => 'monthly',
                'credits' => 0,
                'amount' => 67,
                'description' => '',
                'permissions' => [
                    "has_everything" => false,
                    "features" => [
                        FeatureEnum::LEAD_COLLECTION->value,
                    ]
                ],
                'show' => 1,
                'wplus_code' => 'wso_k8q6t6',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Audio Generation',
                'slug' => Str::slug('Audio Generation'),
                'type' => 'monthly',
                'credits' => 0,
                'amount' => 29,
                'description' => '',
                'permissions' => [
                    "has_everything" => false,
                    "features" => [
                        FeatureEnum::AUDIO_BOOK_GENERATION->value,
                    ]
                ],
                'show' => 1,
                'wplus_code' => 'wso_kt6rfr',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Advanced Layout & Background Templates',
                'slug' => Str::slug('Advanced Layout & Background Templates'),
                'type' => 'monthly',
                'credits' => 0,
                'amount' => 29,
                'description' => '',
                'permissions' => [
                    "has_everything" => false,
                    "features" => [
                        FeatureEnum::AUDIO_BOOK_GENERATION->value,
                    ]
                ],
                'show' => 1,
                'wplus_code' => '',
                'is_unlimited' => false,
            ]
        ];

        foreach ($plans as $plan) {
            Plan::updateOrCreate(
                ['slug' => $plan['slug']],
                $plan
            );
        }
    }
}
